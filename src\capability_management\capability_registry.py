"""
能力注册表 - 管理所有能力的注册、版本控制和依赖关系
"""
import asyncio
import logging
import json
import importlib
import sys
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime
import sqlite3
import aiosqlite


@dataclass
class CapabilityInfo:
    """能力信息"""
    name: str
    version: str
    description: str
    module_path: str
    class_name: str
    dependencies: List[str]
    api_info: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    status: str  # active, inactive, error
    performance_metrics: Dict[str, Any]


class CapabilityRegistry:
    """
    能力注册表
    
    负责管理所有能力模块的注册、版本控制、依赖关系和生命周期
    """
    
    def __init__(self, db_path: str = "data/capabilities.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self.loaded_capabilities: Dict[str, Any] = {}
        self.capability_cache: Dict[str, CapabilityInfo] = {}
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """初始化注册表"""
        await self._create_database_schema()
        await self._load_existing_capabilities()
        
    async def _create_database_schema(self):
        """创建数据库模式"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                CREATE TABLE IF NOT EXISTS capabilities (
                    name TEXT PRIMARY KEY,
                    version TEXT NOT NULL,
                    description TEXT,
                    module_path TEXT NOT NULL,
                    class_name TEXT NOT NULL,
                    dependencies TEXT,  -- JSON array
                    api_info TEXT,      -- JSON object
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    performance_metrics TEXT  -- JSON object
                )
            ''')
            
            await db.execute('''
                CREATE TABLE IF NOT EXISTS capability_dependencies (
                    capability_name TEXT,
                    dependency_name TEXT,
                    PRIMARY KEY (capability_name, dependency_name),
                    FOREIGN KEY (capability_name) REFERENCES capabilities (name),
                    FOREIGN KEY (dependency_name) REFERENCES capabilities (name)
                )
            ''')
            
            await db.commit()
            
    async def _load_existing_capabilities(self):
        """加载现有能力"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('SELECT * FROM capabilities WHERE status = "active"') as cursor:
                async for row in cursor:
                    capability_info = self._row_to_capability_info(row)
                    self.capability_cache[capability_info.name] = capability_info
                    
        self.logger.info(f"加载了 {len(self.capability_cache)} 个能力")
        
    def _row_to_capability_info(self, row) -> CapabilityInfo:
        """将数据库行转换为能力信息对象"""
        return CapabilityInfo(
            name=row[0],
            version=row[1],
            description=row[2] or "",
            module_path=row[3],
            class_name=row[4],
            dependencies=json.loads(row[5]) if row[5] else [],
            api_info=json.loads(row[6]) if row[6] else {},
            created_at=datetime.fromisoformat(row[7]),
            updated_at=datetime.fromisoformat(row[8]),
            status=row[9],
            performance_metrics=json.loads(row[10]) if row[10] else {}
        )
        
    async def register_capability(
        self,
        name: str,
        module_code: str,
        api_info: Dict[str, Any] = None,
        dependencies: List[str] = None,
        version: str = "1.0.0"
    ) -> bool:
        """
        注册新能力
        
        Args:
            name: 能力名称
            module_code: 模块代码
            api_info: API信息
            dependencies: 依赖列表
            version: 版本号
            
        Returns:
            注册是否成功
        """
        self.logger.info(f"注册能力: {name}")
        
        try:
            # 1. 保存模块代码到文件
            module_path = await self._save_module_code(name, module_code)
            
            # 2. 验证模块
            class_name = await self._validate_module(name, module_path)
            
            # 3. 检查依赖
            if dependencies:
                await self._validate_dependencies(dependencies)
                
            # 4. 创建能力信息
            capability_info = CapabilityInfo(
                name=name,
                version=version,
                description=f"{name} 能力模块",
                module_path=module_path,
                class_name=class_name,
                dependencies=dependencies or [],
                api_info=api_info or {},
                created_at=datetime.now(),
                updated_at=datetime.now(),
                status="active",
                performance_metrics={}
            )
            
            # 5. 保存到数据库
            await self._save_capability_to_db(capability_info)
            
            # 6. 更新缓存
            self.capability_cache[name] = capability_info
            
            # 7. 热加载模块
            await self._hot_load_capability(name)
            
            self.logger.info(f"成功注册能力: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"注册能力失败 {name}: {e}")
            return False
            
    async def _save_module_code(self, name: str, code: str) -> str:
        """保存模块代码到文件"""
        capabilities_dir = Path("src/capabilities")
        capabilities_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建__init__.py文件
        init_file = capabilities_dir / "__init__.py"
        if not init_file.exists():
            init_file.write_text("# Capabilities Package\n")
            
        module_file = capabilities_dir / f"{name}.py"
        module_file.write_text(code, encoding="utf-8")
        
        return f"src.capabilities.{name}"
        
    async def _validate_module(self, name: str, module_path: str) -> str:
        """验证模块并返回主类名"""
        try:
            # 动态导入模块
            module = importlib.import_module(module_path)
            
            # 查找能力类
            class_name = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    attr_name.endswith('Capability') and
                    attr_name != 'Capability'):
                    class_name = attr_name
                    break
                    
            if not class_name:
                raise ValueError(f"模块 {module_path} 中未找到能力类")
                
            # 验证类是否可以实例化
            capability_class = getattr(module, class_name)
            instance = capability_class()
            
            return class_name
            
        except Exception as e:
            raise ValueError(f"模块验证失败: {e}")
            
    async def _validate_dependencies(self, dependencies: List[str]):
        """验证依赖关系"""
        for dep in dependencies:
            if not await self.has_capability(dep):
                raise ValueError(f"依赖能力不存在: {dep}")
                
    async def _save_capability_to_db(self, capability_info: CapabilityInfo):
        """保存能力信息到数据库"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT OR REPLACE INTO capabilities 
                (name, version, description, module_path, class_name, 
                 dependencies, api_info, created_at, updated_at, status, performance_metrics)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                capability_info.name,
                capability_info.version,
                capability_info.description,
                capability_info.module_path,
                capability_info.class_name,
                json.dumps(capability_info.dependencies),
                json.dumps(capability_info.api_info),
                capability_info.created_at.isoformat(),
                capability_info.updated_at.isoformat(),
                capability_info.status,
                json.dumps(capability_info.performance_metrics)
            ))
            
            # 保存依赖关系
            await db.execute(
                'DELETE FROM capability_dependencies WHERE capability_name = ?',
                (capability_info.name,)
            )
            
            for dep in capability_info.dependencies:
                await db.execute('''
                    INSERT INTO capability_dependencies (capability_name, dependency_name)
                    VALUES (?, ?)
                ''', (capability_info.name, dep))
                
            await db.commit()
            
    async def _hot_load_capability(self, name: str):
        """热加载能力模块"""
        try:
            capability_info = self.capability_cache[name]
            
            # 重新导入模块（如果已存在）
            if capability_info.module_path in sys.modules:
                importlib.reload(sys.modules[capability_info.module_path])
            else:
                importlib.import_module(capability_info.module_path)
                
            self.logger.info(f"热加载能力成功: {name}")
            
        except Exception as e:
            self.logger.error(f"热加载能力失败 {name}: {e}")
            raise
            
    async def has_capability(self, name: str) -> bool:
        """检查是否有指定能力"""
        return name in self.capability_cache
        
    async def get_capability_info(self, name: str) -> Optional[CapabilityInfo]:
        """获取能力信息"""
        return self.capability_cache.get(name)
        
    def list_capabilities(self) -> List[str]:
        """列出所有能力名称"""
        return list(self.capability_cache.keys())
        
    async def get_capability_details(self) -> List[Dict[str, Any]]:
        """获取所有能力的详细信息"""
        return [asdict(info) for info in self.capability_cache.values()]
        
    async def remove_capability(self, name: str) -> bool:
        """移除能力"""
        if name not in self.capability_cache:
            return False
            
        try:
            # 检查是否有其他能力依赖此能力
            dependents = await self._get_dependents(name)
            if dependents:
                raise ValueError(f"无法删除能力 {name}，被以下能力依赖: {dependents}")
                
            # 从数据库删除
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('DELETE FROM capabilities WHERE name = ?', (name,))
                await db.execute('DELETE FROM capability_dependencies WHERE capability_name = ?', (name,))
                await db.commit()
                
            # 从缓存删除
            del self.capability_cache[name]
            
            # 删除模块文件
            module_file = Path(f"src/capabilities/{name}.py")
            if module_file.exists():
                module_file.unlink()
                
            self.logger.info(f"成功删除能力: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除能力失败 {name}: {e}")
            return False
            
    async def _get_dependents(self, name: str) -> List[str]:
        """获取依赖指定能力的其他能力"""
        dependents = []
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                'SELECT capability_name FROM capability_dependencies WHERE dependency_name = ?',
                (name,)
            ) as cursor:
                async for row in cursor:
                    dependents.append(row[0])
        return dependents

    async def update_capability_status(self, name: str, status: str) -> bool:
        """更新能力状态"""
        if name not in self.capability_cache:
            return False

        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    'UPDATE capabilities SET status = ?, updated_at = ? WHERE name = ?',
                    (status, datetime.now().isoformat(), name)
                )
                await db.commit()

            self.capability_cache[name].status = status
            self.capability_cache[name].updated_at = datetime.now()

            self.logger.info(f"更新能力状态: {name} -> {status}")
            return True

        except Exception as e:
            self.logger.error(f"更新能力状态失败 {name}: {e}")
            return False

    async def update_performance_metrics(self, name: str, metrics: Dict[str, Any]) -> bool:
        """更新性能指标"""
        if name not in self.capability_cache:
            return False

        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    'UPDATE capabilities SET performance_metrics = ?, updated_at = ? WHERE name = ?',
                    (json.dumps(metrics), datetime.now().isoformat(), name)
                )
                await db.commit()

            self.capability_cache[name].performance_metrics = metrics
            self.capability_cache[name].updated_at = datetime.now()

            return True

        except Exception as e:
            self.logger.error(f"更新性能指标失败 {name}: {e}")
            return False

    async def get_dependency_graph(self) -> Dict[str, List[str]]:
        """获取依赖关系图"""
        graph = {}
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('SELECT capability_name, dependency_name FROM capability_dependencies') as cursor:
                async for row in cursor:
                    capability, dependency = row
                    if capability not in graph:
                        graph[capability] = []
                    graph[capability].append(dependency)
        return graph

    async def resolve_load_order(self, capabilities: List[str]) -> List[str]:
        """解析加载顺序（拓扑排序）"""
        # 获取依赖图
        dependency_graph = await self.get_dependency_graph()

        # 构建反向图（被依赖关系）
        reverse_graph = {}
        in_degree = {}

        for capability in capabilities:
            in_degree[capability] = 0
            reverse_graph[capability] = []

        for capability in capabilities:
            if capability in dependency_graph:
                for dep in dependency_graph[capability]:
                    if dep in capabilities:
                        reverse_graph[dep].append(capability)
                        in_degree[capability] += 1

        # 拓扑排序
        queue = [cap for cap in capabilities if in_degree[cap] == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            for dependent in reverse_graph[current]:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)

        if len(result) != len(capabilities):
            raise ValueError("检测到循环依赖")

        return result
