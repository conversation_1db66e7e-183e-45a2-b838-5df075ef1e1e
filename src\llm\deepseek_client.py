"""
DeepSeek LLM 客户端
用于集成 DeepSeek API 到自进化智能体系统
"""
import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
import aiohttp


class DeepSeekClient:
    """DeepSeek API 客户端"""
    
    def __init__(self, api_key: str = "sk-969a0919a5b34ca6bfd8df95f4f0ca9c"):
        self.api_key = api_key
        self.base_url = "https://api.deepseek.com/v1"
        self.logger = logging.getLogger(__name__)
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=aiohttp.ClientTimeout(total=60)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用 DeepSeek 聊天完成 API
        
        Args:
            messages: 对话消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            **kwargs: 其他参数
            
        Returns:
            API响应结果
        """
        if not self.session:
            raise RuntimeError("请在异步上下文中使用客户端")
            
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            **kwargs
        }
        
        try:
            self.logger.info(f"调用 DeepSeek API: {model}")
            
            async with self.session.post(
                f"{self.base_url}/chat/completions",
                json=payload
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    self.logger.info("DeepSeek API 调用成功")
                    return result
                else:
                    error_text = await response.text()
                    self.logger.error(f"DeepSeek API 错误 {response.status}: {error_text}")
                    raise Exception(f"API调用失败: {response.status} - {error_text}")
                    
        except Exception as e:
            self.logger.error(f"DeepSeek API 调用异常: {e}")
            raise
            
    async def analyze_task_capabilities(self, task_description: str) -> List[str]:
        """
        使用 LLM 分析任务所需能力
        
        Args:
            task_description: 任务描述
            
        Returns:
            所需能力列表
        """
        messages = [
            {
                "role": "system",
                "content": """你是一个智能任务分析器。分析用户任务描述，识别完成该任务所需的具体能力。

可用能力类型：
- web_search: 网络搜索
- web_scraping: 网页爬虫  
- file_operations: 文件操作
- data_analysis: 数据分析
- image_processing: 图像处理
- email_operations: 邮件操作
- database_operations: 数据库操作
- api_integration: API集成
- natural_language_processing: 自然语言处理
- scheduling: 任务调度
- weather_api: 天气查询
- translation: 翻译服务
- text_generation: 文本生成
- code_generation: 代码生成

请只返回JSON格式的能力列表，例如：["web_search", "file_operations"]"""
            },
            {
                "role": "user", 
                "content": f"任务描述：{task_description}"
            }
        ]
        
        try:
            result = await self.chat_completion(messages, temperature=0.3)
            
            # 提取生成的内容
            content = result["choices"][0]["message"]["content"].strip()
            
            # 尝试解析JSON（处理代码块格式）
            try:
                # 如果内容包含代码块，提取其中的JSON
                if "```json" in content:
                    json_content = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    json_content = content.split("```")[1].strip()
                else:
                    json_content = content.strip()

                capabilities = json.loads(json_content)
                if isinstance(capabilities, list):
                    self.logger.info(f"LLM识别能力: {capabilities}")
                    return capabilities
                else:
                    self.logger.warning(f"LLM返回格式错误: {content}")
                    return []
            except json.JSONDecodeError:
                # 如果不是JSON，尝试从文本中提取
                self.logger.warning(f"LLM返回非JSON格式: {content}")
                return self._extract_capabilities_from_text(content)
                
        except Exception as e:
            self.logger.error(f"LLM能力分析失败: {e}")
            return []
            
    def _extract_capabilities_from_text(self, text: str) -> List[str]:
        """从文本中提取能力关键词"""
        capabilities = []
        capability_keywords = {
            "web_search": ["搜索", "search", "查找", "检索"],
            "file_operations": ["文件", "file", "保存", "读取", "写入"],
            "data_analysis": ["分析", "analysis", "数据", "统计"],
            "image_processing": ["图片", "图像", "image", "处理"],
            "email_operations": ["邮件", "email", "发送"],
            "weather_api": ["天气", "weather", "气温"],
            "translation": ["翻译", "translate", "转换"],
            "natural_language_processing": ["文本", "语言", "nlp"]
        }
        
        text_lower = text.lower()
        for capability, keywords in capability_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                capabilities.append(capability)
                
        return capabilities
        
    async def generate_api_implementation(
        self, 
        capability_name: str, 
        api_info: Dict[str, Any]
    ) -> str:
        """
        使用 LLM 生成 API 实现代码
        
        Args:
            capability_name: 能力名称
            api_info: API信息
            
        Returns:
            生成的Python代码
        """
        # 根据能力类型确定主要方法名
        method_mapping = {
            "web_search": "search_web",
            "weather_api": "get_weather",
            "news_api": "search_news",
            "translation": "translate_text",
            "email_operations": "send_email",
            "file_operations": "process_file",
            "data_analysis": "analyze_data",
            "image_processing": "process_image"
        }

        main_method = method_mapping.get(capability_name, f"{capability_name}_action")

        messages = [
            {
                "role": "system",
                "content": f"""你是一个专业的Python代码生成器。根据API文档生成完整的Python类实现。

要求：
1. 生成完整的Python类，类名为 {capability_name.title().replace('_', '')}Capability
2. 包含 __init__ 方法和主要方法 {main_method}
3. 使用 aiohttp 进行异步HTTP请求
4. 包含完整的错误处理
5. 添加详细的日志记录
6. 代码要符合Python规范
7. 只返回Python代码，不要其他解释

代码模板结构：
```python
import asyncio
import logging
import aiohttp
from typing import Dict, Any

class {capability_name.title().replace('_', '')}Capability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 初始化代码

    async def {main_method}(self, context: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        # 使用 **kwargs 接收所有参数，如 query, text 等
        # 实现代码
        pass
```

注意：方法必须使用 **kwargs 来接收参数，这样可以接收任意命名的参数。"""
            },
            {
                "role": "user",
                "content": f"""能力名称: {capability_name}
API信息: {json.dumps(api_info, ensure_ascii=False, indent=2)}

请生成完整的Python实现代码。"""
            }
        ]
        
        try:
            result = await self.chat_completion(
                messages, 
                temperature=0.2,
                max_tokens=3000
            )
            
            content = result["choices"][0]["message"]["content"].strip()
            
            # 提取代码块
            if "```python" in content:
                code = content.split("```python")[1].split("```")[0].strip()
            elif "```" in content:
                code = content.split("```")[1].strip()
            else:
                code = content
                
            self.logger.info(f"LLM生成代码成功: {capability_name}")
            return code
            
        except Exception as e:
            self.logger.error(f"LLM代码生成失败: {e}")
            raise
            
    async def optimize_execution_strategy(
        self, 
        task_description: str,
        available_capabilities: List[str],
        execution_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        使用 LLM 优化执行策略
        
        Args:
            task_description: 任务描述
            available_capabilities: 可用能力
            execution_history: 执行历史
            
        Returns:
            优化建议
        """
        messages = [
            {
                "role": "system", 
                "content": """你是一个智能执行策略优化器。分析任务、可用能力和历史执行情况，提供最优的执行策略。

请返回JSON格式的优化建议：
{
    "recommended_sequence": ["capability1", "capability2"],
    "optimization_tips": ["建议1", "建议2"],
    "risk_assessment": "风险评估",
    "success_probability": 0.85
}"""
            },
            {
                "role": "user",
                "content": f"""任务: {task_description}
可用能力: {available_capabilities}
执行历史: {json.dumps(execution_history, ensure_ascii=False, indent=2)}

请提供优化建议。"""
            }
        ]
        
        try:
            result = await self.chat_completion(messages, temperature=0.4)
            content = result["choices"][0]["message"]["content"].strip()
            
            try:
                optimization = json.loads(content)
                self.logger.info("LLM策略优化成功")
                return optimization
            except json.JSONDecodeError:
                self.logger.warning(f"LLM返回非JSON格式: {content}")
                return {
                    "recommended_sequence": available_capabilities,
                    "optimization_tips": ["使用默认执行顺序"],
                    "risk_assessment": "中等风险",
                    "success_probability": 0.7
                }
                
        except Exception as e:
            self.logger.error(f"LLM策略优化失败: {e}")
            return {
                "recommended_sequence": available_capabilities,
                "optimization_tips": ["执行策略优化失败，使用默认策略"],
                "risk_assessment": "未知风险",
                "success_probability": 0.5
            }


# 全局客户端实例
_deepseek_client = None

async def get_deepseek_client() -> DeepSeekClient:
    """获取全局 DeepSeek 客户端实例"""
    global _deepseek_client
    if _deepseek_client is None:
        _deepseek_client = DeepSeekClient()
    return _deepseek_client
