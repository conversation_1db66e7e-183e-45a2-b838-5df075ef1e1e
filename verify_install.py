#!/usr/bin/env python3
"""
验证安装是否成功
"""
import sys
import importlib
from pathlib import Path


def test_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        # 测试标准库
        import asyncio
        import json
        import logging
        print("[OK] 标准库导入成功")
        
        # 测试第三方库
        import jinja2
        import requests
        import aiosqlite
        import yaml
        import watchdog
        print("[OK] 第三方库导入成功")
        
        # 测试项目模块
        sys.path.append(str(Path(__file__).parent))
        
        from src.core.capability_discoverer import CapabilityDiscoverer
        print("[OK] 能力发现器导入成功")
        
        from src.code_generation.code_generator import CodeGenerator
        print("[OK] 代码生成器导入成功")
        
        from src.capability_management.capability_registry import CapabilityRegistry
        print("[OK] 能力注册器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"[ERROR] 导入失败: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] 其他错误: {e}")
        return False


def test_directories():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = [
        "src",
        "src/core",
        "src/capabilities", 
        "src/capability_management",
        "src/code_generation",
        "src/learning",
        "data",
        "logs"
    ]
    
    all_exist = True
    for directory in required_dirs:
        path = Path(directory)
        if path.exists():
            print(f"[OK] {directory}")
        else:
            print(f"[MISSING] {directory}")
            all_exist = False
            
    return all_exist


def test_files():
    """测试关键文件"""
    print("\n测试关键文件...")
    
    required_files = [
        "src/core/agent.py",
        "src/core/capability_discoverer.py",
        "src/code_generation/code_generator.py",
        "src/capability_management/capability_registry.py",
        "main.py",
        "run_demo.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"[OK] {file_path}")
        else:
            print(f"[MISSING] {file_path}")
            all_exist = False
            
    return all_exist


def main():
    """主验证函数"""
    print("=" * 50)
    print("自进化智能体安装验证")
    print("=" * 50)
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试目录
    dirs_ok = test_directories()
    
    # 测试文件
    files_ok = test_files()
    
    print("\n" + "=" * 50)
    if imports_ok and dirs_ok and files_ok:
        print("[SUCCESS] 安装验证通过!")
        print("\n下一步:")
        print("1. 运行演示: python run_demo.py --mode demo")
        print("2. 交互模式: python run_demo.py --mode interactive")
        print("3. 运行测试: python run_demo.py --mode test")
    else:
        print("[FAILED] 安装验证失败!")
        if not imports_ok:
            print("- 请检查依赖安装")
        if not dirs_ok:
            print("- 请检查目录结构")
        if not files_ok:
            print("- 请检查项目文件")
    print("=" * 50)


if __name__ == "__main__":
    main()
