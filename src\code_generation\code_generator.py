"""
动态代码生成器 - 基于API文档自动生成功能模块
"""
import logging
import json
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
import ast
import inspect


class CodeGenerator:
    """
    动态代码生成器
    
    根据用户提供的API文档，自动生成对应的功能模块代码
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.template_env = self._setup_template_environment()
        
    def _setup_template_environment(self) -> Environment:
        """设置模板环境"""
        # 创建模板目录
        template_dir = Path("src/code_generation/templates")
        template_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建基础模板文件
        self._create_base_templates(template_dir)
        
        return Environment(loader=FileSystemLoader(str(template_dir)))
        
    def _create_base_templates(self, template_dir: Path):
        """创建基础模板文件"""
        # 能力模块模板
        capability_template = '''"""
{{ capability_name }} 能力模块
自动生成于: {{ timestamp }}
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
{% for import_stmt in imports %}
{{ import_stmt }}
{% endfor %}


class {{ class_name }}:
    """
    {{ capability_description }}
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        {% for init_line in initialization %}
        {{ init_line }}
        {% endfor %}
        
    {% for method in methods %}
    async def {{ method.name }}(self, {% for param in method.params %}{{ param.name }}: {{ param.type }}{% if param.default %} = {{ param.default }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}, context: Dict[str, Any] = None) -> {{ method.return_type }}:
        """
        {{ method.description }}
        
        Args:
            {% for param in method.params %}
            {{ param.name }}: {{ param.description }}
            {% endfor %}
            context: 执行上下文
            
        Returns:
            {{ method.return_description }}
        """
        self.logger.info(f"执行 {{ method.name }}")
        
        try:
            {% for line in method.implementation %}
            {{ line }}
            {% endfor %}
            
        except Exception as e:
            self.logger.error(f"{{ method.name }} 执行失败: {e}")
            raise
            
    {% endfor %}
'''
        
        with open(template_dir / "capability_module.py.j2", "w", encoding="utf-8") as f:
            f.write(capability_template)
            
    async def generate_capability_module(
        self,
        capability_name: str,
        api_info: Dict[str, Any]
    ) -> str:
        """
        生成能力模块代码
        
        Args:
            capability_name: 能力名称
            api_info: API信息
            
        Returns:
            生成的模块代码
        """
        self.logger.info(f"生成能力模块: {capability_name}")
        
        try:
            # 解析API信息
            parsed_info = await self._parse_api_info(api_info)
            
            # 生成模块结构
            module_structure = await self._create_module_structure(
                capability_name, parsed_info
            )
            
            # 使用模板生成代码
            code = await self._generate_code_from_template(module_structure)
            
            # 验证生成的代码
            await self._validate_generated_code(code)
            
            self.logger.info(f"成功生成能力模块: {capability_name}")
            return code
            
        except Exception as e:
            self.logger.error(f"生成能力模块失败: {e}")
            raise
            
    async def _parse_api_info(self, api_info: Dict[str, Any]) -> Dict[str, Any]:
        """解析API信息"""
        parsed = {
            "api_type": api_info.get("type", "rest"),
            "base_url": api_info.get("base_url", ""),
            "authentication": api_info.get("authentication", {}),
            "endpoints": [],
            "dependencies": []
        }
        
        # 解析端点信息
        if "endpoints" in api_info:
            for endpoint in api_info["endpoints"]:
                parsed_endpoint = {
                    "name": endpoint.get("name", ""),
                    "method": endpoint.get("method", "GET"),
                    "path": endpoint.get("path", ""),
                    "description": endpoint.get("description", ""),
                    "parameters": endpoint.get("parameters", []),
                    "response": endpoint.get("response", {})
                }
                parsed["endpoints"].append(parsed_endpoint)
                
        # 解析依赖
        if "dependencies" in api_info:
            parsed["dependencies"] = api_info["dependencies"]
            
        return parsed
        
    async def _create_module_structure(
        self,
        capability_name: str,
        parsed_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建模块结构"""
        from datetime import datetime
        
        class_name = self._generate_class_name(capability_name)
        
        structure = {
            "capability_name": capability_name,
            "class_name": class_name,
            "capability_description": f"{capability_name} 功能模块",
            "timestamp": datetime.now().isoformat(),
            "imports": self._generate_imports(parsed_info),
            "initialization": self._generate_initialization(parsed_info),
            "methods": []
        }
        
        # 为每个端点生成方法
        for endpoint in parsed_info.get("endpoints", []):
            method = await self._create_method_from_endpoint(endpoint, parsed_info)
            structure["methods"].append(method)
            
        # 如果没有端点，生成通用方法
        if not structure["methods"]:
            structure["methods"].append(await self._create_generic_method(capability_name))
            
        return structure
        
    def _generate_class_name(self, capability_name: str) -> str:
        """生成类名"""
        words = capability_name.split('_')
        class_name = ''.join(word.capitalize() for word in words) + 'Capability'
        return class_name
        
    def _generate_imports(self, parsed_info: Dict[str, Any]) -> List[str]:
        """生成导入语句"""
        imports = []
        
        api_type = parsed_info.get("api_type", "rest")
        
        if api_type == "rest":
            imports.append("import aiohttp")
            imports.append("import json")
            
        # 添加依赖导入
        for dep in parsed_info.get("dependencies", []):
            imports.append(f"import {dep}")
            
        return imports
        
    def _generate_initialization(self, parsed_info: Dict[str, Any]) -> List[str]:
        """生成初始化代码"""
        init_lines = []
        
        # 设置基础URL
        if parsed_info.get("base_url"):
            init_lines.append(f'self.base_url = "{parsed_info["base_url"]}"')
            
        # 设置认证信息
        auth = parsed_info.get("authentication", {})
        if auth:
            if auth.get("type") == "api_key":
                init_lines.append(f'self.api_key = "{auth.get("key", "")}"')
            elif auth.get("type") == "bearer":
                init_lines.append(f'self.token = "{auth.get("token", "")}"')
                
        # 创建HTTP会话
        if parsed_info.get("api_type") == "rest":
            init_lines.append("self.session = None")
            
        return init_lines
        
    async def _create_method_from_endpoint(
        self,
        endpoint: Dict[str, Any],
        parsed_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """从端点创建方法"""
        method_name = self._sanitize_method_name(endpoint.get("name", "execute"))
        
        # 生成参数
        params = []
        for param in endpoint.get("parameters", []):
            param_info = {
                "name": param.get("name", "param"),
                "type": param.get("type", "str"),
                "description": param.get("description", ""),
                "default": param.get("default")
            }
            params.append(param_info)
            
        # 生成实现代码
        implementation = await self._generate_method_implementation(endpoint, parsed_info)
        
        return {
            "name": method_name,
            "description": endpoint.get("description", f"执行 {method_name}"),
            "params": params,
            "return_type": "Dict[str, Any]",
            "return_description": "API响应结果",
            "implementation": implementation
        }
        
    async def _create_generic_method(self, capability_name: str) -> Dict[str, Any]:
        """创建通用方法"""
        return {
            "name": "execute",
            "description": f"执行 {capability_name} 功能",
            "params": [
                {
                    "name": "params",
                    "type": "Dict[str, Any]",
                    "description": "执行参数",
                    "default": "{}"
                }
            ],
            "return_type": "Dict[str, Any]",
            "return_description": "执行结果",
            "implementation": [
                "# TODO: 实现具体功能",
                'self.logger.info(f"执行参数: {params}")',
                'return {"status": "success", "message": "功能执行完成"}'
            ]
        }
        
    async def _generate_method_implementation(
        self,
        endpoint: Dict[str, Any],
        parsed_info: Dict[str, Any]
    ) -> List[str]:
        """生成方法实现代码"""
        implementation = []
        
        api_type = parsed_info.get("api_type", "rest")
        
        if api_type == "rest":
            # REST API实现
            implementation.extend([
                "if not self.session:",
                "    self.session = aiohttp.ClientSession()",
                "",
                f'url = self.base_url + "{endpoint.get("path", "")}"',
                f'method = "{endpoint.get("method", "GET")}"',
                "",
                "headers = {}",
                "if hasattr(self, 'api_key'):",
                "    headers['Authorization'] = f'Bearer {self.api_key}'",
                "",
                "async with self.session.request(method, url, headers=headers) as response:",
                "    if response.status == 200:",
                "        result = await response.json()",
                "        return result",
                "    else:",
                "        raise Exception(f'API请求失败: {response.status}')"
            ])
        else:
            # 通用实现
            implementation.extend([
                "# TODO: 实现API调用逻辑",
                'return {"status": "success", "data": "API调用结果"}'
            ])
            
        return implementation
        
    def _sanitize_method_name(self, name: str) -> str:
        """清理方法名"""
        # 移除特殊字符，转换为有效的Python标识符
        name = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        name = re.sub(r'^[0-9]', '_', name)  # 不能以数字开头
        return name.lower()
        
    async def _generate_code_from_template(self, structure: Dict[str, Any]) -> str:
        """使用模板生成代码"""
        template = self.template_env.get_template("capability_module.py.j2")
        code = template.render(**structure)
        return code
        
    async def _validate_generated_code(self, code: str):
        """验证生成的代码"""
        try:
            # 语法检查
            ast.parse(code)
            self.logger.info("生成的代码语法正确")
        except SyntaxError as e:
            self.logger.error(f"生成的代码语法错误: {e}")
            raise ValueError(f"生成的代码语法错误: {e}")
            
    async def generate_test_code(
        self,
        capability_name: str,
        module_code: str
    ) -> str:
        """生成测试代码"""
        class_name = self._generate_class_name(capability_name)

        test_code = f'''"""
{capability_name} 能力模块测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch
from src.capabilities.{capability_name} import {class_name}


class Test{class_name}:
    """测试 {class_name} 类"""

    def setup_method(self):
        """测试前设置"""
        self.capability = {class_name}()

    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        assert self.capability is not None
        assert hasattr(self.capability, 'logger')

    @pytest.mark.asyncio
    async def test_execute_method(self):
        """测试执行方法"""
        # TODO: 添加具体的测试用例
        result = await self.capability.execute({{}})
        assert result is not None
        assert isinstance(result, dict)
'''

        return test_code

    def get_supported_api_types(self) -> List[str]:
        """获取支持的API类型"""
        return ["rest", "graphql", "websocket", "grpc"]

    async def analyze_api_documentation(self, doc_content: str) -> Dict[str, Any]:
        """分析API文档内容"""
        # 简化的文档分析逻辑
        # 实际应用中可以使用更复杂的NLP技术

        analysis = {
            "api_type": "rest",
            "endpoints": [],
            "authentication": {},
            "base_url": "",
            "dependencies": []
        }

        # 检测API类型
        if "graphql" in doc_content.lower():
            analysis["api_type"] = "graphql"
        elif "websocket" in doc_content.lower():
            analysis["api_type"] = "websocket"
        elif "grpc" in doc_content.lower():
            analysis["api_type"] = "grpc"

        # 提取基础URL
        import re
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, doc_content)
        if urls:
            analysis["base_url"] = urls[0]

        return analysis
