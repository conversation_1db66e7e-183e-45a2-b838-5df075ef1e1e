"""
自进化智能体主程序
"""
import asyncio
import logging
import json
from typing import Dict, Any, Optional
from pathlib import Path

from src.core.agent import SelfEvolvingAgent
from src.core.hot_reload import HotReloader


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/agent.log'),
        logging.StreamHandler()
    ]
)

# 确保日志目录存在
Path('logs').mkdir(exist_ok=True)


class AgentInterface:
    """智能体交互接口"""
    
    def __init__(self):
        self.agent = SelfEvolvingAgent()
        self.hot_reloader = None
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化智能体"""
        self.logger.info("初始化自进化智能体...")
        
        # 初始化各个组件
        await self.agent.capability_registry.initialize()
        await self.agent.experience_recorder.initialize()
        
        # 设置用户交互回调
        self.agent.set_user_interaction_callback(self.handle_user_interaction)
        
        # 初始化热更新
        self.hot_reloader = HotReloader(
            self.agent.capability_registry,
            self.agent.task_executor
        )
        await self.hot_reloader.start_monitoring()
        
        self.logger.info("智能体初始化完成")
        
    async def handle_user_interaction(self, interaction_type: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理用户交互
        
        Args:
            interaction_type: 交互类型
            data: 交互数据
            
        Returns:
            用户响应
        """
        if interaction_type == "capability_request":
            return await self.handle_capability_request(data)
        else:
            self.logger.warning(f"未知交互类型: {interaction_type}")
            return None
            
    async def handle_capability_request(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理能力请求"""
        capability = data.get("capability")
        message = data.get("message")
        
        print(f"\n🤖 智能体请求: {message}")
        print(f"需要能力: {capability}")
        print("\n请提供API信息 (JSON格式)，或输入 'skip' 跳过:")
        
        try:
            user_input = input("> ").strip()
            
            if user_input.lower() == 'skip':
                return None
                
            # 尝试解析JSON
            api_info = json.loads(user_input)
            
            print(f"✅ 收到API信息，正在生成 {capability} 能力...")
            return api_info
            
        except json.JSONDecodeError:
            print("❌ JSON格式错误，跳过此能力")
            return None
        except KeyboardInterrupt:
            print("\n用户中断")
            return None
            
    async def run_interactive_mode(self):
        """运行交互模式"""
        print("🚀 自进化智能体已启动!")
        print("输入任务描述，智能体将尝试完成任务")
        print("如果缺少能力，智能体会请求您提供API信息")
        print("输入 'quit' 退出，'status' 查看状态，'help' 查看帮助\n")
        
        while True:
            try:
                user_input = input("📝 请输入任务: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'status':
                    await self.show_status()
                    continue
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                elif not user_input:
                    continue
                    
                # 执行任务
                print(f"\n🔄 开始执行任务: {user_input}")
                result = await self.agent.execute_task(user_input)
                
                if result["status"] == "success":
                    print(f"✅ 任务完成: {result.get('result', '无返回结果')}")
                else:
                    print(f"❌ 任务失败: {result.get('error', '未知错误')}")
                    
                print("-" * 50)
                
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                
    async def show_status(self):
        """显示智能体状态"""
        print("\n📊 智能体状态:")
        
        # 可用能力
        capabilities = self.agent.get_available_capabilities()
        print(f"可用能力: {len(capabilities)} 个")
        for cap in capabilities:
            print(f"  - {cap}")
            
        # 任务历史
        history = self.agent.get_task_history()
        print(f"\n任务历史: {len(history)} 个")
        for task in history[-3:]:  # 显示最近3个任务
            status_emoji = "✅" if task["status"] == "completed" else "❌"
            print(f"  {status_emoji} {task['description'][:50]}...")
            
        # 学习洞察
        insights = await self.agent.experience_recorder.get_learning_insights()
        print(f"\n学习洞察:")
        print(f"  任务成功率: {insights['task_success_rate']:.1%}")
        
        missing_caps = insights['most_requested_capabilities']
        if missing_caps:
            print(f"  最需要的能力: {missing_caps[0]['capability']} (请求{missing_caps[0]['request_count']}次)")
            
        print()
        
    def show_help(self):
        """显示帮助信息"""
        print("""
📖 帮助信息:

基本命令:
  quit    - 退出程序
  status  - 查看智能体状态
  help    - 显示此帮助

任务示例:
  "搜索最新的AI新闻"
  "分析这个文件的数据"
  "发送邮件给客户"
  "处理这张图片"

API信息格式示例:
{
  "type": "rest",
  "base_url": "https://api.example.com",
  "authentication": {
    "type": "api_key",
    "key": "your-api-key"
  },
  "endpoints": [
    {
      "name": "search",
      "method": "GET",
      "path": "/search",
      "description": "搜索功能",
      "parameters": [
        {"name": "query", "type": "str", "description": "搜索关键词"}
      ]
    }
  ]
}
""")
        
    async def cleanup(self):
        """清理资源"""
        if self.hot_reloader:
            self.hot_reloader.stop_monitoring()
        self.logger.info("智能体已关闭")


async def main():
    """主函数"""
    interface = AgentInterface()
    
    try:
        await interface.initialize()
        await interface.run_interactive_mode()
    finally:
        await interface.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
