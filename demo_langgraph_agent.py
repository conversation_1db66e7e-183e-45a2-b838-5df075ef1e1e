"""
LangGraph Agent 演示
展示基于LangGraph的自进化智能体功能
"""
import asyncio
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 导入LangGraph Agent
from src.langgraph_agent import SelfEvolvingAgentGraph, TaskStatus


async def demo_langgraph_agent():
    """演示LangGraph Agent功能"""
    print("=" * 80)
    print("🤖 LangGraph 自进化智能体演示")
    print("=" * 80)
    
    try:
        # 1. 创建Agent
        print("\n🚀 步骤1: 初始化LangGraph Agent...")
        
        # 使用DeepSeek API密钥（如果有的话）
        deepseek_api_key = "***********************************"  # 从记忆中获取
        agent = SelfEvolvingAgentGraph(deepseek_api_key=deepseek_api_key)
        
        print("✅ LangGraph Agent初始化完成")
        
        # 2. 显示工作流图
        print("\n📊 步骤2: 工作流程图...")
        mermaid_diagram = await agent.get_graph_visualization()
        print("🔄 Agent工作流程:")
        print(mermaid_diagram)
        
        # 3. 演示任务处理
        test_tasks = [
            {
                "name": "PDF文档分析",
                "task": "分析PDF文档，提取文本内容和元数据信息",
                "expected_capabilities": ["pdf_processing"]
            },
            {
                "name": "网络搜索",
                "task": "搜索最新的人工智能技术新闻",
                "expected_capabilities": ["web_search"]
            },
            {
                "name": "天气查询",
                "task": "查询北京今天的天气情况",
                "expected_capabilities": ["weather_api"]
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_tasks, 1):
            print(f"\n🎯 步骤{i+2}: 处理任务 - {test_case['name']}")
            print(f"   任务描述: {test_case['task']}")
            
            # 处理任务
            result = await agent.process_task(
                user_task=test_case['task'],
                task_id=f"demo_task_{i}"
            )
            
            results.append({
                "task_name": test_case['name'],
                "result": result
            })
            
            # 显示结果
            print(f"📊 执行结果:")
            print(f"   - 成功: {'✅' if result['success'] else '❌'}")
            print(f"   - 执行时间: {result['execution_time']:.2f}s")
            print(f"   - 总步骤: {result['step_count']}")
            print(f"   - 生成能力: {len(result.get('generated_capabilities', []))}")
            
            if result.get('generated_capabilities'):
                print(f"   - 新增能力: {', '.join(result['generated_capabilities'])}")
            
            if result.get('error_messages'):
                print(f"   - 错误信息: {len(result['error_messages'])} 个错误")
            
            print(f"   - 最终响应: {result.get('final_response', '无响应')}")
        
        # 4. 性能分析
        print(f"\n📈 步骤{len(test_tasks)+3}: 性能分析...")
        
        total_time = sum(r['result']['execution_time'] for r in results)
        success_count = sum(1 for r in results if r['result']['success'])
        total_capabilities = sum(len(r['result'].get('generated_capabilities', [])) for r in results)
        
        print(f"📊 总体性能:")
        print(f"   - 任务总数: {len(test_tasks)}")
        print(f"   - 成功任务: {success_count}")
        print(f"   - 成功率: {success_count/len(test_tasks)*100:.1f}%")
        print(f"   - 总执行时间: {total_time:.2f}s")
        print(f"   - 平均执行时间: {total_time/len(test_tasks):.2f}s")
        print(f"   - 生成能力总数: {total_capabilities}")
        
        # 5. 保存详细结果
        print(f"\n💾 步骤{len(test_tasks)+4}: 保存执行结果...")
        
        detailed_results = {
            "demo_info": {
                "agent_type": "LangGraph自进化智能体",
                "demo_time": asyncio.get_event_loop().time(),
                "deepseek_api_used": bool(deepseek_api_key)
            },
            "performance_summary": {
                "total_tasks": len(test_tasks),
                "successful_tasks": success_count,
                "success_rate": success_count/len(test_tasks),
                "total_execution_time": total_time,
                "average_execution_time": total_time/len(test_tasks),
                "total_generated_capabilities": total_capabilities
            },
            "task_results": results,
            "workflow_diagram": mermaid_diagram
        }
        
        # 保存到文件
        output_file = Path("langgraph_agent_demo_results.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 结果已保存到: {output_file}")
        
        # 6. LangGraph特性展示
        print(f"\n🌟 步骤{len(test_tasks)+5}: LangGraph特性展示...")
        
        print("🎯 LangGraph Agent 优势:")
        print("   ✅ 状态管理: 完整的状态跟踪和历史记录")
        print("   ✅ 流程控制: 条件分支和循环控制")
        print("   ✅ 检查点: 支持任务暂停和恢复")
        print("   ✅ 可视化: 工作流程图自动生成")
        print("   ✅ 调试友好: 详细的步骤跟踪")
        print("   ✅ 扩展性: 易于添加新节点和边")
        
        print("\n🔄 与传统Agent对比:")
        print("   📈 更好的状态管理")
        print("   📈 更清晰的流程控制")
        print("   📈 更强的可观测性")
        print("   📈 更容易的调试和优化")
        print("   📈 更好的错误处理和恢复")
        
        print("\n" + "=" * 80)
        print("🎉 LangGraph Agent演示完成！")
        print("💡 LangGraph提供了更强大的Agent构建能力")
        print("=" * 80)
        
        return detailed_results
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }


async def demo_graph_features():
    """演示LangGraph特有功能"""
    print("\n" + "=" * 80)
    print("🔧 LangGraph 特有功能演示")
    print("=" * 80)
    
    try:
        agent = SelfEvolvingAgentGraph()
        
        # 1. 状态历史
        print("\n📚 功能1: 状态历史跟踪")
        task_id = "demo_history_task"
        
        result = await agent.process_task(
            "测试状态历史功能",
            task_id=task_id
        )
        
        print(f"✅ 任务执行完成，任务ID: {task_id}")
        
        # 获取状态历史
        history = agent.get_state_history(task_id)
        print(f"📊 状态历史记录: {len(history)} 个状态")
        
        # 2. 任务恢复
        print("\n🔄 功能2: 任务恢复")
        resume_result = await agent.resume_task(task_id)
        print(f"恢复结果: {resume_result}")
        
        # 3. 图可视化
        print("\n🎨 功能3: 工作流可视化")
        visualization = await agent.get_graph_visualization()
        print("📊 工作流图已生成")
        
        print("\n✅ LangGraph特有功能演示完成")
        
    except Exception as e:
        print(f"❌ 特有功能演示失败: {e}")


async def main():
    """主演示函数"""
    print("🚀 启动LangGraph Agent完整演示")
    
    # 主要演示
    main_result = await demo_langgraph_agent()
    
    # 特有功能演示
    await demo_graph_features()
    
    print(f"\n🎯 演示总结:")
    if isinstance(main_result, dict) and main_result.get('performance_summary'):
        summary = main_result['performance_summary']
        print(f"   - 处理任务: {summary['total_tasks']}")
        print(f"   - 成功率: {summary['success_rate']*100:.1f}%")
        print(f"   - 平均耗时: {summary['average_execution_time']:.2f}s")
        print(f"   - 生成能力: {summary['total_generated_capabilities']}")
    
    print("\n🌟 LangGraph Agent 已准备就绪！")
    print("💡 可以开始使用基于LangGraph的自进化智能体了")


if __name__ == "__main__":
    asyncio.run(main())
