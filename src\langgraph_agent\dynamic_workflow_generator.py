"""
动态工作流生成器
根据任务目标自动生成LangGraph工作流和优化prompt
"""
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .agent_state import AgentState, TaskStatus


class WorkflowPattern(Enum):
    """工作流模式枚举"""
    LINEAR = "linear"  # 线性流程
    PARALLEL = "parallel"  # 并行处理
    CONDITIONAL = "conditional"  # 条件分支
    ITERATIVE = "iterative"  # 迭代循环
    HYBRID = "hybrid"  # 混合模式


class TaskComplexity(Enum):
    """任务复杂度枚举"""
    SIMPLE = "simple"  # 简单任务
    MEDIUM = "medium"  # 中等复杂度
    COMPLEX = "complex"  # 复杂任务
    EXPERT = "expert"  # 专家级任务


@dataclass
class WorkflowNode:
    """工作流节点定义"""
    name: str
    node_type: str
    description: str
    dependencies: List[str] = field(default_factory=list)
    conditions: Dict[str, Any] = field(default_factory=dict)
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1
    parallel_group: Optional[str] = None
    retry_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WorkflowEdge:
    """工作流边定义"""
    from_node: str
    to_node: str
    condition: Optional[str] = None
    condition_func: Optional[str] = None
    weight: float = 1.0


@dataclass
class GeneratedWorkflow:
    """生成的工作流"""
    workflow_id: str
    name: str
    description: str
    pattern: WorkflowPattern
    complexity: TaskComplexity
    nodes: List[WorkflowNode]
    edges: List[WorkflowEdge]
    entry_point: str
    exit_points: List[str]
    estimated_duration: float
    resource_requirements: Dict[str, Any]
    optimization_hints: List[str]


class DynamicWorkflowGenerator:
    """动态工作流生成器"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.logger = logging.getLogger(__name__)
        
        # 预定义的工作流模板
        self.workflow_templates = self._init_workflow_templates()
        
        # 任务模式识别规则
        self.task_patterns = self._init_task_patterns()
        
        # 节点类型定义
        self.node_types = self._init_node_types()
    
    def _init_workflow_templates(self) -> Dict[str, Dict[str, Any]]:
        """初始化工作流模板"""
        return {
            "data_processing": {
                "pattern": WorkflowPattern.LINEAR,
                "nodes": ["data_input", "data_validation", "data_processing", "data_output"],
                "description": "数据处理工作流"
            },
            "api_integration": {
                "pattern": WorkflowPattern.CONDITIONAL,
                "nodes": ["api_search", "api_evaluation", "api_integration", "testing"],
                "description": "API集成工作流"
            },
            "code_generation": {
                "pattern": WorkflowPattern.ITERATIVE,
                "nodes": ["requirement_analysis", "code_generation", "code_validation", "optimization"],
                "description": "代码生成工作流"
            },
            "research_analysis": {
                "pattern": WorkflowPattern.PARALLEL,
                "nodes": ["web_search", "document_analysis", "data_synthesis", "report_generation"],
                "description": "研究分析工作流"
            },
            "problem_solving": {
                "pattern": WorkflowPattern.HYBRID,
                "nodes": ["problem_decomposition", "solution_search", "solution_evaluation", "implementation"],
                "description": "问题解决工作流"
            }
        }
    
    def _init_task_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化任务模式识别规则"""
        return {
            "data_keywords": {
                "keywords": ["数据", "处理", "分析", "清洗", "转换", "data", "process", "analyze"],
                "template": "data_processing",
                "complexity_factors": ["数据量", "数据源数量", "处理步骤"]
            },
            "api_keywords": {
                "keywords": ["API", "接口", "集成", "调用", "服务", "integration"],
                "template": "api_integration",
                "complexity_factors": ["API数量", "认证复杂度", "数据格式"]
            },
            "code_keywords": {
                "keywords": ["代码", "编程", "开发", "实现", "算法", "code", "programming"],
                "template": "code_generation",
                "complexity_factors": ["功能复杂度", "技术栈", "性能要求"]
            },
            "research_keywords": {
                "keywords": ["研究", "调研", "分析", "报告", "总结", "research", "analysis"],
                "template": "research_analysis",
                "complexity_factors": ["信息源数量", "分析深度", "报告要求"]
            },
            "problem_keywords": {
                "keywords": ["解决", "问题", "方案", "优化", "改进", "solve", "problem"],
                "template": "problem_solving",
                "complexity_factors": ["问题复杂度", "约束条件", "解决方案数量"]
            }
        }
    
    def _init_node_types(self) -> Dict[str, Dict[str, Any]]:
        """初始化节点类型定义"""
        return {
            "input": {
                "description": "输入处理节点",
                "base_duration": 1.0,
                "resource_weight": 0.1
            },
            "analysis": {
                "description": "分析处理节点",
                "base_duration": 5.0,
                "resource_weight": 0.3
            },
            "search": {
                "description": "搜索节点",
                "base_duration": 10.0,
                "resource_weight": 0.2
            },
            "generation": {
                "description": "生成节点",
                "base_duration": 15.0,
                "resource_weight": 0.5
            },
            "validation": {
                "description": "验证节点",
                "base_duration": 3.0,
                "resource_weight": 0.2
            },
            "integration": {
                "description": "集成节点",
                "base_duration": 8.0,
                "resource_weight": 0.4
            },
            "output": {
                "description": "输出节点",
                "base_duration": 2.0,
                "resource_weight": 0.1
            }
        }
    
    async def generate_workflow(self, task_description: str, context: Dict[str, Any] = None) -> GeneratedWorkflow:
        """根据任务描述生成工作流"""
        self.logger.info(f"🔧 开始生成工作流: {task_description}")
        
        try:
            # 1. 分析任务特征
            task_analysis = await self._analyze_task(task_description, context)
            
            # 2. 选择工作流模板
            template = self._select_template(task_analysis)
            
            # 3. 生成具体节点
            nodes = await self._generate_nodes(task_analysis, template)
            
            # 4. 生成边连接
            edges = self._generate_edges(nodes, template)
            
            # 5. 优化工作流
            optimized_workflow = self._optimize_workflow(nodes, edges, task_analysis)
            
            # 6. 创建工作流对象
            workflow = GeneratedWorkflow(
                workflow_id=f"workflow_{hash(task_description) % 10000}",
                name=f"Dynamic Workflow for: {task_description[:50]}...",
                description=f"Auto-generated workflow for: {task_description}",
                pattern=template["pattern"],
                complexity=task_analysis["complexity"],
                nodes=optimized_workflow["nodes"],
                edges=optimized_workflow["edges"],
                entry_point=optimized_workflow["entry_point"],
                exit_points=optimized_workflow["exit_points"],
                estimated_duration=optimized_workflow["estimated_duration"],
                resource_requirements=optimized_workflow["resource_requirements"],
                optimization_hints=optimized_workflow["optimization_hints"]
            )
            
            self.logger.info(f"✅ 工作流生成完成: {len(nodes)} 个节点, {len(edges)} 条边")
            return workflow
            
        except Exception as e:
            self.logger.error(f"工作流生成失败: {e}")
            # 返回默认工作流
            return self._create_default_workflow(task_description)
    
    async def _analyze_task(self, task_description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析任务特征"""
        analysis = {
            "task_description": task_description,
            "task_type": "general",
            "complexity": TaskComplexity.MEDIUM,
            "required_capabilities": [],
            "estimated_steps": 3,
            "parallel_opportunities": [],
            "risk_factors": [],
            "optimization_opportunities": []
        }
        
        # 使用LLM进行深度分析
        if self.llm_client:
            try:
                llm_analysis = await self._llm_task_analysis(task_description, context)
                analysis.update(llm_analysis)
            except Exception as e:
                self.logger.warning(f"LLM分析失败，使用基础分析: {e}")
        
        # 基础模式识别
        task_lower = task_description.lower()
        
        for pattern_name, pattern_info in self.task_patterns.items():
            keyword_matches = sum(1 for keyword in pattern_info["keywords"] 
                                if keyword in task_lower)
            if keyword_matches > 0:
                analysis["task_type"] = pattern_info["template"]
                analysis["pattern_confidence"] = keyword_matches / len(pattern_info["keywords"])
                break
        
        # 复杂度评估
        complexity_indicators = [
            len(task_description) > 200,  # 长描述
            "复杂" in task_lower or "complex" in task_lower,
            "多步骤" in task_lower or "multiple" in task_lower,
            "集成" in task_lower or "integration" in task_lower,
            "优化" in task_lower or "optimization" in task_lower
        ]
        
        complexity_score = sum(complexity_indicators)
        if complexity_score >= 3:
            analysis["complexity"] = TaskComplexity.COMPLEX
        elif complexity_score >= 1:
            analysis["complexity"] = TaskComplexity.MEDIUM
        else:
            analysis["complexity"] = TaskComplexity.SIMPLE
        
        return analysis

    async def _llm_task_analysis(self, task_description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """使用LLM进行深度任务分析"""
        analysis_prompt = f"""
请分析以下任务，并以JSON格式返回分析结果：

任务描述: {task_description}

上下文信息: {json.dumps(context, ensure_ascii=False) if context else "无"}

请分析以下方面：
1. 任务类型 (data_processing, api_integration, code_generation, research_analysis, problem_solving)
2. 复杂度等级 (simple, medium, complex, expert)
3. 所需能力列表
4. 预估步骤数量
5. 并行处理机会
6. 风险因素
7. 优化机会

返回格式：
{{
    "task_type": "任务类型",
    "complexity": "复杂度",
    "required_capabilities": ["能力1", "能力2"],
    "estimated_steps": 数字,
    "parallel_opportunities": ["可并行的步骤"],
    "risk_factors": ["风险因素"],
    "optimization_opportunities": ["优化机会"]
}}
"""

        try:
            response = await self.llm_client.generate_response(analysis_prompt)
            # 尝试解析JSON响应
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                self.logger.warning("LLM响应不包含有效JSON")
                return {}
        except Exception as e:
            self.logger.error(f"LLM任务分析失败: {e}")
            return {}

    def _select_template(self, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """选择合适的工作流模板"""
        task_type = task_analysis.get("task_type", "general")

        if task_type in self.workflow_templates:
            template = self.workflow_templates[task_type].copy()
        else:
            # 使用通用模板
            template = {
                "pattern": WorkflowPattern.LINEAR,
                "nodes": ["analysis", "processing", "output"],
                "description": "通用工作流"
            }

        # 根据复杂度调整模板
        complexity = task_analysis.get("complexity", TaskComplexity.MEDIUM)
        if complexity == TaskComplexity.COMPLEX:
            # 添加更多验证和优化步骤
            if "validation" not in template["nodes"]:
                template["nodes"].insert(-1, "validation")
            if "optimization" not in template["nodes"]:
                template["nodes"].append("optimization")
        elif complexity == TaskComplexity.SIMPLE:
            # 简化流程
            template["nodes"] = [node for node in template["nodes"]
                               if node not in ["optimization", "detailed_validation"]]

        return template

    async def _generate_nodes(self, task_analysis: Dict[str, Any], template: Dict[str, Any]) -> List[WorkflowNode]:
        """生成具体的工作流节点"""
        nodes = []
        base_nodes = template["nodes"]

        for i, node_name in enumerate(base_nodes):
            # 确定节点类型
            node_type = self._determine_node_type(node_name)

            # 生成节点描述
            description = await self._generate_node_description(
                node_name, task_analysis, template
            )

            # 设置依赖关系
            dependencies = []
            if i > 0:
                dependencies.append(base_nodes[i-1])

            # 设置参数
            parameters = self._generate_node_parameters(node_name, task_analysis)

            # 设置重试配置
            retry_config = self._generate_retry_config(node_name, task_analysis)

            node = WorkflowNode(
                name=node_name,
                node_type=node_type,
                description=description,
                dependencies=dependencies,
                parameters=parameters,
                priority=i + 1,
                retry_config=retry_config
            )

            nodes.append(node)

        # 处理并行机会
        parallel_opportunities = task_analysis.get("parallel_opportunities", [])
        if parallel_opportunities:
            nodes = self._add_parallel_groups(nodes, parallel_opportunities)

        return nodes

    def _determine_node_type(self, node_name: str) -> str:
        """确定节点类型"""
        type_mapping = {
            "input": "input",
            "data_input": "input",
            "task_reception": "input",

            "analysis": "analysis",
            "task_analysis": "analysis",
            "requirement_analysis": "analysis",
            "data_validation": "analysis",

            "search": "search",
            "api_search": "search",
            "web_search": "search",
            "solution_search": "search",

            "generation": "generation",
            "code_generation": "generation",
            "report_generation": "generation",
            "solution_generation": "generation",

            "validation": "validation",
            "code_validation": "validation",
            "result_validation": "validation",
            "testing": "validation",

            "integration": "integration",
            "api_integration": "integration",
            "data_integration": "integration",

            "output": "output",
            "data_output": "output",
            "response_generation": "output"
        }

        return type_mapping.get(node_name, "analysis")

    async def _generate_node_description(self, node_name: str, task_analysis: Dict[str, Any],
                                       template: Dict[str, Any]) -> str:
        """生成节点描述"""
        task_description = task_analysis["task_description"]

        if self.llm_client:
            try:
                description_prompt = f"""
为工作流节点生成简洁的描述：

节点名称: {node_name}
任务背景: {task_description}
工作流类型: {template.get('description', '通用工作流')}

请生成一个简洁明确的节点描述（不超过50字）：
"""
                description = await self.llm_client.generate_response(description_prompt)
                return description.strip()[:100]  # 限制长度
            except Exception as e:
                self.logger.warning(f"LLM节点描述生成失败: {e}")

        # 回退到预定义描述
        default_descriptions = {
            "task_reception": "接收和解析用户任务",
            "analysis": "分析任务需求和复杂度",
            "search": "搜索相关资源和信息",
            "generation": "生成解决方案或代码",
            "validation": "验证结果质量和正确性",
            "integration": "集成外部服务或API",
            "output": "格式化和输出最终结果"
        }

        return default_descriptions.get(node_name, f"执行{node_name}相关操作")

    def _generate_node_parameters(self, node_name: str, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成节点参数"""
        base_params = {
            "timeout": 300,  # 5分钟默认超时
            "max_retries": 3,
            "log_level": "INFO"
        }

        # 根据节点类型添加特定参数
        if "search" in node_name:
            base_params.update({
                "max_results": 10,
                "search_timeout": 60
            })
        elif "generation" in node_name:
            base_params.update({
                "max_tokens": 4000,
                "temperature": 0.7
            })
        elif "validation" in node_name:
            base_params.update({
                "validation_threshold": 0.8,
                "strict_mode": False
            })

        # 根据任务复杂度调整参数
        complexity = task_analysis.get("complexity", TaskComplexity.MEDIUM)
        if complexity == TaskComplexity.COMPLEX:
            base_params["timeout"] *= 2
            base_params["max_retries"] += 1
        elif complexity == TaskComplexity.SIMPLE:
            base_params["timeout"] //= 2

        return base_params

    def _generate_retry_config(self, node_name: str, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成重试配置"""
        base_config = {
            "max_attempts": 3,
            "backoff_factor": 2.0,
            "retry_on_errors": ["timeout", "network_error", "temporary_failure"]
        }

        # 关键节点增加重试次数
        critical_nodes = ["generation", "integration", "validation"]
        if any(critical in node_name for critical in critical_nodes):
            base_config["max_attempts"] = 5

        return base_config

    def _add_parallel_groups(self, nodes: List[WorkflowNode], parallel_opportunities: List[str]) -> List[WorkflowNode]:
        """添加并行处理组"""
        for i, opportunity in enumerate(parallel_opportunities):
            group_name = f"parallel_group_{i}"

            # 查找可以并行的节点
            for node in nodes:
                if any(keyword in node.name for keyword in opportunity.split()):
                    node.parallel_group = group_name

        return nodes

    def _generate_edges(self, nodes: List[WorkflowNode], template: Dict[str, Any]) -> List[WorkflowEdge]:
        """生成工作流边"""
        edges = []

        # 基础线性连接
        for i in range(len(nodes) - 1):
            current_node = nodes[i]
            next_node = nodes[i + 1]

            # 检查是否需要条件边
            condition = self._determine_edge_condition(current_node, next_node)

            edge = WorkflowEdge(
                from_node=current_node.name,
                to_node=next_node.name,
                condition=condition,
                weight=1.0
            )
            edges.append(edge)

        # 处理并行组
        parallel_groups = {}
        for node in nodes:
            if node.parallel_group:
                if node.parallel_group not in parallel_groups:
                    parallel_groups[node.parallel_group] = []
                parallel_groups[node.parallel_group].append(node)

        # 为并行组创建特殊边
        for group_name, group_nodes in parallel_groups.items():
            if len(group_nodes) > 1:
                # 创建并行分支
                for node in group_nodes[1:]:
                    # 从前一个节点分支到并行节点
                    prev_node = self._find_previous_node(node, nodes)
                    if prev_node:
                        edge = WorkflowEdge(
                            from_node=prev_node.name,
                            to_node=node.name,
                            condition="parallel_branch",
                            weight=0.5
                        )
                        edges.append(edge)

        # 添加条件分支
        edges.extend(self._generate_conditional_edges(nodes, template))

        return edges

    def _determine_edge_condition(self, from_node: WorkflowNode, to_node: WorkflowNode) -> Optional[str]:
        """确定边的条件"""
        # 基于节点类型确定条件
        if from_node.node_type == "validation":
            return "validation_passed"
        elif from_node.node_type == "search" and to_node.node_type == "integration":
            return "results_found"
        elif from_node.node_type == "analysis" and "generation" in to_node.name:
            return "requirements_clear"

        return None

    def _find_previous_node(self, target_node: WorkflowNode, all_nodes: List[WorkflowNode]) -> Optional[WorkflowNode]:
        """查找目标节点的前一个节点"""
        target_index = all_nodes.index(target_node)
        if target_index > 0:
            return all_nodes[target_index - 1]
        return None

    def _generate_conditional_edges(self, nodes: List[WorkflowNode], template: Dict[str, Any]) -> List[WorkflowEdge]:
        """生成条件边"""
        conditional_edges = []

        # 基于模板模式生成条件边
        pattern = template.get("pattern", WorkflowPattern.LINEAR)

        if pattern == WorkflowPattern.CONDITIONAL:
            # 添加失败重试边
            for node in nodes:
                if node.node_type in ["generation", "integration", "validation"]:
                    retry_edge = WorkflowEdge(
                        from_node=node.name,
                        to_node=node.name,  # 自循环
                        condition="retry_needed",
                        condition_func="should_retry",
                        weight=0.3
                    )
                    conditional_edges.append(retry_edge)

        elif pattern == WorkflowPattern.ITERATIVE:
            # 添加迭代边
            generation_nodes = [n for n in nodes if "generation" in n.name]
            validation_nodes = [n for n in nodes if "validation" in n.name]

            if generation_nodes and validation_nodes:
                iteration_edge = WorkflowEdge(
                    from_node=validation_nodes[0].name,
                    to_node=generation_nodes[0].name,
                    condition="needs_improvement",
                    condition_func="should_iterate",
                    weight=0.4
                )
                conditional_edges.append(iteration_edge)

        return conditional_edges

    def _optimize_workflow(self, nodes: List[WorkflowNode], edges: List[WorkflowEdge],
                          task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """优化工作流"""
        # 计算预估执行时间
        total_duration = 0.0
        for node in nodes:
            node_type_info = self.node_types.get(node.node_type, {"base_duration": 5.0})
            base_duration = node_type_info["base_duration"]

            # 根据复杂度调整时间
            complexity = task_analysis.get("complexity", TaskComplexity.MEDIUM)
            if complexity == TaskComplexity.COMPLEX:
                base_duration *= 1.5
            elif complexity == TaskComplexity.SIMPLE:
                base_duration *= 0.7

            total_duration += base_duration

        # 计算资源需求
        resource_requirements = {
            "cpu_intensive": False,
            "memory_intensive": False,
            "network_intensive": False,
            "storage_intensive": False
        }

        for node in nodes:
            if node.node_type in ["generation", "analysis"]:
                resource_requirements["cpu_intensive"] = True
            if node.node_type in ["search", "integration"]:
                resource_requirements["network_intensive"] = True

        # 生成优化建议
        optimization_hints = []

        # 检查并行机会
        parallel_nodes = [n for n in nodes if n.parallel_group]
        if len(parallel_nodes) > 1:
            optimization_hints.append("检测到并行处理机会，可以提高执行效率")

        # 检查瓶颈节点
        generation_nodes = [n for n in nodes if "generation" in n.name]
        if len(generation_nodes) > 2:
            optimization_hints.append("多个生成节点可能成为瓶颈，考虑合并或优化")

        # 检查重复验证
        validation_nodes = [n for n in nodes if "validation" in n.name]
        if len(validation_nodes) > 2:
            optimization_hints.append("考虑合并验证步骤以提高效率")

        if not optimization_hints:
            optimization_hints.append("工作流结构合理，无明显优化点")

        return {
            "nodes": nodes,
            "edges": edges,
            "entry_point": nodes[0].name if nodes else "start",
            "exit_points": [nodes[-1].name] if nodes else ["end"],
            "estimated_duration": total_duration,
            "resource_requirements": resource_requirements,
            "optimization_hints": optimization_hints
        }

    def _create_default_workflow(self, task_description: str) -> GeneratedWorkflow:
        """创建默认工作流（当生成失败时使用）"""
        default_nodes = [
            WorkflowNode(
                name="task_reception",
                node_type="input",
                description="接收和解析任务",
                priority=1
            ),
            WorkflowNode(
                name="task_analysis",
                node_type="analysis",
                description="分析任务需求",
                dependencies=["task_reception"],
                priority=2
            ),
            WorkflowNode(
                name="task_execution",
                node_type="generation",
                description="执行任务",
                dependencies=["task_analysis"],
                priority=3
            ),
            WorkflowNode(
                name="result_output",
                node_type="output",
                description="输出结果",
                dependencies=["task_execution"],
                priority=4
            )
        ]

        default_edges = [
            WorkflowEdge("task_reception", "task_analysis"),
            WorkflowEdge("task_analysis", "task_execution"),
            WorkflowEdge("task_execution", "result_output")
        ]

        return GeneratedWorkflow(
            workflow_id="default_workflow",
            name="Default Workflow",
            description=f"Default workflow for: {task_description}",
            pattern=WorkflowPattern.LINEAR,
            complexity=TaskComplexity.MEDIUM,
            nodes=default_nodes,
            edges=default_edges,
            entry_point="task_reception",
            exit_points=["result_output"],
            estimated_duration=30.0,
            resource_requirements={"cpu_intensive": False, "memory_intensive": False},
            optimization_hints=["使用默认工作流，建议根据具体需求优化"]
        )
