"""
LangGraph Agent 状态定义
定义自进化智能体的状态结构和数据流
"""
from typing import Dict, List, Any, Optional, TypedDict, Union
from dataclasses import dataclass, field
from enum import Enum
import time


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    CAPABILITY_CHECK = "capability_check"
    API_SEARCH = "api_search"
    API_EVALUATION = "api_evaluation"
    API_INTEGRATION = "api_integration"
    CODE_GENERATION = "code_generation"
    CODE_VALIDATION = "code_validation"
    DEPENDENCY_MANAGEMENT = "dependency_management"
    TASK_EXECUTION = "task_execution"
    RESULT_VALIDATION = "result_validation"
    EXPERIENCE_LEARNING = "experience_learning"
    RESPONSE_GENERATION = "response_generation"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


class CapabilityStatus(Enum):
    """能力状态枚举"""
    AVAILABLE = "available"
    MISSING = "missing"
    SEARCHING_API = "searching_api"
    API_FOUND = "api_found"
    API_INTEGRATED = "api_integrated"
    GENERATING_CODE = "generating_code"
    CODE_GENERATED = "code_generated"
    CODE_VALIDATED = "code_validated"
    DEPENDENCIES_INSTALLED = "dependencies_installed"
    READY = "ready"
    FAILED = "failed"


class APIStatus(Enum):
    """API状态枚举"""
    SEARCHING = "searching"
    FOUND = "found"
    EVALUATING = "evaluating"
    EVALUATED = "evaluated"
    INTEGRATING = "integrating"
    INTEGRATED = "integrated"
    FAILED = "failed"


class CodeStatus(Enum):
    """代码状态枚举"""
    GENERATING = "generating"
    GENERATED = "generated"
    VALIDATING = "validating"
    VALIDATED = "validated"
    TESTING = "testing"
    TESTED = "tested"
    FAILED = "failed"


@dataclass
class APICandidate:
    """API候选信息"""
    name: str
    url: str
    description: str
    documentation_url: Optional[str] = None
    quality_score: float = 0.0
    integration_difficulty: str = "unknown"  # easy, medium, hard
    cost: str = "unknown"  # free, paid, freemium
    rate_limit: Optional[str] = None
    authentication_type: Optional[str] = None
    status: APIStatus = APIStatus.FOUND


@dataclass
class CodeArtifact:
    """代码工件信息"""
    name: str
    code_content: str
    language: str = "python"
    file_path: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)
    validation_score: float = 0.0
    test_results: Optional[Dict[str, Any]] = None
    status: CodeStatus = CodeStatus.GENERATED
    error_message: Optional[str] = None


@dataclass
class CapabilityInfo:
    """能力信息"""
    name: str
    description: str
    status: CapabilityStatus
    required_libraries: List[str] = field(default_factory=list)

    # API相关
    api_candidates: List[APICandidate] = field(default_factory=list)
    selected_api: Optional[APICandidate] = None
    api_integration_code: Optional[str] = None

    # 代码生成相关
    generated_code_artifacts: List[CodeArtifact] = field(default_factory=list)
    selected_code: Optional[CodeArtifact] = None

    # 通用
    validation_score: Optional[float] = None
    error_message: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)


@dataclass
class TaskContext:
    """任务上下文"""
    original_task: str
    task_id: str
    user_input: str

    # 分析结果
    llm_analysis: Optional[Dict[str, Any]] = None
    required_capabilities: List[str] = field(default_factory=list)
    missing_capabilities: List[str] = field(default_factory=list)

    # 执行相关
    execution_plan: Optional[Dict[str, Any]] = None
    execution_result: Optional[Any] = None
    execution_metadata: Dict[str, Any] = field(default_factory=dict)

    # 历史和错误
    error_history: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3

    # 时间戳
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None


@dataclass
class DependencyInfo:
    """依赖信息"""
    package_name: str
    version: Optional[str] = None
    install_name: Optional[str] = None  # pip install name if different
    is_installed: bool = False
    installation_method: str = "pip"  # pip, conda, etc.
    installation_time: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class ExecutionMetrics:
    """执行指标"""
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
    api_calls_count: int = 0
    code_generation_count: int = 0
    dependency_install_count: int = 0
    error_count: int = 0
    retry_count: int = 0


class AgentState(TypedDict):
    """
    LangGraph Agent 状态
    使用 TypedDict 定义状态结构，支持类型检查和自动补全
    """
    # === 核心状态 ===
    task_context: TaskContext
    current_status: TaskStatus
    current_node: Optional[str]

    # === 能力管理 ===
    capabilities: Dict[str, CapabilityInfo]
    available_capabilities: List[str]
    capability_search_results: Dict[str, List[APICandidate]]

    # === API管理 ===
    api_search_results: Dict[str, List[APICandidate]]
    api_evaluation_results: Dict[str, Dict[str, Any]]
    api_integration_results: Dict[str, Dict[str, Any]]

    # === 代码管理 ===
    code_generation_results: Dict[str, List[CodeArtifact]]
    code_validation_results: Dict[str, Dict[str, Any]]
    generated_files: List[str]

    # === 依赖管理 ===
    dependency_analysis: Dict[str, List[DependencyInfo]]
    dependency_installation_results: Dict[str, Dict[str, Any]]
    installed_packages: List[str]

    # === 执行管理 ===
    execution_plan: Optional[Dict[str, Any]]
    execution_results: Dict[str, Any]
    task_outputs: List[Any]

    # === 验证和质量 ===
    validation_results: Dict[str, Dict[str, Any]]
    quality_scores: Dict[str, float]
    user_feedback: Optional[Dict[str, Any]]

    # === 学习和优化 ===
    experience_data: Dict[str, Any]
    performance_metrics: ExecutionMetrics
    optimization_suggestions: List[str]

    # === 元数据和调试 ===
    step_count: int
    node_execution_history: List[Dict[str, Any]]
    error_messages: List[str]
    warning_messages: List[str]
    debug_info: Dict[str, Any]

    # === 配置和偏好 ===
    user_preferences: Dict[str, Any]
    execution_config: Dict[str, Any]

    # === 最终输出 ===
    final_response: Optional[str]
    response_metadata: Dict[str, Any]


def create_initial_state(user_task: str, task_id: str, config: Optional[Dict[str, Any]] = None) -> AgentState:
    """创建初始状态"""
    current_time = time.time()

    return AgentState(
        # 核心状态
        task_context=TaskContext(
            original_task=user_task,
            task_id=task_id,
            user_input=user_task,
            created_at=current_time,
            started_at=current_time
        ),
        current_status=TaskStatus.PENDING,
        current_node=None,

        # 能力管理
        capabilities={},
        available_capabilities=[],
        capability_search_results={},

        # API管理
        api_search_results={},
        api_evaluation_results={},
        api_integration_results={},

        # 代码管理
        code_generation_results={},
        code_validation_results={},
        generated_files=[],

        # 依赖管理
        dependency_analysis={},
        dependency_installation_results={},
        installed_packages=[],

        # 执行管理
        execution_plan=None,
        execution_results={},
        task_outputs=[],

        # 验证和质量
        validation_results={},
        quality_scores={},
        user_feedback=None,

        # 学习和优化
        experience_data={},
        performance_metrics=ExecutionMetrics(start_time=current_time),
        optimization_suggestions=[],

        # 元数据和调试
        step_count=0,
        node_execution_history=[],
        error_messages=[],
        warning_messages=[],
        debug_info={},

        # 配置和偏好
        user_preferences=config.get("user_preferences", {}) if config else {},
        execution_config=config.get("execution_config", {}) if config else {},

        # 最终输出
        final_response=None,
        response_metadata={}
    )


def update_state_status(state: AgentState, new_status: TaskStatus, node_name: Optional[str] = None) -> AgentState:
    """更新状态"""
    old_status = state["current_status"]
    state["current_status"] = new_status
    state["step_count"] += 1

    if node_name:
        state["current_node"] = node_name

    # 记录状态变更历史
    state["node_execution_history"].append({
        "step": state["step_count"],
        "timestamp": time.time(),
        "from_status": old_status.value,
        "to_status": new_status.value,
        "node": node_name,
    })

    return state


def add_error_message(state: AgentState, error: str, node_name: Optional[str] = None) -> AgentState:
    """添加错误信息"""
    error_entry = {
        "message": error,
        "timestamp": time.time(),
        "node": node_name,
        "step": state["step_count"]
    }
    state["error_messages"].append(error_entry)
    state["performance_metrics"]["error_count"] += 1
    return state


def add_warning_message(state: AgentState, warning: str, node_name: Optional[str] = None) -> AgentState:
    """添加警告信息"""
    warning_entry = {
        "message": warning,
        "timestamp": time.time(),
        "node": node_name,
        "step": state["step_count"]
    }
    state["warning_messages"].append(warning_entry)
    return state


def add_debug_info(state: AgentState, key: str, value: Any, node_name: Optional[str] = None) -> AgentState:
    """添加调试信息"""
    debug_entry = {
        "value": value,
        "timestamp": time.time(),
        "node": node_name,
        "step": state["step_count"]
    }
    state["debug_info"][key] = debug_entry
    return state


def update_capability_status(state: AgentState, capability_name: str,
                           new_status: CapabilityStatus, **kwargs) -> AgentState:
    """更新能力状态"""
    current_time = time.time()

    if capability_name not in state["capabilities"]:
        state["capabilities"][capability_name] = CapabilityInfo(
            name=capability_name,
            description=kwargs.get("description", ""),
            status=new_status,
            created_at=current_time,
            updated_at=current_time
        )
    else:
        state["capabilities"][capability_name].status = new_status
        state["capabilities"][capability_name].updated_at = current_time

    # 更新其他属性
    for key, value in kwargs.items():
        if hasattr(state["capabilities"][capability_name], key):
            setattr(state["capabilities"][capability_name], key, value)

    return state


def add_api_candidate(state: AgentState, capability_name: str, api_candidate: APICandidate) -> AgentState:
    """添加API候选"""
    if capability_name not in state["api_search_results"]:
        state["api_search_results"][capability_name] = []

    state["api_search_results"][capability_name].append(api_candidate)

    # 同时更新能力信息
    if capability_name in state["capabilities"]:
        state["capabilities"][capability_name].api_candidates.append(api_candidate)

    return state


def add_code_artifact(state: AgentState, capability_name: str, code_artifact: CodeArtifact) -> AgentState:
    """添加代码工件"""
    if capability_name not in state["code_generation_results"]:
        state["code_generation_results"][capability_name] = []

    state["code_generation_results"][capability_name].append(code_artifact)

    # 同时更新能力信息
    if capability_name in state["capabilities"]:
        state["capabilities"][capability_name].generated_code_artifacts.append(code_artifact)

    return state


def update_dependency_status(state: AgentState, package_name: str, dependency_info: DependencyInfo) -> AgentState:
    """更新依赖状态"""
    capability_key = "global"  # 可以根据需要分组

    if capability_key not in state["dependency_analysis"]:
        state["dependency_analysis"][capability_key] = []

    # 查找现有依赖或添加新依赖
    existing_dep = None
    for dep in state["dependency_analysis"][capability_key]:
        if dep.package_name == package_name:
            existing_dep = dep
            break

    if existing_dep:
        # 更新现有依赖
        existing_dep.is_installed = dependency_info.is_installed
        existing_dep.installation_time = dependency_info.installation_time
        existing_dep.error_message = dependency_info.error_message
    else:
        # 添加新依赖
        state["dependency_analysis"][capability_key].append(dependency_info)

    return state


def get_missing_capabilities(state: AgentState) -> List[str]:
    """获取缺失的能力"""
    return [
        name for name, info in state["capabilities"].items()
        if info.status == CapabilityStatus.MISSING
    ]


def get_ready_capabilities(state: AgentState) -> List[str]:
    """获取就绪的能力"""
    return [
        name for name, info in state["capabilities"].items()
        if info.status == CapabilityStatus.READY
    ]


def is_task_completed(state: AgentState) -> bool:
    """检查任务是否完成"""
    return state["current_status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED]


def should_retry(state: AgentState) -> bool:
    """检查是否应该重试"""
    return (state["task_context"].retry_count < state["task_context"].max_retries and
            state["current_status"] == TaskStatus.FAILED)


def increment_retry_count(state: AgentState) -> AgentState:
    """增加重试计数"""
    state["task_context"].retry_count += 1
    state["performance_metrics"]["retry_count"] += 1
    return state


def finalize_task(state: AgentState, success: bool, final_response: str) -> AgentState:
    """完成任务"""
    current_time = time.time()

    state["task_context"].completed_at = current_time
    state["final_response"] = final_response
    state["current_status"] = TaskStatus.COMPLETED if success else TaskStatus.FAILED

    # 更新性能指标
    metrics = state["performance_metrics"]
    metrics["end_time"] = current_time
    metrics["duration"] = current_time - metrics["start_time"]

    return state


def get_state_summary(state: AgentState) -> Dict[str, Any]:
    """获取状态摘要"""
    metrics = state["performance_metrics"]

    return {
        "task_id": state["task_context"].task_id,
        "status": state["current_status"].value,
        "current_node": state["current_node"],
        "step_count": state["step_count"],
        "duration": metrics.get("duration"),
        "capabilities": {
            "total": len(state["capabilities"]),
            "missing": len(get_missing_capabilities(state)),
            "ready": len(get_ready_capabilities(state))
        },
        "apis": {
            "searched": len(state["api_search_results"]),
            "integrated": len(state["api_integration_results"])
        },
        "code": {
            "generated": len(state["code_generation_results"]),
            "validated": len(state["code_validation_results"])
        },
        "dependencies": {
            "analyzed": sum(len(deps) for deps in state["dependency_analysis"].values()),
            "installed": len(state["installed_packages"])
        },
        "errors": len(state["error_messages"]),
        "warnings": len(state["warning_messages"]),
        "performance": {
            "api_calls": metrics.get("api_calls_count", 0),
            "code_generations": metrics.get("code_generation_count", 0),
            "dependency_installs": metrics.get("dependency_install_count", 0),
            "retries": metrics.get("retry_count", 0)
        }
    }
