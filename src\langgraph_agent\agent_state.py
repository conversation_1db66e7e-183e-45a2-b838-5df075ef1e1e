"""
LangGraph Agent 状态定义
定义自进化智能体的状态结构和数据流
"""
from typing import Dict, List, Any, Optional, TypedDict
from dataclasses import dataclass, field
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    CAPABILITY_CHECK = "capability_check"
    MISSING_CAPABILITY = "missing_capability"
    GENERATING_CODE = "generating_code"
    VALIDATING_CODE = "validating_code"
    INSTALLING_DEPS = "installing_deps"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"


class CapabilityStatus(Enum):
    """能力状态枚举"""
    AVAILABLE = "available"
    MISSING = "missing"
    GENERATING = "generating"
    GENERATED = "generated"
    VALIDATED = "validated"
    INSTALLED = "installed"


@dataclass
class CapabilityInfo:
    """能力信息"""
    name: str
    description: str
    status: CapabilityStatus
    required_libraries: List[str] = field(default_factory=list)
    generated_code: Optional[str] = None
    validation_score: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class TaskContext:
    """任务上下文"""
    original_task: str
    task_id: str
    user_input: str
    analysis_result: Optional[Dict[str, Any]] = None
    required_capabilities: List[str] = field(default_factory=list)
    missing_capabilities: List[str] = field(default_factory=list)
    execution_result: Optional[Any] = None
    error_history: List[str] = field(default_factory=list)


class AgentState(TypedDict):
    """
    LangGraph Agent 状态
    使用 TypedDict 定义状态结构，支持类型检查和自动补全
    """
    # 任务相关
    task_context: TaskContext
    current_status: TaskStatus
    
    # 能力相关
    capabilities: Dict[str, CapabilityInfo]
    available_capabilities: List[str]

    # 分析结果
    llm_analysis: Optional[Dict[str, Any]]
    capability_analysis: Optional[Dict[str, Any]]

    # 代码生成
    generated_code: Optional[str]
    validation_result: Optional[Dict[str, Any]]

    # 依赖管理
    dependency_status: Optional[Dict[str, Any]]
    installation_result: Optional[Dict[str, Any]]

    # 执行结果
    execution_result: Optional[Any]
    final_response: Optional[str]

    # 元数据
    step_count: int
    execution_time: float
    error_messages: List[str]
    debug_info: Dict[str, Any]
    
    # 学习和优化
    experience_data: Optional[Dict[str, Any]]
    performance_metrics: Optional[Dict[str, Any]]


def create_initial_state(user_task: str, task_id: str) -> AgentState:
    """创建初始状态"""
    return AgentState(
        task_context=TaskContext(
            original_task=user_task,
            task_id=task_id,
            user_input=user_task
        ),
        current_status=TaskStatus.PENDING,
        capabilities={},
        available_capabilities=[],
        llm_analysis=None,
        capability_analysis=None,
        generated_code=None,
        validation_result=None,
        dependency_status=None,
        installation_result=None,
        execution_result=None,
        final_response=None,
        step_count=0,
        execution_time=0.0,
        error_messages=[],
        debug_info={},
        experience_data=None,
        performance_metrics=None
    )


def update_state_status(state: AgentState, new_status: TaskStatus) -> AgentState:
    """更新状态"""
    state["current_status"] = new_status
    state["step_count"] += 1
    return state


def add_error_message(state: AgentState, error: str) -> AgentState:
    """添加错误信息"""
    if state["error_messages"] is None:
        state["error_messages"] = []
    state["error_messages"].append(error)
    return state


def add_debug_info(state: AgentState, key: str, value: Any) -> AgentState:
    """添加调试信息"""
    if state["debug_info"] is None:
        state["debug_info"] = {}
    state["debug_info"][key] = value
    return state


def update_capability_status(state: AgentState, capability_name: str, 
                           new_status: CapabilityStatus, **kwargs) -> AgentState:
    """更新能力状态"""
    if capability_name not in state["capabilities"]:
        state["capabilities"][capability_name] = CapabilityInfo(
            name=capability_name,
            description=kwargs.get("description", ""),
            status=new_status
        )
    else:
        state["capabilities"][capability_name].status = new_status
    
    # 更新其他属性
    for key, value in kwargs.items():
        if hasattr(state["capabilities"][capability_name], key):
            setattr(state["capabilities"][capability_name], key, value)
    
    return state


def get_missing_capabilities(state: AgentState) -> List[str]:
    """获取缺失的能力"""
    return [
        name for name, info in state["capabilities"].items()
        if info.status == CapabilityStatus.MISSING
    ]


def is_task_completed(state: AgentState) -> bool:
    """检查任务是否完成"""
    return state["current_status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED]


def get_state_summary(state: AgentState) -> Dict[str, Any]:
    """获取状态摘要"""
    return {
        "task_id": state["task_context"].task_id,
        "status": state["current_status"].value,
        "step_count": state["step_count"],
        "capabilities_count": len(state["capabilities"]),
        "missing_capabilities": get_missing_capabilities(state),
        "has_errors": len(state["error_messages"]) > 0,
        "execution_time": state["execution_time"]
    }
