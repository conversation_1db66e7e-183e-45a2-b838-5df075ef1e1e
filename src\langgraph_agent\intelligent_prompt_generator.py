"""
智能Prompt生成器
根据任务和上下文自动生成优化的prompt
"""
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .agent_state import AgentState


class PromptType(Enum):
    """Prompt类型枚举"""
    TASK_ANALYSIS = "task_analysis"
    CAPABILITY_SEARCH = "capability_search"
    CODE_GENERATION = "code_generation"
    API_INTEGRATION = "api_integration"
    VALIDATION = "validation"
    OPTIMIZATION = "optimization"
    RESPONSE_GENERATION = "response_generation"


class PromptStyle(Enum):
    """Prompt风格枚举"""
    DIRECT = "direct"  # 直接指令
    CONVERSATIONAL = "conversational"  # 对话式
    STRUCTURED = "structured"  # 结构化
    CREATIVE = "creative"  # 创意式
    ANALYTICAL = "analytical"  # 分析式


@dataclass
class PromptTemplate:
    """Prompt模板"""
    name: str
    prompt_type: PromptType
    style: PromptStyle
    template: str
    variables: List[str] = field(default_factory=list)
    examples: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    optimization_hints: List[str] = field(default_factory=list)


@dataclass
class GeneratedPrompt:
    """生成的Prompt"""
    prompt_id: str
    prompt_type: PromptType
    style: PromptStyle
    content: str
    variables_used: Dict[str, Any]
    estimated_tokens: int
    optimization_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)


class IntelligentPromptGenerator:
    """智能Prompt生成器"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.logger = logging.getLogger(__name__)
        
        # 初始化Prompt模板库
        self.prompt_templates = self._init_prompt_templates()
        
        # 上下文分析器
        self.context_analyzer = ContextAnalyzer()
        
        # Prompt优化器
        self.prompt_optimizer = PromptOptimizer()
    
    def _init_prompt_templates(self) -> Dict[PromptType, List[PromptTemplate]]:
        """初始化Prompt模板库"""
        templates = {
            PromptType.TASK_ANALYSIS: [
                PromptTemplate(
                    name="detailed_task_analysis",
                    prompt_type=PromptType.TASK_ANALYSIS,
                    style=PromptStyle.ANALYTICAL,
                    template="""
请详细分析以下任务：

任务描述: {task_description}
上下文信息: {context}

请从以下角度进行分析：
1. 任务目标和预期结果
2. 所需的核心能力和技能
3. 可能的实现路径
4. 潜在的挑战和风险
5. 成功的评估标准

请以结构化的JSON格式返回分析结果。
""",
                    variables=["task_description", "context"],
                    constraints=["返回JSON格式", "分析要全面客观"],
                    optimization_hints=["使用具体的分析维度", "提供可操作的建议"]
                ),
                PromptTemplate(
                    name="quick_task_analysis",
                    prompt_type=PromptType.TASK_ANALYSIS,
                    style=PromptStyle.DIRECT,
                    template="""
快速分析任务: {task_description}

请简洁回答：
- 主要目标：
- 关键步骤：
- 所需能力：
- 预估难度：
""",
                    variables=["task_description"],
                    constraints=["保持简洁", "重点突出"],
                    optimization_hints=["适用于简单任务", "快速响应"]
                )
            ],
            
            PromptType.CODE_GENERATION: [
                PromptTemplate(
                    name="comprehensive_code_generation",
                    prompt_type=PromptType.CODE_GENERATION,
                    style=PromptStyle.STRUCTURED,
                    template="""
请为以下需求生成高质量的代码：

功能需求: {requirement}
编程语言: {language}
技术栈: {tech_stack}
性能要求: {performance_requirements}

代码要求：
1. 遵循最佳实践和编码规范
2. 包含适当的错误处理
3. 添加清晰的注释和文档
4. 考虑可扩展性和维护性
5. 包含基本的测试用例

请按以下格式返回：
```{language}
# 代码实现
[代码内容]
```

依赖包：
- [依赖列表]

使用说明：
[使用方法]
""",
                    variables=["requirement", "language", "tech_stack", "performance_requirements"],
                    constraints=["代码质量高", "包含文档", "考虑最佳实践"],
                    optimization_hints=["适用于复杂功能", "注重代码质量"]
                ),
                PromptTemplate(
                    name="rapid_code_generation",
                    prompt_type=PromptType.CODE_GENERATION,
                    style=PromptStyle.DIRECT,
                    template="""
快速生成{language}代码实现：{requirement}

要求：
- 功能完整
- 代码简洁
- 包含基本注释

```{language}
[请在此处生成代码]
```
""",
                    variables=["requirement", "language"],
                    constraints=["快速生成", "功能完整"],
                    optimization_hints=["适用于简单功能", "快速原型"]
                )
            ],
            
            PromptType.API_INTEGRATION: [
                PromptTemplate(
                    name="api_integration_guide",
                    prompt_type=PromptType.API_INTEGRATION,
                    style=PromptStyle.STRUCTURED,
                    template="""
请为以下API集成需求提供完整的解决方案：

API信息: {api_info}
集成目标: {integration_goal}
技术环境: {tech_environment}

请提供：
1. API调用示例代码
2. 错误处理机制
3. 认证和安全考虑
4. 数据格式转换
5. 性能优化建议

代码示例：
```python
[API集成代码]
```

注意事项：
- [重要提醒]
""",
                    variables=["api_info", "integration_goal", "tech_environment"],
                    constraints=["包含完整示例", "考虑安全性", "处理异常情况"],
                    optimization_hints=["注重实用性", "提供最佳实践"]
                )
            ],
            
            PromptType.VALIDATION: [
                PromptTemplate(
                    name="comprehensive_validation",
                    prompt_type=PromptType.VALIDATION,
                    style=PromptStyle.ANALYTICAL,
                    template="""
请对以下内容进行全面验证：

验证对象: {validation_target}
验证类型: {validation_type}
质量标准: {quality_standards}

验证维度：
1. 功能正确性
2. 性能表现
3. 安全性
4. 可维护性
5. 用户体验

请提供：
- 验证结果评分 (0-100)
- 具体问题列表
- 改进建议
- 风险评估

验证报告：
[详细分析]
""",
                    variables=["validation_target", "validation_type", "quality_standards"],
                    constraints=["客观评估", "提供具体建议", "量化评分"],
                    optimization_hints=["全面覆盖", "重点突出"]
                )
            ]
        }
        
        return templates
    
    async def generate_prompt(self, prompt_type: PromptType, context: Dict[str, Any], 
                            style_preference: Optional[PromptStyle] = None) -> GeneratedPrompt:
        """生成优化的Prompt"""
        self.logger.info(f"🎯 开始生成Prompt: {prompt_type.value}")
        
        try:
            # 1. 分析上下文
            context_analysis = await self.context_analyzer.analyze_context(context)
            
            # 2. 选择最佳模板
            template = self._select_best_template(prompt_type, context_analysis, style_preference)
            
            # 3. 填充模板变量
            filled_prompt = self._fill_template_variables(template, context)
            
            # 4. 优化Prompt
            optimized_prompt = await self.prompt_optimizer.optimize_prompt(
                filled_prompt, context_analysis
            )
            
            # 5. 创建生成结果
            generated_prompt = GeneratedPrompt(
                prompt_id=f"prompt_{hash(optimized_prompt) % 10000}",
                prompt_type=prompt_type,
                style=template.style,
                content=optimized_prompt,
                variables_used=self._extract_used_variables(template, context),
                estimated_tokens=len(optimized_prompt.split()) * 1.3,  # 估算token数
                optimization_score=await self._calculate_optimization_score(optimized_prompt, context_analysis),
                metadata={
                    "template_name": template.name,
                    "context_complexity": context_analysis.get("complexity", "medium"),
                    "generation_time": context_analysis.get("generation_time", 0)
                }
            )
            
            self.logger.info(f"✅ Prompt生成完成，评分: {generated_prompt.optimization_score:.2f}")
            return generated_prompt
            
        except Exception as e:
            self.logger.error(f"Prompt生成失败: {e}")
            # 返回基础Prompt
            return self._create_fallback_prompt(prompt_type, context)
    
    def _select_best_template(self, prompt_type: PromptType, context_analysis: Dict[str, Any], 
                            style_preference: Optional[PromptStyle] = None) -> PromptTemplate:
        """选择最佳模板"""
        available_templates = self.prompt_templates.get(prompt_type, [])
        
        if not available_templates:
            return self._create_default_template(prompt_type)
        
        # 根据上下文复杂度选择模板
        complexity = context_analysis.get("complexity", "medium")
        
        if style_preference:
            # 优先选择指定风格的模板
            preferred_templates = [t for t in available_templates if t.style == style_preference]
            if preferred_templates:
                available_templates = preferred_templates
        
        # 根据复杂度选择
        if complexity == "high":
            # 选择最详细的模板
            return max(available_templates, key=lambda t: len(t.template))
        elif complexity == "low":
            # 选择最简洁的模板
            return min(available_templates, key=lambda t: len(t.template))
        else:
            # 选择中等复杂度的模板
            return available_templates[len(available_templates) // 2]
    
    def _fill_template_variables(self, template: PromptTemplate, context: Dict[str, Any]) -> str:
        """填充模板变量"""
        prompt_content = template.template
        
        # 填充已知变量
        for variable in template.variables:
            value = context.get(variable, f"[{variable}]")
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False, indent=2)
            prompt_content = prompt_content.replace(f"{{{variable}}}", str(value))
        
        return prompt_content
    
    def _extract_used_variables(self, template: PromptTemplate, context: Dict[str, Any]) -> Dict[str, Any]:
        """提取使用的变量"""
        used_variables = {}
        for variable in template.variables:
            if variable in context:
                used_variables[variable] = context[variable]
        return used_variables
    
    async def _calculate_optimization_score(self, prompt: str, context_analysis: Dict[str, Any]) -> float:
        """计算优化评分"""
        score = 0.5  # 基础分数
        
        # 长度适中性 (0.2)
        prompt_length = len(prompt)
        if 100 <= prompt_length <= 2000:
            score += 0.2
        elif prompt_length < 100:
            score += 0.1
        
        # 结构清晰性 (0.2)
        if "请" in prompt or "Please" in prompt:
            score += 0.1
        if any(marker in prompt for marker in ["1.", "2.", "3.", "-", "•"]):
            score += 0.1
        
        # 上下文相关性 (0.3)
        context_keywords = context_analysis.get("keywords", [])
        keyword_matches = sum(1 for keyword in context_keywords if keyword in prompt)
        if keyword_matches > 0:
            score += min(0.3, keyword_matches * 0.1)
        
        return min(1.0, score)
    
    def _create_fallback_prompt(self, prompt_type: PromptType, context: Dict[str, Any]) -> GeneratedPrompt:
        """创建回退Prompt"""
        fallback_content = f"请处理以下{prompt_type.value}任务：\n\n{context.get('task_description', '未指定任务')}"
        
        return GeneratedPrompt(
            prompt_id="fallback_prompt",
            prompt_type=prompt_type,
            style=PromptStyle.DIRECT,
            content=fallback_content,
            variables_used=context,
            estimated_tokens=len(fallback_content.split()) * 1.3,
            optimization_score=0.3,
            metadata={"fallback": True}
        )
    
    def _create_default_template(self, prompt_type: PromptType) -> PromptTemplate:
        """创建默认模板"""
        return PromptTemplate(
            name=f"default_{prompt_type.value}",
            prompt_type=prompt_type,
            style=PromptStyle.DIRECT,
            template="请处理以下任务：{task_description}",
            variables=["task_description"]
        )


class ContextAnalyzer:
    """上下文分析器"""
    
    async def analyze_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """分析上下文信息"""
        analysis = {
            "complexity": "medium",
            "keywords": [],
            "domain": "general",
            "urgency": "normal",
            "technical_level": "intermediate"
        }
        
        # 分析任务描述
        task_description = context.get("task_description", "")
        if task_description:
            analysis["keywords"] = self._extract_keywords(task_description)
            analysis["complexity"] = self._assess_complexity(task_description)
            analysis["domain"] = self._identify_domain(task_description)
        
        # 分析技术要求
        if any(key in context for key in ["language", "tech_stack", "api_info"]):
            analysis["technical_level"] = "advanced"
        
        return analysis
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        import re
        words = re.findall(r'\b\w+\b', text.lower())
        # 过滤常见词汇
        stop_words = {"的", "是", "在", "有", "和", "与", "或", "但", "如果", "因为", "所以"}
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        return list(set(keywords))[:10]  # 返回前10个关键词
    
    def _assess_complexity(self, text: str) -> str:
        """评估复杂度"""
        complexity_indicators = [
            len(text) > 500,  # 长文本
            "复杂" in text or "complex" in text.lower(),
            "多步骤" in text or "multiple" in text.lower(),
            "集成" in text or "integration" in text.lower(),
            "优化" in text or "optimization" in text.lower(),
            "算法" in text or "algorithm" in text.lower()
        ]
        
        score = sum(complexity_indicators)
        if score >= 3:
            return "high"
        elif score >= 1:
            return "medium"
        else:
            return "low"
    
    def _identify_domain(self, text: str) -> str:
        """识别领域"""
        domain_keywords = {
            "data": ["数据", "分析", "处理", "data", "analysis"],
            "web": ["网站", "前端", "后端", "web", "frontend", "backend"],
            "ai": ["AI", "机器学习", "深度学习", "算法", "模型"],
            "api": ["API", "接口", "服务", "集成", "调用"],
            "mobile": ["移动", "手机", "APP", "mobile", "android", "ios"]
        }
        
        text_lower = text.lower()
        for domain, keywords in domain_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return domain
        
        return "general"


class PromptOptimizer:
    """Prompt优化器"""
    
    async def optimize_prompt(self, prompt: str, context_analysis: Dict[str, Any]) -> str:
        """优化Prompt"""
        optimized = prompt
        
        # 添加上下文相关的优化
        complexity = context_analysis.get("complexity", "medium")
        domain = context_analysis.get("domain", "general")
        
        # 根据复杂度调整
        if complexity == "high":
            optimized = self._add_detailed_instructions(optimized)
        elif complexity == "low":
            optimized = self._simplify_instructions(optimized)
        
        # 根据领域添加专业术语
        optimized = self._add_domain_context(optimized, domain)
        
        # 格式优化
        optimized = self._format_optimization(optimized)
        
        return optimized
    
    def _add_detailed_instructions(self, prompt: str) -> str:
        """添加详细说明"""
        if "请详细" not in prompt and "Please provide detailed" not in prompt:
            prompt = prompt.replace("请", "请详细")
        return prompt
    
    def _simplify_instructions(self, prompt: str) -> str:
        """简化说明"""
        # 移除过于复杂的要求
        simplified = prompt.replace("详细分析", "分析")
        simplified = simplified.replace("comprehensive", "basic")
        return simplified
    
    def _add_domain_context(self, prompt: str, domain: str) -> str:
        """添加领域上下文"""
        domain_contexts = {
            "data": "\n注意：请考虑数据质量、处理效率和结果准确性。",
            "web": "\n注意：请考虑用户体验、性能优化和安全性。",
            "ai": "\n注意：请考虑模型准确性、计算效率和可解释性。",
            "api": "\n注意：请考虑接口稳定性、错误处理和文档完整性。"
        }
        
        context = domain_contexts.get(domain, "")
        return prompt + context
    
    def _format_optimization(self, prompt: str) -> str:
        """格式优化"""
        # 确保适当的换行和缩进
        lines = prompt.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
