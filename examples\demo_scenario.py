"""
自进化智能体演示场景
展示智能体如何自动获得新能力并完成任务
"""
import asyncio
import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.agent import SelfEvolvingAgent
from src.core.hot_reload import HotReloader


class DemoScenario:
    """演示场景"""
    
    def __init__(self):
        self.agent = SelfEvolvingAgent()
        self.hot_reloader = None
        self.logger = logging.getLogger(__name__)
        
        # 模拟的API信息
        self.mock_apis = {
            "web_search": {
                "type": "rest",
                "base_url": "https://api.duckduckgo.com",
                "authentication": {},
                "endpoints": [
                    {
                        "name": "search",
                        "method": "GET",
                        "path": "/",
                        "description": "搜索网页内容",
                        "parameters": [
                            {"name": "q", "type": "str", "description": "搜索关键词"},
                            {"name": "format", "type": "str", "description": "返回格式", "default": "json"}
                        ],
                        "response": {"type": "object", "description": "搜索结果"}
                    }
                ]
            },
            "weather_api": {
                "type": "rest",
                "base_url": "https://api.openweathermap.org/data/2.5",
                "authentication": {
                    "type": "api_key",
                    "key": "your-api-key"
                },
                "endpoints": [
                    {
                        "name": "get_weather",
                        "method": "GET",
                        "path": "/weather",
                        "description": "获取天气信息",
                        "parameters": [
                            {"name": "q", "type": "str", "description": "城市名称"},
                            {"name": "appid", "type": "str", "description": "API密钥"}
                        ]
                    }
                ]
            },
            "file_operations": {
                "type": "local",
                "description": "本地文件操作",
                "endpoints": [
                    {
                        "name": "save_file",
                        "description": "保存文件",
                        "parameters": [
                            {"name": "filename", "type": "str", "description": "文件名"},
                            {"name": "content", "type": "str", "description": "文件内容"}
                        ]
                    },
                    {
                        "name": "read_file",
                        "description": "读取文件",
                        "parameters": [
                            {"name": "filename", "type": "str", "description": "文件名"}
                        ]
                    }
                ]
            }
        }
        
    async def initialize(self):
        """初始化演示环境"""
        print("🚀 初始化自进化智能体演示...")
        
        # 初始化智能体
        await self.agent.capability_registry.initialize()
        await self.agent.experience_recorder.initialize()
        
        # 设置自动API提供回调
        self.agent.set_user_interaction_callback(self.auto_provide_api)
        
        # 初始化热更新
        self.hot_reloader = HotReloader(
            self.agent.capability_registry,
            self.agent.task_executor
        )
        await self.hot_reloader.start_monitoring()
        
        print("✅ 初始化完成\n")
        
    async def auto_provide_api(self, interaction_type: str, data: dict) -> dict:
        """自动提供API信息"""
        if interaction_type == "capability_request":
            capability = data.get("capability")
            message = data.get("message")
            
            print(f"🤖 智能体请求: {message}")
            print(f"需要能力: {capability}")
            
            # 检查是否有对应的模拟API
            if capability in self.mock_apis:
                api_info = self.mock_apis[capability]
                print(f"📡 自动提供 {capability} API信息")
                print(f"API类型: {api_info['type']}")
                return api_info
            else:
                print(f"❌ 没有 {capability} 的API信息")
                return None
                
        return None
        
    async def run_demo_tasks(self):
        """运行演示任务"""
        print("📋 开始演示任务序列...\n")
        
        # 任务1: 搜索任务（需要web_search能力）
        await self.demo_task_1()
        
        # 任务2: 天气查询（需要weather_api能力）
        await self.demo_task_2()
        
        # 任务3: 文件操作（需要file_operations能力）
        await self.demo_task_3()
        
        # 任务4: 复合任务（需要多个能力）
        await self.demo_task_4()
        
        # 显示学习结果
        await self.show_learning_results()
        
    async def demo_task_1(self):
        """演示任务1: 搜索最新AI新闻"""
        print("=" * 60)
        print("📰 任务1: 搜索最新AI新闻")
        print("=" * 60)
        
        task_description = "搜索最新的人工智能新闻"
        
        print(f"任务描述: {task_description}")
        print("预期: 智能体发现缺少web_search能力，自动请求并获得该能力\n")
        
        result = await self.agent.execute_task(task_description)
        
        if result["status"] == "success":
            print("✅ 任务1完成!")
            print(f"结果: {result.get('result', '无返回结果')}")
        else:
            print("❌ 任务1失败!")
            print(f"错误: {result.get('error', '未知错误')}")
            
        print(f"\n当前可用能力: {self.agent.get_available_capabilities()}")
        print("\n" + "=" * 60 + "\n")
        
    async def demo_task_2(self):
        """演示任务2: 查询天气"""
        print("🌤️ 任务2: 查询北京天气")
        print("=" * 60)
        
        task_description = "查询北京今天的天气情况"
        
        print(f"任务描述: {task_description}")
        print("预期: 智能体发现缺少weather_api能力，自动请求并获得该能力\n")
        
        result = await self.agent.execute_task(task_description)
        
        if result["status"] == "success":
            print("✅ 任务2完成!")
            print(f"结果: {result.get('result', '无返回结果')}")
        else:
            print("❌ 任务2失败!")
            print(f"错误: {result.get('error', '未知错误')}")
            
        print(f"\n当前可用能力: {self.agent.get_available_capabilities()}")
        print("\n" + "=" * 60 + "\n")
        
    async def demo_task_3(self):
        """演示任务3: 保存搜索结果"""
        print("💾 任务3: 保存搜索结果到文件")
        print("=" * 60)
        
        task_description = "将搜索到的AI新闻保存到文件中"
        
        print(f"任务描述: {task_description}")
        print("预期: 智能体发现缺少file_operations能力，自动请求并获得该能力\n")
        
        result = await self.agent.execute_task(task_description)
        
        if result["status"] == "success":
            print("✅ 任务3完成!")
            print(f"结果: {result.get('result', '无返回结果')}")
        else:
            print("❌ 任务3失败!")
            print(f"错误: {result.get('error', '未知错误')}")
            
        print(f"\n当前可用能力: {self.agent.get_available_capabilities()}")
        print("\n" + "=" * 60 + "\n")
        
    async def demo_task_4(self):
        """演示任务4: 复合任务"""
        print("🔄 任务4: 复合任务 - 搜索天气并保存报告")
        print("=" * 60)
        
        task_description = "搜索上海的天气预报，并将结果保存为天气报告文件"
        
        print(f"任务描述: {task_description}")
        print("预期: 智能体使用已有的多个能力完成复合任务\n")
        
        result = await self.agent.execute_task(task_description)
        
        if result["status"] == "success":
            print("✅ 任务4完成!")
            print(f"结果: {result.get('result', '无返回结果')}")
        else:
            print("❌ 任务4失败!")
            print(f"错误: {result.get('error', '未知错误')}")
            
        print(f"\n当前可用能力: {self.agent.get_available_capabilities()}")
        print("\n" + "=" * 60 + "\n")
        
    async def show_learning_results(self):
        """显示学习结果"""
        print("📊 学习结果分析")
        print("=" * 60)
        
        # 任务历史
        history = self.agent.get_task_history()
        print(f"总任务数: {len(history)}")
        
        successful_tasks = [t for t in history if t["status"] == "completed"]
        print(f"成功任务: {len(successful_tasks)}")
        print(f"成功率: {len(successful_tasks)/len(history)*100:.1f}%")
        
        # 能力获得情况
        capabilities = self.agent.get_available_capabilities()
        print(f"\n获得的能力: {len(capabilities)} 个")
        for cap in capabilities:
            print(f"  ✓ {cap}")
            
        # 学习洞察
        insights = await self.agent.experience_recorder.get_learning_insights()
        print(f"\n学习洞察:")
        print(f"  任务成功率: {insights['task_success_rate']:.1%}")
        
        if insights['improvement_suggestions']:
            print(f"\n改进建议:")
            for suggestion in insights['improvement_suggestions']:
                print(f"  💡 {suggestion}")
                
        print("\n" + "=" * 60)
        
    async def cleanup(self):
        """清理资源"""
        if self.hot_reloader:
            self.hot_reloader.stop_monitoring()


async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    demo = DemoScenario()
    
    try:
        await demo.initialize()
        await demo.run_demo_tasks()
        
        print("\n🎉 演示完成!")
        print("智能体已成功展示了自进化能力:")
        print("1. 自动识别缺失的功能")
        print("2. 请求用户提供API")
        print("3. 自动生成功能模块")
        print("4. 热更新集成新功能")
        print("5. 使用新功能完成任务")
        print("6. 学习和优化执行策略")
        
    except KeyboardInterrupt:
        print("\n演示被中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await demo.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
