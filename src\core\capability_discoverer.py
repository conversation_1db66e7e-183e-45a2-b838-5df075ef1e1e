"""
能力发现器 - 分析任务并识别所需能力
"""
import re
import logging
from typing import List, Dict, Any, Set
from dataclasses import dataclass
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from src.llm.deepseek_client import get_deepseek_client
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False


@dataclass
class CapabilityPattern:
    """能力模式定义"""
    name: str
    keywords: List[str]
    patterns: List[str]
    description: str
    priority: int = 1


class CapabilityDiscoverer:
    """
    能力发现器
    
    通过分析任务描述，识别完成任务所需的能力
    使用关键词匹配、正则表达式和语义分析
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.capability_patterns = self._load_capability_patterns()
        
    def _load_capability_patterns(self) -> List[CapabilityPattern]:
        """加载预定义的能力模式"""
        patterns = [
            CapabilityPattern(
                name="web_search",
                keywords=["搜索", "查找", "search", "find", "google", "百度"],
                patterns=[
                    r"搜索.*?信息",
                    r"查找.*?资料",
                    r"search.*?for",
                    r"find.*?information"
                ],
                description="网络搜索能力",
                priority=2
            ),
            CapabilityPattern(
                name="web_scraping",
                keywords=["爬取", "抓取", "scrape", "crawl", "网页", "webpage"],
                patterns=[
                    r"爬取.*?网页",
                    r"抓取.*?数据",
                    r"scrape.*?website",
                    r"crawl.*?data"
                ],
                description="网页爬虫能力",
                priority=2
            ),
            CapabilityPattern(
                name="file_operations",
                keywords=["文件", "保存", "读取", "file", "save", "read", "write"],
                patterns=[
                    r"保存.*?文件",
                    r"读取.*?文件",
                    r"save.*?file",
                    r"read.*?file"
                ],
                description="文件操作能力",
                priority=1
            ),
            CapabilityPattern(
                name="data_analysis",
                keywords=["分析", "统计", "analyze", "statistics", "数据", "data"],
                patterns=[
                    r"分析.*?数据",
                    r"统计.*?信息",
                    r"analyze.*?data",
                    r"statistical.*?analysis"
                ],
                description="数据分析能力",
                priority=2
            ),
            CapabilityPattern(
                name="image_processing",
                keywords=["图片", "图像", "image", "picture", "photo", "处理"],
                patterns=[
                    r"处理.*?图片",
                    r"生成.*?图像",
                    r"process.*?image",
                    r"generate.*?picture"
                ],
                description="图像处理能力",
                priority=3
            ),
            CapabilityPattern(
                name="email_operations",
                keywords=["邮件", "email", "发送", "send", "接收", "receive"],
                patterns=[
                    r"发送.*?邮件",
                    r"接收.*?邮件",
                    r"send.*?email",
                    r"receive.*?email"
                ],
                description="邮件操作能力",
                priority=2
            ),
            CapabilityPattern(
                name="database_operations",
                keywords=["数据库", "database", "sql", "查询", "query", "存储"],
                patterns=[
                    r"查询.*?数据库",
                    r"存储.*?数据",
                    r"database.*?query",
                    r"sql.*?operation"
                ],
                description="数据库操作能力",
                priority=2
            ),
            CapabilityPattern(
                name="api_integration",
                keywords=["api", "接口", "调用", "call", "集成", "integration"],
                patterns=[
                    r"调用.*?api",
                    r"集成.*?接口",
                    r"call.*?api",
                    r"api.*?integration"
                ],
                description="API集成能力",
                priority=2
            ),
            CapabilityPattern(
                name="natural_language_processing",
                keywords=["翻译", "translate", "语言", "language", "文本", "text"],
                patterns=[
                    r"翻译.*?文本",
                    r"处理.*?语言",
                    r"translate.*?text",
                    r"language.*?processing"
                ],
                description="自然语言处理能力",
                priority=2
            ),
            CapabilityPattern(
                name="scheduling",
                keywords=["定时", "计划", "schedule", "timer", "任务", "task"],
                patterns=[
                    r"定时.*?执行",
                    r"计划.*?任务",
                    r"schedule.*?task",
                    r"timer.*?job"
                ],
                description="任务调度能力",
                priority=2
            )
        ]
        return patterns
        
    async def analyze_task(self, task_description: str) -> List[str]:
        """
        分析任务描述，识别所需能力
        
        Args:
            task_description: 任务描述
            
        Returns:
            所需能力列表
        """
        self.logger.info(f"分析任务所需能力: {task_description}")
        
        required_capabilities = set()

        # 1. 优先使用 LLM 分析（如果可用）
        if LLM_AVAILABLE:
            try:
                llm_capabilities = await self._llm_analysis(task_description)
                if llm_capabilities:
                    required_capabilities.update(llm_capabilities)
                    self.logger.info(f"LLM识别能力: {llm_capabilities}")
            except Exception as e:
                self.logger.warning(f"LLM分析失败，使用传统方法: {e}")

        # 2. 关键词匹配（作为补充或备用）
        keyword_capabilities = self._match_by_keywords(task_description)
        required_capabilities.update(keyword_capabilities)

        # 3. 正则表达式匹配
        pattern_capabilities = self._match_by_patterns(task_description)
        required_capabilities.update(pattern_capabilities)

        # 4. 语义分析（简化版）
        semantic_capabilities = self._semantic_analysis(task_description)
        required_capabilities.update(semantic_capabilities)

        # 5. 按优先级排序
        sorted_capabilities = self._sort_by_priority(list(required_capabilities))
        
        self.logger.info(f"识别到所需能力: {sorted_capabilities}")
        return sorted_capabilities

    async def _llm_analysis(self, task_description: str) -> List[str]:
        """使用 LLM 进行智能能力分析"""
        if not LLM_AVAILABLE:
            return []

        try:
            client = await get_deepseek_client()
            async with client:
                capabilities = await client.analyze_task_capabilities(task_description)
                return capabilities
        except Exception as e:
            self.logger.error(f"LLM分析异常: {e}")
            return []

    def _match_by_keywords(self, text: str) -> Set[str]:
        """通过关键词匹配能力"""
        text_lower = text.lower()
        matched_capabilities = set()
        
        for pattern in self.capability_patterns:
            for keyword in pattern.keywords:
                if keyword.lower() in text_lower:
                    matched_capabilities.add(pattern.name)
                    break
                    
        return matched_capabilities
        
    def _match_by_patterns(self, text: str) -> Set[str]:
        """通过正则表达式匹配能力"""
        matched_capabilities = set()
        
        for pattern in self.capability_patterns:
            for regex_pattern in pattern.patterns:
                if re.search(regex_pattern, text, re.IGNORECASE):
                    matched_capabilities.add(pattern.name)
                    break
                    
        return matched_capabilities
        
    def _semantic_analysis(self, text: str) -> Set[str]:
        """语义分析（简化版）"""
        # 这里可以集成更复杂的NLP模型
        # 目前使用简单的规则
        semantic_capabilities = set()
        
        # 检查是否需要外部数据
        if any(word in text.lower() for word in ["最新", "实时", "当前", "latest", "current", "real-time"]):
            semantic_capabilities.add("web_search")
            
        # 检查是否需要数据处理
        if any(word in text.lower() for word in ["整理", "汇总", "报告", "organize", "summarize", "report"]):
            semantic_capabilities.add("data_analysis")
            
        # 检查是否需要持久化
        if any(word in text.lower() for word in ["保存", "记录", "存储", "save", "record", "store"]):
            semantic_capabilities.add("file_operations")
            
        return semantic_capabilities
        
    def _sort_by_priority(self, capabilities: List[str]) -> List[str]:
        """按优先级排序能力"""
        capability_priority = {}
        
        for pattern in self.capability_patterns:
            if pattern.name in capabilities:
                capability_priority[pattern.name] = pattern.priority
                
        # 按优先级降序排序
        sorted_capabilities = sorted(
            capabilities,
            key=lambda x: capability_priority.get(x, 0),
            reverse=True
        )
        
        return sorted_capabilities
        
    def add_capability_pattern(self, pattern: CapabilityPattern):
        """添加新的能力模式"""
        self.capability_patterns.append(pattern)
        self.logger.info(f"添加新能力模式: {pattern.name}")
        
    def get_capability_description(self, capability_name: str) -> str:
        """获取能力描述"""
        for pattern in self.capability_patterns:
            if pattern.name == capability_name:
                return pattern.description
        return f"未知能力: {capability_name}"
        
    def list_known_capabilities(self) -> List[Dict[str, Any]]:
        """列出所有已知的能力模式"""
        return [
            {
                "name": pattern.name,
                "description": pattern.description,
                "keywords": pattern.keywords,
                "priority": pattern.priority
            }
            for pattern in self.capability_patterns
        ]
