"""
工作流优化器
根据执行结果和性能数据自动优化工作流
"""
import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .dynamic_workflow_generator import GeneratedWorkflow, WorkflowNode, WorkflowEdge, WorkflowPattern
from .agent_state import AgentState, ExecutionMetrics


class OptimizationType(Enum):
    """优化类型枚举"""
    PERFORMANCE = "performance"  # 性能优化
    RELIABILITY = "reliability"  # 可靠性优化
    EFFICIENCY = "efficiency"  # 效率优化
    QUALITY = "quality"  # 质量优化
    COST = "cost"  # 成本优化


class OptimizationStrategy(Enum):
    """优化策略枚举"""
    AGGRESSIVE = "aggressive"  # 激进优化
    CONSERVATIVE = "conservative"  # 保守优化
    BALANCED = "balanced"  # 平衡优化
    ADAPTIVE = "adaptive"  # 自适应优化


@dataclass
class OptimizationRule:
    """优化规则"""
    name: str
    condition: str
    action: str
    priority: int
    optimization_type: OptimizationType
    expected_improvement: float
    risk_level: str  # low, medium, high
    description: str


@dataclass
class OptimizationResult:
    """优化结果"""
    optimization_id: str
    original_workflow: GeneratedWorkflow
    optimized_workflow: GeneratedWorkflow
    applied_rules: List[OptimizationRule]
    performance_improvement: Dict[str, float]
    optimization_score: float
    risk_assessment: Dict[str, Any]
    recommendations: List[str]


class WorkflowOptimizer:
    """工作流优化器"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.logger = logging.getLogger(__name__)
        
        # 优化规则库
        self.optimization_rules = self._init_optimization_rules()
        
        # 性能基准
        self.performance_baselines = self._init_performance_baselines()
        
        # 学习历史
        self.optimization_history = []
    
    def _init_optimization_rules(self) -> List[OptimizationRule]:
        """初始化优化规则库"""
        return [
            OptimizationRule(
                name="parallel_execution",
                condition="independent_nodes_exist",
                action="add_parallel_groups",
                priority=1,
                optimization_type=OptimizationType.PERFORMANCE,
                expected_improvement=0.3,
                risk_level="low",
                description="将独立的节点并行执行以提高性能"
            ),
            OptimizationRule(
                name="remove_redundant_validation",
                condition="multiple_validation_nodes",
                action="merge_validation_steps",
                priority=2,
                optimization_type=OptimizationType.EFFICIENCY,
                expected_improvement=0.2,
                risk_level="medium",
                description="合并冗余的验证步骤"
            ),
            OptimizationRule(
                name="cache_expensive_operations",
                condition="repeated_expensive_operations",
                action="add_caching_layer",
                priority=3,
                optimization_type=OptimizationType.PERFORMANCE,
                expected_improvement=0.4,
                risk_level="low",
                description="为昂贵操作添加缓存层"
            ),
            OptimizationRule(
                name="optimize_retry_strategy",
                condition="high_failure_rate",
                action="adjust_retry_parameters",
                priority=4,
                optimization_type=OptimizationType.RELIABILITY,
                expected_improvement=0.25,
                risk_level="low",
                description="优化重试策略以提高可靠性"
            ),
            OptimizationRule(
                name="reduce_api_calls",
                condition="excessive_api_usage",
                action="batch_api_requests",
                priority=5,
                optimization_type=OptimizationType.COST,
                expected_improvement=0.35,
                risk_level="medium",
                description="批量处理API请求以降低成本"
            ),
            OptimizationRule(
                name="early_termination",
                condition="predictable_failure_patterns",
                action="add_early_exit_conditions",
                priority=6,
                optimization_type=OptimizationType.EFFICIENCY,
                expected_improvement=0.3,
                risk_level="medium",
                description="添加早期终止条件避免无效执行"
            ),
            OptimizationRule(
                name="dynamic_timeout_adjustment",
                condition="timeout_issues",
                action="implement_adaptive_timeouts",
                priority=7,
                optimization_type=OptimizationType.RELIABILITY,
                expected_improvement=0.2,
                risk_level="low",
                description="实现自适应超时机制"
            ),
            OptimizationRule(
                name="resource_pooling",
                condition="resource_contention",
                action="implement_resource_pools",
                priority=8,
                optimization_type=OptimizationType.PERFORMANCE,
                expected_improvement=0.25,
                risk_level="medium",
                description="实现资源池以减少资源竞争"
            )
        ]
    
    def _init_performance_baselines(self) -> Dict[str, float]:
        """初始化性能基准"""
        return {
            "average_execution_time": 60.0,  # 秒
            "success_rate": 0.85,
            "api_call_efficiency": 0.7,
            "resource_utilization": 0.6,
            "error_rate": 0.15,
            "retry_rate": 0.1
        }
    
    async def optimize_workflow(self, workflow: GeneratedWorkflow, 
                              execution_history: List[Dict[str, Any]],
                              strategy: OptimizationStrategy = OptimizationStrategy.BALANCED) -> OptimizationResult:
        """优化工作流"""
        self.logger.info(f"🔧 开始优化工作流: {workflow.name}")
        
        try:
            # 1. 分析执行历史
            performance_analysis = self._analyze_execution_history(execution_history)
            
            # 2. 识别优化机会
            optimization_opportunities = self._identify_optimization_opportunities(
                workflow, performance_analysis
            )
            
            # 3. 选择优化规则
            applicable_rules = self._select_optimization_rules(
                optimization_opportunities, strategy
            )
            
            # 4. 应用优化
            optimized_workflow = await self._apply_optimizations(
                workflow, applicable_rules, performance_analysis
            )
            
            # 5. 评估优化效果
            optimization_score = self._calculate_optimization_score(
                workflow, optimized_workflow, applicable_rules
            )
            
            # 6. 生成建议
            recommendations = self._generate_recommendations(
                performance_analysis, applicable_rules
            )
            
            # 7. 创建优化结果
            result = OptimizationResult(
                optimization_id=f"opt_{int(time.time())}",
                original_workflow=workflow,
                optimized_workflow=optimized_workflow,
                applied_rules=applicable_rules,
                performance_improvement=self._estimate_performance_improvement(applicable_rules),
                optimization_score=optimization_score,
                risk_assessment=self._assess_optimization_risks(applicable_rules),
                recommendations=recommendations
            )
            
            # 8. 记录优化历史
            self.optimization_history.append({
                "timestamp": time.time(),
                "workflow_id": workflow.workflow_id,
                "optimization_id": result.optimization_id,
                "applied_rules": [rule.name for rule in applicable_rules],
                "performance_improvement": result.performance_improvement,
                "optimization_score": optimization_score
            })
            
            self.logger.info(f"✅ 工作流优化完成，评分: {optimization_score:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"工作流优化失败: {e}")
            # 返回原始工作流
            return OptimizationResult(
                optimization_id="failed_optimization",
                original_workflow=workflow,
                optimized_workflow=workflow,
                applied_rules=[],
                performance_improvement={},
                optimization_score=0.0,
                risk_assessment={"error": str(e)},
                recommendations=["优化失败，建议检查执行历史数据"]
            )
    
    def _analyze_execution_history(self, execution_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析执行历史"""
        if not execution_history:
            return {"error": "无执行历史数据"}
        
        analysis = {
            "total_executions": len(execution_history),
            "success_rate": 0.0,
            "average_duration": 0.0,
            "error_patterns": {},
            "performance_trends": {},
            "bottlenecks": [],
            "resource_usage": {}
        }
        
        successful_executions = 0
        total_duration = 0.0
        error_counts = {}
        node_durations = {}
        
        for execution in execution_history:
            # 成功率统计
            if execution.get("success", False):
                successful_executions += 1
            
            # 执行时间统计
            duration = execution.get("duration", 0)
            total_duration += duration
            
            # 错误模式统计
            errors = execution.get("errors", [])
            for error in errors:
                error_type = error.get("type", "unknown")
                error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            # 节点性能统计
            node_metrics = execution.get("node_metrics", {})
            for node_name, metrics in node_metrics.items():
                if node_name not in node_durations:
                    node_durations[node_name] = []
                node_durations[node_name].append(metrics.get("duration", 0))
        
        # 计算统计结果
        analysis["success_rate"] = successful_executions / len(execution_history)
        analysis["average_duration"] = total_duration / len(execution_history)
        analysis["error_patterns"] = error_counts
        
        # 识别瓶颈节点
        for node_name, durations in node_durations.items():
            avg_duration = sum(durations) / len(durations)
            if avg_duration > analysis["average_duration"] * 0.3:  # 超过30%的总时间
                analysis["bottlenecks"].append({
                    "node": node_name,
                    "average_duration": avg_duration,
                    "impact": avg_duration / analysis["average_duration"]
                })
        
        return analysis
    
    def _identify_optimization_opportunities(self, workflow: GeneratedWorkflow, 
                                           performance_analysis: Dict[str, Any]) -> List[str]:
        """识别优化机会"""
        opportunities = []
        
        # 检查并行机会
        independent_nodes = self._find_independent_nodes(workflow)
        if len(independent_nodes) > 1:
            opportunities.append("independent_nodes_exist")
        
        # 检查冗余验证
        validation_nodes = [n for n in workflow.nodes if "validation" in n.name.lower()]
        if len(validation_nodes) > 2:
            opportunities.append("multiple_validation_nodes")
        
        # 检查性能问题
        success_rate = performance_analysis.get("success_rate", 1.0)
        if success_rate < 0.8:
            opportunities.append("high_failure_rate")
        
        avg_duration = performance_analysis.get("average_duration", 0)
        baseline_duration = self.performance_baselines["average_execution_time"]
        if avg_duration > baseline_duration * 1.5:
            opportunities.append("performance_issues")
        
        # 检查API使用
        api_nodes = [n for n in workflow.nodes if "api" in n.name.lower() or "search" in n.name.lower()]
        if len(api_nodes) > 3:
            opportunities.append("excessive_api_usage")
        
        # 检查错误模式
        error_patterns = performance_analysis.get("error_patterns", {})
        if any(count > len(performance_analysis.get("total_executions", 1)) * 0.2 for count in error_patterns.values()):
            opportunities.append("predictable_failure_patterns")
        
        # 检查瓶颈
        bottlenecks = performance_analysis.get("bottlenecks", [])
        if bottlenecks:
            opportunities.append("performance_bottlenecks")
        
        return opportunities
    
    def _find_independent_nodes(self, workflow: GeneratedWorkflow) -> List[WorkflowNode]:
        """查找独立的节点"""
        independent_nodes = []
        
        for node in workflow.nodes:
            # 检查节点是否有共同的依赖
            has_shared_dependencies = False
            for other_node in workflow.nodes:
                if node != other_node:
                    shared_deps = set(node.dependencies) & set(other_node.dependencies)
                    if shared_deps and not node.dependencies and not other_node.dependencies:
                        has_shared_dependencies = True
                        break
            
            if not has_shared_dependencies and not node.dependencies:
                independent_nodes.append(node)
        
        return independent_nodes
    
    def _select_optimization_rules(self, opportunities: List[str], 
                                 strategy: OptimizationStrategy) -> List[OptimizationRule]:
        """选择适用的优化规则"""
        applicable_rules = []
        
        for rule in self.optimization_rules:
            if rule.condition in opportunities:
                # 根据策略过滤规则
                if strategy == OptimizationStrategy.CONSERVATIVE and rule.risk_level == "high":
                    continue
                elif strategy == OptimizationStrategy.AGGRESSIVE:
                    applicable_rules.append(rule)
                elif strategy == OptimizationStrategy.BALANCED and rule.risk_level != "high":
                    applicable_rules.append(rule)
                elif strategy == OptimizationStrategy.ADAPTIVE:
                    # 自适应策略基于历史成功率
                    if self._should_apply_adaptive_rule(rule):
                        applicable_rules.append(rule)
        
        # 按优先级排序
        applicable_rules.sort(key=lambda r: r.priority)
        
        return applicable_rules
    
    def _should_apply_adaptive_rule(self, rule: OptimizationRule) -> bool:
        """判断是否应该应用自适应规则"""
        # 检查历史优化成功率
        rule_history = [h for h in self.optimization_history 
                       if rule.name in h.get("applied_rules", [])]
        
        if not rule_history:
            return rule.risk_level != "high"  # 没有历史数据时避免高风险规则
        
        success_count = sum(1 for h in rule_history 
                          if h.get("optimization_score", 0) > 0.5)
        success_rate = success_count / len(rule_history)
        
        return success_rate > 0.6  # 成功率超过60%才应用
    
    async def _apply_optimizations(self, workflow: GeneratedWorkflow, 
                                 rules: List[OptimizationRule],
                                 performance_analysis: Dict[str, Any]) -> GeneratedWorkflow:
        """应用优化规则"""
        optimized_workflow = workflow  # 创建副本
        
        for rule in rules:
            try:
                if rule.action == "add_parallel_groups":
                    optimized_workflow = self._add_parallel_execution(optimized_workflow)
                elif rule.action == "merge_validation_steps":
                    optimized_workflow = self._merge_validation_nodes(optimized_workflow)
                elif rule.action == "add_caching_layer":
                    optimized_workflow = self._add_caching_layer(optimized_workflow)
                elif rule.action == "adjust_retry_parameters":
                    optimized_workflow = self._optimize_retry_strategy(optimized_workflow)
                elif rule.action == "batch_api_requests":
                    optimized_workflow = self._batch_api_requests(optimized_workflow)
                elif rule.action == "add_early_exit_conditions":
                    optimized_workflow = self._add_early_termination(optimized_workflow)
                elif rule.action == "implement_adaptive_timeouts":
                    optimized_workflow = self._implement_adaptive_timeouts(optimized_workflow)
                elif rule.action == "implement_resource_pools":
                    optimized_workflow = self._implement_resource_pooling(optimized_workflow)
                
                self.logger.info(f"✅ 应用优化规则: {rule.name}")
                
            except Exception as e:
                self.logger.error(f"优化规则应用失败 {rule.name}: {e}")
        
        return optimized_workflow
    
    def _add_parallel_execution(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """添加并行执行"""
        # 找到可以并行的节点
        independent_nodes = self._find_independent_nodes(workflow)
        
        if len(independent_nodes) > 1:
            # 为独立节点添加并行组
            for i, node in enumerate(independent_nodes):
                node.parallel_group = f"parallel_group_{i}"
        
        return workflow
    
    def _merge_validation_nodes(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """合并验证节点"""
        validation_nodes = [n for n in workflow.nodes if "validation" in n.name.lower()]
        
        if len(validation_nodes) > 1:
            # 保留第一个验证节点，合并其他节点的功能
            primary_validation = validation_nodes[0]
            primary_validation.description += " (合并多个验证步骤)"
            
            # 移除其他验证节点
            workflow.nodes = [n for n in workflow.nodes if n not in validation_nodes[1:]]
            
            # 更新边连接
            for edge in workflow.edges:
                if edge.from_node in [n.name for n in validation_nodes[1:]]:
                    edge.from_node = primary_validation.name
                if edge.to_node in [n.name for n in validation_nodes[1:]]:
                    edge.to_node = primary_validation.name
        
        return workflow
    
    def _add_caching_layer(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """添加缓存层"""
        # 为昂贵的操作添加缓存参数
        expensive_operations = ["generation", "search", "analysis"]
        
        for node in workflow.nodes:
            if any(op in node.name.lower() for op in expensive_operations):
                node.parameters["enable_cache"] = True
                node.parameters["cache_ttl"] = 3600  # 1小时缓存
        
        return workflow
    
    def _optimize_retry_strategy(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """优化重试策略"""
        for node in workflow.nodes:
            # 根据节点类型调整重试参数
            if node.node_type in ["generation", "integration"]:
                node.retry_config["max_attempts"] = 5
                node.retry_config["backoff_factor"] = 1.5
            elif node.node_type in ["search", "validation"]:
                node.retry_config["max_attempts"] = 3
                node.retry_config["backoff_factor"] = 2.0
        
        return workflow
    
    def _batch_api_requests(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """批量API请求"""
        api_nodes = [n for n in workflow.nodes if "api" in n.name.lower() or "search" in n.name.lower()]
        
        for node in api_nodes:
            node.parameters["batch_size"] = 10
            node.parameters["batch_timeout"] = 30
        
        return workflow
    
    def _add_early_termination(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """添加早期终止"""
        for node in workflow.nodes:
            if node.node_type in ["validation", "analysis"]:
                node.parameters["early_exit_threshold"] = 0.3
                node.parameters["enable_early_termination"] = True
        
        return workflow
    
    def _implement_adaptive_timeouts(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """实现自适应超时"""
        for node in workflow.nodes:
            base_timeout = node.parameters.get("timeout", 300)
            node.parameters["adaptive_timeout"] = True
            node.parameters["min_timeout"] = base_timeout * 0.5
            node.parameters["max_timeout"] = base_timeout * 2.0
        
        return workflow
    
    def _implement_resource_pooling(self, workflow: GeneratedWorkflow) -> GeneratedWorkflow:
        """实现资源池"""
        for node in workflow.nodes:
            if node.node_type in ["generation", "integration"]:
                node.parameters["use_resource_pool"] = True
                node.parameters["pool_size"] = 5
        
        return workflow
    
    def _calculate_optimization_score(self, original: GeneratedWorkflow, 
                                    optimized: GeneratedWorkflow, 
                                    rules: List[OptimizationRule]) -> float:
        """计算优化评分"""
        if not rules:
            return 0.0
        
        # 基于应用的规则计算预期改进
        total_improvement = sum(rule.expected_improvement for rule in rules)
        
        # 考虑风险因素
        risk_penalty = 0.0
        for rule in rules:
            if rule.risk_level == "high":
                risk_penalty += 0.1
            elif rule.risk_level == "medium":
                risk_penalty += 0.05
        
        # 计算最终评分
        score = min(1.0, total_improvement - risk_penalty)
        return max(0.0, score)
    
    def _estimate_performance_improvement(self, rules: List[OptimizationRule]) -> Dict[str, float]:
        """估算性能改进"""
        improvements = {
            "execution_time": 0.0,
            "success_rate": 0.0,
            "resource_efficiency": 0.0,
            "cost_reduction": 0.0
        }
        
        for rule in rules:
            if rule.optimization_type == OptimizationType.PERFORMANCE:
                improvements["execution_time"] += rule.expected_improvement * 0.3
            elif rule.optimization_type == OptimizationType.RELIABILITY:
                improvements["success_rate"] += rule.expected_improvement * 0.2
            elif rule.optimization_type == OptimizationType.EFFICIENCY:
                improvements["resource_efficiency"] += rule.expected_improvement * 0.25
            elif rule.optimization_type == OptimizationType.COST:
                improvements["cost_reduction"] += rule.expected_improvement * 0.4
        
        return improvements
    
    def _assess_optimization_risks(self, rules: List[OptimizationRule]) -> Dict[str, Any]:
        """评估优化风险"""
        risk_assessment = {
            "overall_risk": "low",
            "risk_factors": [],
            "mitigation_strategies": []
        }
        
        high_risk_count = sum(1 for rule in rules if rule.risk_level == "high")
        medium_risk_count = sum(1 for rule in rules if rule.risk_level == "medium")
        
        if high_risk_count > 0:
            risk_assessment["overall_risk"] = "high"
            risk_assessment["risk_factors"].append(f"{high_risk_count} 个高风险优化")
        elif medium_risk_count > 2:
            risk_assessment["overall_risk"] = "medium"
            risk_assessment["risk_factors"].append(f"{medium_risk_count} 个中等风险优化")
        
        # 添加缓解策略
        if risk_assessment["overall_risk"] != "low":
            risk_assessment["mitigation_strategies"].extend([
                "建议分阶段应用优化",
                "密切监控优化后的性能表现",
                "准备回滚方案"
            ])
        
        return risk_assessment
    
    def _generate_recommendations(self, performance_analysis: Dict[str, Any], 
                                rules: List[OptimizationRule]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于性能分析的建议
        success_rate = performance_analysis.get("success_rate", 1.0)
        if success_rate < 0.8:
            recommendations.append("建议重点关注错误处理和重试机制")
        
        avg_duration = performance_analysis.get("average_duration", 0)
        if avg_duration > self.performance_baselines["average_execution_time"]:
            recommendations.append("建议优化性能瓶颈节点")
        
        # 基于应用规则的建议
        if any(rule.optimization_type == OptimizationType.PERFORMANCE for rule in rules):
            recommendations.append("性能优化已应用，建议监控执行时间变化")
        
        if any(rule.optimization_type == OptimizationType.RELIABILITY for rule in rules):
            recommendations.append("可靠性优化已应用，建议监控成功率变化")
        
        # 通用建议
        recommendations.extend([
            "建议定期收集执行数据以持续优化",
            "考虑根据实际使用场景调整优化策略"
        ])
        
        return recommendations
