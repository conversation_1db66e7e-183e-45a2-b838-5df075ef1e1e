"""
file_operations 能力模块
自动生成于: 2024-01-01T00:00:00
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import csv


class FileOperationsCapability:
    """
    文件操作能力
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_dir = Path("data/files")
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
    async def save_file(self, filename: str, content: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        保存文件
        
        Args:
            filename: 文件名
            content: 文件内容
            context: 执行上下文
            
        Returns:
            保存结果
        """
        self.logger.info(f"执行 save_file")
        
        try:
            file_path = self.base_dir / filename
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            self.logger.info(f"文件保存成功: {file_path}")
            
            return {
                "status": "success",
                "message": f"文件已保存到 {file_path}",
                "file_path": str(file_path),
                "file_size": len(content)
            }
            
        except Exception as e:
            self.logger.error(f"save_file 执行失败: {e}")
            raise
            
    async def read_file(self, filename: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        读取文件
        
        Args:
            filename: 文件名
            context: 执行上下文
            
        Returns:
            文件内容
        """
        self.logger.info(f"执行 read_file")
        
        try:
            file_path = self.base_dir / filename
            
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.logger.info(f"文件读取成功: {file_path}")
            
            return {
                "status": "success",
                "content": content,
                "file_path": str(file_path),
                "file_size": len(content)
            }
            
        except Exception as e:
            self.logger.error(f"read_file 执行失败: {e}")
            raise
            
    async def list_files(self, pattern: str = "*", context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        列出文件
        
        Args:
            pattern: 文件模式
            context: 执行上下文
            
        Returns:
            文件列表
        """
        self.logger.info(f"执行 list_files")
        
        try:
            files = list(self.base_dir.glob(pattern))
            file_info = []
            
            for file_path in files:
                if file_path.is_file():
                    stat = file_path.stat()
                    file_info.append({
                        "name": file_path.name,
                        "path": str(file_path),
                        "size": stat.st_size,
                        "modified": stat.st_mtime
                    })
                    
            return {
                "status": "success",
                "files": file_info,
                "count": len(file_info)
            }
            
        except Exception as e:
            self.logger.error(f"list_files 执行失败: {e}")
            raise
            
    async def delete_file(self, filename: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            filename: 文件名
            context: 执行上下文
            
        Returns:
            删除结果
        """
        self.logger.info(f"执行 delete_file")
        
        try:
            file_path = self.base_dir / filename
            
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            file_path.unlink()
            
            self.logger.info(f"文件删除成功: {file_path}")
            
            return {
                "status": "success",
                "message": f"文件已删除: {file_path}",
                "file_path": str(file_path)
            }
            
        except Exception as e:
            self.logger.error(f"delete_file 执行失败: {e}")
            raise
            
    async def save_json(self, filename: str, data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        保存JSON文件
        
        Args:
            filename: 文件名
            data: JSON数据
            context: 执行上下文
            
        Returns:
            保存结果
        """
        self.logger.info(f"执行 save_json")
        
        try:
            if not filename.endswith('.json'):
                filename += '.json'
                
            json_content = json.dumps(data, ensure_ascii=False, indent=2)
            return await self.save_file(filename, json_content, context)
            
        except Exception as e:
            self.logger.error(f"save_json 执行失败: {e}")
            raise
            
    async def read_json(self, filename: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        读取JSON文件
        
        Args:
            filename: 文件名
            context: 执行上下文
            
        Returns:
            JSON数据
        """
        self.logger.info(f"执行 read_json")
        
        try:
            if not filename.endswith('.json'):
                filename += '.json'
                
            result = await self.read_file(filename, context)
            
            if result["status"] == "success":
                data = json.loads(result["content"])
                result["data"] = data
                
            return result
            
        except Exception as e:
            self.logger.error(f"read_json 执行失败: {e}")
            raise
            
    async def execute(self, params: Dict[str, Any] = {}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行 file_operations 功能
        
        Args:
            params: 执行参数
            context: 执行上下文
            
        Returns:
            执行结果
        """
        self.logger.info(f"执行参数: {params}")
        
        action = params.get("action", "save_file")
        
        if action == "save_file":
            return await self.save_file(
                params.get("filename", "output.txt"),
                params.get("content", ""),
                context
            )
        elif action == "read_file":
            return await self.read_file(
                params.get("filename", ""),
                context
            )
        elif action == "list_files":
            return await self.list_files(
                params.get("pattern", "*"),
                context
            )
        elif action == "delete_file":
            return await self.delete_file(
                params.get("filename", ""),
                context
            )
        elif action == "save_json":
            return await self.save_json(
                params.get("filename", "data.json"),
                params.get("data", {}),
                context
            )
        elif action == "read_json":
            return await self.read_json(
                params.get("filename", "data.json"),
                context
            )
        else:
            return {
                "status": "error",
                "message": f"未知操作: {action}"
            }
