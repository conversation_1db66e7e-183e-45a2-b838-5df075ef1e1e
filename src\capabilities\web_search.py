"""
web_search 能力模块
自动生成于: 2025-06-30T14:02:04.407662
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional

import aiohttp

import json



class WebSearchCapability:
    """
    web_search 功能模块
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        self.base_url = "https://api.duckduckgo.com"
        
        self.session = None
        
        
    
    async def search(self, q: str, format: str = json, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        搜索网页内容
        
        Args:
            
            q: 搜索关键词
            
            format: 返回格式
            
            context: 执行上下文
            
        Returns:
            API响应结果
        """
        self.logger.info(f"执行 search")
        
        try:
            
            if not self.session:
            
                self.session = aiohttp.ClientSession()
            
            
            
            url = self.base_url + "/"
            
            method = "GET"
            
            
            
            headers = {}
            
            if hasattr(self, 'api_key'):
            
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            
            
            async with self.session.request(method, url, headers=headers) as response:
            
                if response.status == 200:
            
                    result = await response.json()
            
                    return result
            
                else:
            
                    raise Exception(f'API请求失败: {response.status}')
            
            
        except Exception as e:
            self.logger.error(f"search 执行失败: {e}")
            raise
            
    