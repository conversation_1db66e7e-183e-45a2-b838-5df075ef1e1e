import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional

class WebSearchCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://api.duckduckgo.com"
        self.default_params = {
            'format': 'json',
            'no_html': '1',
            'skip_disambig': '1'
        }
        
    async def _make_request(self, params: Dict[str, str]) -> Dict[str, Any]:
        """Internal method to make HTTP request to DuckDuckGo API"""
        try:
            async with aiohttp.ClientSession() as session:
                self.logger.debug(f"Making request to DuckDuckGo API with params: {params}")
                async with session.get(self.base_url, params=params) as response:
                    if response.status != 200:
                        error_msg = f"API request failed with status {response.status}"
                        self.logger.error(error_msg)
                        raise Exception(error_msg)
                    return await response.json()
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP client error occurred: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during API request: {str(e)}")
            raise

    async def search_web(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        Search the web using DuckDuckGo Instant Answer API
        
        Args:
            context: Optional context dictionary
            **kwargs: Arbitrary keyword arguments. Expected to contain 'query' or 'q' for search term.
        
        Returns:
            Dictionary containing search results from DuckDuckGo API
            
        Raises:
            ValueError: If no search query is provided
            Exception: For API request failures
        """
        self.logger.info("Starting web search")
        
        # Extract query from kwargs or context
        query = kwargs.get('query') or kwargs.get('q') or (context.get('query') if context else None)
        
        if not query:
            error_msg = "No search query provided"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
            
        params = self.default_params.copy()
        params['q'] = query
        
        try:
            self.logger.info(f"Searching for: {query}")
            result = await self._make_request(params)
            self.logger.debug(f"Received search results: {result}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to complete web search: {str(e)}")
            raise