import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional

class WebSearchCapability:
    def __init__(self, base_url: str = "https://api.example.com"):
        """
        Initialize the WebSearchCapability with API base URL.
        
        Args:
            base_url: The base URL of the web search API
        """
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)
        self.session = None
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    async def __aenter__(self):
        """Initialize aiohttp session when entering async context."""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close aiohttp session when exiting async context."""
        if self.session:
            await self.session.close()

    async def search_web(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        Perform a web search using the provided query.
        
        Args:
            context: Optional context dictionary
            **kwargs: Arbitrary keyword arguments, must include 'query' parameter
            
        Returns:
            Dictionary containing search results
            
        Raises:
            ValueError: If query parameter is missing
            aiohttp.ClientError: For HTTP request errors
            Exception: For other unexpected errors
        """
        if not kwargs.get('query'):
            error_msg = "Query parameter is required for web search"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            params = {
                'query': kwargs['query']
            }
            
            if context:
                self.logger.info(f"Executing web search with context: {context}")
            
            self.logger.info(f"Performing web search for query: {kwargs['query']}")
            
            async with self.session.get(
                f"{self.base_url}/action",
                params=params,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                response.raise_for_status()
                result = await response.json()
                
                self.logger.info(f"Successfully completed web search for query: {kwargs['query']}")
                return {
                    'status': 'success',
                    'data': result,
                    'query': kwargs['query']
                }
                
        except aiohttp.ClientError as e:
            error_msg = f"HTTP error occurred during web search: {str(e)}"
            self.logger.error(error_msg)
            return {
                'status': 'error',
                'error': error_msg,
                'query': kwargs.get('query', '')
            }
            
        except asyncio.TimeoutError:
            error_msg = "Request timed out during web search"
            self.logger.error(error_msg)
            return {
                'status': 'error',
                'error': error_msg,
                'query': kwargs.get('query', '')
            }
            
        except Exception as e:
            error_msg = f"Unexpected error during web search: {str(e)}"
            self.logger.error(error_msg)
            return {
                'status': 'error',
                'error': error_msg,
                'query': kwargs.get('query', '')
            }