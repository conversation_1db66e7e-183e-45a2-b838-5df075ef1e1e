import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional
from urllib.parse import urlencode

class WebSearchCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://api.duckduckgo.com"
        self.session = None
        self.headers = {
            "Accept": "application/json",
            "User-Agent": "WebSearchCapability/1.0"
        }

    async def initialize(self):
        """Initialize the aiohttp session"""
        if not self.session:
            self.session = aiohttp.ClientSession(headers=self.headers)
            self.logger.debug("Initialized aiohttp session")

    async def close(self):
        """Close the aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.debug("Closed aiohttp session")

    async def search_web(self, params: Dict[str, Any] = {}, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Perform a web search using DuckDuckGo API
        
        Args:
            params: Dictionary containing search parameters
                - q (str): Search query (required)
                - format (str): Response format (default: json)
            context: Additional context (not used in this implementation)
        
        Returns:
            Dictionary containing search results
            
        Raises:
            ValueError: If required parameters are missing
            aiohttp.ClientError: If there's an HTTP error
            Exception: For other unexpected errors
        """
        try:
            await self.initialize()
            
            # Validate parameters
            if 'q' not in params:
                raise ValueError("Missing required parameter 'q' (search query)")
                
            # Set default format if not provided
            params.setdefault('format', 'json')
            
            # Build query URL
            query_params = urlencode(params)
            url = f"{self.base_url}/?{query_params}"
            
            self.logger.info(f"Performing search with URL: {url}")
            
            async with self.session.get(url) as response:
                response.raise_for_status()
                content_type = response.headers.get('Content-Type', '')
                
                if 'application/json' in content_type:
                    result = await response.json()
                else:
                    result = await response.text()
                
                self.logger.debug(f"Received response: {result}")
                return {
                    'status': 'success',
                    'data': result
                }
                
        except ValueError as ve:
            self.logger.error(f"Parameter validation error: {str(ve)}")
            return {
                'status': 'error',
                'error': str(ve),
                'type': 'validation_error'
            }
        except aiohttp.ClientError as ce:
            self.logger.error(f"HTTP client error: {str(ce)}")
            return {
                'status': 'error',
                'error': str(ce),
                'type': 'http_error'
            }
        except Exception as e:
            self.logger.error(f"Unexpected error during search: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e),
                'type': 'unexpected_error'
            }

    async def __aenter__(self):
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()