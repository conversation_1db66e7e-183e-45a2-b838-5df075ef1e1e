"""
LangGraph Agent 节点定义
定义自进化智能体的各个处理节点
"""
import asyncio
import logging
import time
from typing import Dict, Any, List
from .agent_state import AgentState, TaskStatus, CapabilityStatus, update_state_status, add_error_message, add_debug_info, update_capability_status

# 导入现有的组件
from ..autonomous.enhanced_agent import EnhancedSelfEvolvingAgent
from ..autonomous.code_generator import CodeGenerator
from ..autonomous.code_validator import CodeValidator
from ..autonomous.dependency_manager import DependencyManager
from ..llm.deepseek_client import DeepSeekClient

logger = logging.getLogger(__name__)


class AgentNodes:
    """Agent节点集合"""
    
    def __init__(self, deepseek_api_key: str = None):
        """初始化节点"""
        self.deepseek_api_key = deepseek_api_key
        self.code_generator = CodeGenerator(deepseek_api_key)
        self.code_validator = CodeValidator()
        self.dependency_manager = DependencyManager()
        self.enhanced_agent = EnhancedSelfEvolvingAgent()
        
        if deepseek_api_key:
            self.llm_client = DeepSeekClient(deepseek_api_key)
        else:
            self.llm_client = None
    
    async def task_analysis_node(self, state: AgentState) -> AgentState:
        """任务分析节点"""
        logger.info(f"🧠 开始任务分析: {state['task_context'].original_task}")
        
        try:
            state = update_state_status(state, TaskStatus.ANALYZING)
            start_time = time.time()
            
            # 使用LLM分析任务
            if self.llm_client:
                required_capabilities = await self.llm_client.analyze_task_capabilities(
                    state['task_context'].original_task
                )
                state["llm_analysis"] = {
                    "required_capabilities": required_capabilities,
                    "analysis_method": "deepseek_llm"
                }
            else:
                # 回退到基础分析
                required_capabilities = await self._basic_task_analysis(
                    state['task_context'].original_task
                )
                state["llm_analysis"] = {
                    "required_capabilities": required_capabilities,
                    "analysis_method": "basic"
                }
            
            # 更新任务上下文
            state['task_context'].required_capabilities = required_capabilities
            state['task_context'].analysis_result = state["llm_analysis"]
            
            # 记录调试信息
            analysis_time = time.time() - start_time
            state = add_debug_info(state, "analysis_time", analysis_time)
            state = add_debug_info(state, "required_capabilities", required_capabilities)
            
            logger.info(f"✅ 任务分析完成，识别到 {len(required_capabilities)} 个所需能力")
            
        except Exception as e:
            logger.error(f"❌ 任务分析失败: {e}")
            state = add_error_message(state, f"任务分析失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def capability_check_node(self, state: AgentState) -> AgentState:
        """能力检查节点"""
        logger.info("🔍 开始能力检查")
        
        try:
            state = update_state_status(state, TaskStatus.CAPABILITY_CHECK)
            
            required_capabilities = state['task_context'].required_capabilities
            available_capabilities = self.enhanced_agent.get_available_capabilities()
            
            # 检查每个所需能力
            missing_capabilities = []
            for capability in required_capabilities:
                if capability in available_capabilities:
                    state = update_capability_status(
                        state, capability, CapabilityStatus.AVAILABLE
                    )
                else:
                    state = update_capability_status(
                        state, capability, CapabilityStatus.MISSING,
                        description=f"缺失的能力: {capability}"
                    )
                    missing_capabilities.append(capability)
            
            state['task_context'].missing_capabilities = missing_capabilities
            state["available_capabilities"] = list(available_capabilities)
            
            logger.info(f"📊 能力检查完成: {len(available_capabilities)} 可用, {len(missing_capabilities)} 缺失")
            
        except Exception as e:
            logger.error(f"❌ 能力检查失败: {e}")
            state = add_error_message(state, f"能力检查失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def code_generation_node(self, state: AgentState) -> AgentState:
        """代码生成节点"""
        missing_capabilities = state['task_context'].missing_capabilities
        
        if not missing_capabilities:
            logger.info("✅ 无需生成代码，所有能力都已可用")
            return state
        
        logger.info(f"⚡ 开始生成代码，处理 {len(missing_capabilities)} 个缺失能力")
        
        try:
            state = update_state_status(state, TaskStatus.GENERATING_CODE)
            
            for capability in missing_capabilities:
                logger.info(f"🔧 生成能力: {capability}")
                
                # 生成代码计划
                plan = await self.code_generator.generate_code_plan(
                    capability, 
                    f"实现{capability}功能"
                )
                
                # 更新能力状态
                state = update_capability_status(
                    state, capability, CapabilityStatus.GENERATED,
                    generated_code=plan.code_template,
                    required_libraries=plan.required_libraries
                )
                
                logger.info(f"✅ {capability} 代码生成完成")
            
            logger.info("🎉 所有缺失能力的代码生成完成")
            
        except Exception as e:
            logger.error(f"❌ 代码生成失败: {e}")
            state = add_error_message(state, f"代码生成失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def code_validation_node(self, state: AgentState) -> AgentState:
        """代码验证节点"""
        logger.info("🔍 开始代码验证")
        
        try:
            state = update_state_status(state, TaskStatus.VALIDATING_CODE)
            
            validation_results = {}
            
            for capability_name, capability_info in state["capabilities"].items():
                if capability_info.status == CapabilityStatus.GENERATED:
                    logger.info(f"🧪 验证能力: {capability_name}")
                    
                    # 创建临时的代码计划对象进行验证
                    from ..autonomous.code_generator import CodeGenerationPlan, ImplementationType
                    
                    temp_plan = CodeGenerationPlan(
                        capability_name=capability_name,
                        implementation_type=ImplementationType.SELF_CODED,
                        required_libraries=capability_info.required_libraries,
                        complexity_level="moderate",
                        estimated_lines=len(capability_info.generated_code.split('\n')),
                        main_functions=[],
                        dependencies=capability_info.required_libraries,
                        code_template=capability_info.generated_code,
                        test_cases=[],
                        documentation="",
                        reasoning=""
                    )
                    
                    # 验证代码
                    validation_result = await self.code_validator.validate_code(temp_plan)
                    validation_results[capability_name] = validation_result
                    
                    # 更新能力状态
                    state = update_capability_status(
                        state, capability_name, CapabilityStatus.VALIDATED,
                        validation_score=validation_result.score
                    )
                    
                    logger.info(f"📊 {capability_name} 验证完成，评分: {validation_result.score:.1f}")
            
            state["validation_result"] = validation_results
            logger.info("✅ 代码验证完成")
            
        except Exception as e:
            logger.error(f"❌ 代码验证失败: {e}")
            state = add_error_message(state, f"代码验证失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def dependency_management_node(self, state: AgentState) -> AgentState:
        """依赖管理节点"""
        logger.info("📦 开始依赖管理")
        
        try:
            state = update_state_status(state, TaskStatus.INSTALLING_DEPS)
            
            # 收集所有需要的依赖
            all_dependencies = set()
            for capability_info in state["capabilities"].values():
                if capability_info.status == CapabilityStatus.VALIDATED:
                    all_dependencies.update(capability_info.required_libraries)
            
            if all_dependencies:
                logger.info(f"🔍 检查依赖: {', '.join(all_dependencies)}")
                
                # 检查依赖状态
                dependency_status = self.dependency_manager.check_dependencies(list(all_dependencies))
                state["dependency_status"] = dependency_status
                
                # 自动安装缺失依赖
                if dependency_status['missing_packages']:
                    logger.info(f"💾 安装 {dependency_status['missing_count']} 个缺失依赖")
                    
                    installation_result = await self.dependency_manager.auto_install_dependencies(
                        list(all_dependencies), auto_confirm=True
                    )
                    state["installation_result"] = installation_result
                    
                    if installation_result['success']:
                        logger.info("✅ 所有依赖安装成功")
                    else:
                        logger.warning(f"⚠️ 部分依赖安装失败: {installation_result['failed_packages']}")
                else:
                    logger.info("✅ 所有依赖都已安装")
            else:
                logger.info("ℹ️ 无需安装额外依赖")
            
        except Exception as e:
            logger.error(f"❌ 依赖管理失败: {e}")
            state = add_error_message(state, f"依赖管理失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def task_execution_node(self, state: AgentState) -> AgentState:
        """任务执行节点"""
        logger.info("🚀 开始任务执行")
        
        try:
            state = update_state_status(state, TaskStatus.EXECUTING)
            
            # 注册生成的能力
            for capability_name, capability_info in state["capabilities"].items():
                if capability_info.status == CapabilityStatus.VALIDATED:
                    await self._register_capability(capability_name, capability_info)
            
            # 执行原始任务
            execution_result = await self.enhanced_agent.execute_task(
                state['task_context'].original_task
            )
            
            state["execution_result"] = execution_result
            state['task_context'].execution_result = execution_result
            
            # 生成最终响应
            final_response = self._generate_final_response(state)
            state["final_response"] = final_response
            
            state = update_state_status(state, TaskStatus.COMPLETED)
            logger.info("🎉 任务执行完成")
            
        except Exception as e:
            logger.error(f"❌ 任务执行失败: {e}")
            state = add_error_message(state, f"任务执行失败: {str(e)}")
            state = update_state_status(state, TaskStatus.FAILED)
        
        return state
    
    async def _basic_task_analysis(self, task: str) -> List[str]:
        """基础任务分析（无LLM时的回退方案）"""
        # 简单的关键词匹配
        capabilities = []
        
        if any(word in task.lower() for word in ['搜索', 'search', '查找']):
            capabilities.append('web_search')
        if any(word in task.lower() for word in ['天气', 'weather']):
            capabilities.append('weather_api')
        if any(word in task.lower() for word in ['翻译', 'translate']):
            capabilities.append('translation')
        if any(word in task.lower() for word in ['pdf', '文档', 'document']):
            capabilities.append('pdf_processing')
        
        return capabilities
    
    async def _register_capability(self, name: str, capability_info):
        """注册生成的能力"""
        # 这里可以实现能力的动态注册逻辑
        logger.info(f"📝 注册能力: {name}")
        pass
    
    def _generate_final_response(self, state: AgentState) -> str:
        """生成最终响应"""
        if state["execution_result"]:
            return f"任务执行成功: {state['execution_result']}"
        else:
            return "任务执行完成，但没有返回结果"

    async def experience_learning_node(self, state: AgentState) -> AgentState:
        """经验学习节点"""
        logger.info("🧠 开始经验学习")

        try:
            # 收集执行数据
            experience_data = {
                "task": state['task_context'].original_task,
                "required_capabilities": state['task_context'].required_capabilities,
                "missing_capabilities": state['task_context'].missing_capabilities,
                "generated_capabilities": [
                    name for name, info in state["capabilities"].items()
                    if info.status == CapabilityStatus.VALIDATED
                ],
                "execution_time": state["execution_time"],
                "success": state["current_status"] == TaskStatus.COMPLETED,
                "error_count": len(state["error_messages"]),
                "step_count": state["step_count"]
            }

            # 计算性能指标
            performance_metrics = {
                "efficiency_score": self._calculate_efficiency_score(state),
                "success_rate": 1.0 if state["current_status"] == TaskStatus.COMPLETED else 0.0,
                "capability_generation_success": len([
                    info for info in state["capabilities"].values()
                    if info.status == CapabilityStatus.VALIDATED
                ]) / max(len(state['task_context'].missing_capabilities), 1)
            }

            state["experience_data"] = experience_data
            state["performance_metrics"] = performance_metrics

            logger.info(f"📊 经验学习完成，效率评分: {performance_metrics['efficiency_score']:.2f}")

        except Exception as e:
            logger.error(f"❌ 经验学习失败: {e}")
            state = add_error_message(state, f"经验学习失败: {str(e)}")

        return state

    def _calculate_efficiency_score(self, state: AgentState) -> float:
        """计算效率评分"""
        base_score = 1.0

        # 根据步骤数调整
        if state["step_count"] > 10:
            base_score *= 0.8
        elif state["step_count"] > 15:
            base_score *= 0.6

        # 根据错误数调整
        error_penalty = len(state["error_messages"]) * 0.1
        base_score = max(0.0, base_score - error_penalty)

        # 根据执行时间调整
        if state["execution_time"] > 60:  # 超过1分钟
            base_score *= 0.9

        return base_score
