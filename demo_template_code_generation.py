"""
自主代码生成演示（模板版本）
演示Agent如何使用模板自动生成PDF文档分析功能
"""
import asyncio
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 导入自主代码生成组件
from src.autonomous.code_generator import CodeGenerator, CapabilityAnalyzer, ImplementationType
from src.autonomous.code_validator import CodeValidator


async def demo_template_pdf_generation():
    """演示使用模板生成PDF分析功能"""
    print("=" * 80)
    print("🤖 自主代码生成演示：PDF文档分析功能（模板版本）")
    print("=" * 80)
    
    try:
        # 1. 创建代码生成器（不使用LLM）
        print("\n📋 步骤1: 初始化代码生成器...")
        generator = CodeGenerator(api_key=None)  # 不使用LLM，仅使用模板
        print("✅ 代码生成器初始化完成")
        
        # 2. 演示能力分析
        print("\n🔍 步骤2: 分析PDF处理能力需求...")
        capability_name = "pdf_document_analysis"
        task_description = "分析PDF文档，提取文本内容、识别文档结构、提取表格数据和元数据信息"
        
        analyzer = CapabilityAnalyzer(api_key=None)
        implementation_type = await analyzer.analyze_capability_requirement(
            capability_name, task_description
        )
        
        print(f"📊 分析结果: {capability_name} -> {implementation_type.value}")
        
        if implementation_type == ImplementationType.SELF_CODED:
            print("✅ 判断结果：可以通过自主编程实现")
        else:
            print(f"⚠️  判断结果：{implementation_type.value}")
            
        # 3. 使用模板生成代码
        print("\n💻 步骤3: 使用模板生成PDF分析代码...")
        
        # 直接调用模板生成方法
        plan = generator._generate_template_plan(capability_name, task_description)
        
        print(f"📝 生成计划:")
        print(f"   - 能力名称: {plan.capability_name}")
        print(f"   - 实现类型: {plan.implementation_type.value}")
        print(f"   - 复杂度: {plan.complexity_level}")
        print(f"   - 预估代码行数: {plan.estimated_lines}")
        print(f"   - 所需库: {', '.join(plan.required_libraries)}")
        print(f"   - 主要函数: {', '.join(plan.main_functions)}")
        
        # 4. 验证代码
        print("\n🔍 步骤4: 验证生成的代码...")
        validator = CodeValidator()
        validation_result = await validator.validate_code(plan)
        
        print(f"📊 验证结果:")
        print(f"   - 总体评分: {validation_result.score:.1f}/100")
        print(f"   - 语法检查: {'✅' if validation_result.syntax_check else '❌'}")
        print(f"   - 安全检查: {'✅' if validation_result.security_check else '❌'}")
        print(f"   - 功能检查: {'✅' if validation_result.functionality_check else '❌'}")
        print(f"   - 性能检查: {'✅' if validation_result.performance_check else '❌'}")
        print(f"   - 验证通过: {'✅' if validation_result.is_valid else '❌'}")
        
        if validation_result.errors:
            print(f"   - 错误: {len(validation_result.errors)} 个")
            for error in validation_result.errors[:3]:  # 显示前3个错误
                print(f"     • {error}")
                
        if validation_result.warnings:
            print(f"   - 警告: {len(validation_result.warnings)} 个")
            for warning in validation_result.warnings[:3]:  # 显示前3个警告
                print(f"     • {warning}")
                
        if validation_result.recommendations:
            print(f"   - 建议: {len(validation_result.recommendations)} 个")
            for rec in validation_result.recommendations[:3]:
                print(f"     • {rec}")
                
        # 5. 显示生成的代码片段
        print("\n📄 步骤5: 生成的代码预览...")
        code_lines = plan.code_template.split('\n')
        print("```python")
        # 显示前30行代码
        for i, line in enumerate(code_lines[:30]):
            print(f"{i+1:2d}: {line}")
        if len(code_lines) > 30:
            print(f"... (还有 {len(code_lines) - 30} 行)")
        print("```")
        
        # 6. 保存生成的代码到文件
        print("\n💾 步骤6: 保存生成的代码...")
        output_dir = Path("generated_capabilities")
        output_dir.mkdir(exist_ok=True)
        
        code_file = output_dir / f"{capability_name}_capability.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(f'"""\n{plan.documentation}\n"""\n\n')
            f.write(plan.code_template)
            
        print(f"✅ 代码已保存到: {code_file}")
        
        # 7. 保存详细报告
        report_file = output_dir / f"{capability_name}_report.json"
        report = {
            "capability_name": capability_name,
            "task_description": task_description,
            "implementation_type": implementation_type.value,
            "generation_plan": {
                "complexity_level": plan.complexity_level,
                "estimated_lines": plan.estimated_lines,
                "required_libraries": plan.required_libraries,
                "main_functions": plan.main_functions
            },
            "validation_result": {
                "score": validation_result.score,
                "is_valid": validation_result.is_valid,
                "syntax_check": validation_result.syntax_check,
                "security_check": validation_result.security_check,
                "functionality_check": validation_result.functionality_check,
                "performance_check": validation_result.performance_check,
                "errors": validation_result.errors,
                "warnings": validation_result.warnings,
                "recommendations": validation_result.recommendations
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"📊 详细报告已保存到: {report_file}")
        
        # 8. 演示代码执行
        print("\n🚀 步骤7: 演示生成代码的功能...")
        
        # 动态导入生成的模块
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("pdf_capability", code_file)
            pdf_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(pdf_module)
            
            # 创建PDF分析器实例
            if hasattr(pdf_module, 'PdfDocumentAnalysisCapability'):
                pdf_analyzer = pdf_module.PdfDocumentAnalysisCapability()
                print("✅ 成功创建PDF分析器实例")
                
                # 显示可用方法
                methods = [method for method in dir(pdf_analyzer) 
                          if not method.startswith('_') and callable(getattr(pdf_analyzer, method))]
                print(f"📋 可用方法: {', '.join(methods)}")
                
            else:
                print("⚠️  未找到PdfDocumentAnalysisCapability类")
                
        except Exception as e:
            print(f"⚠️  代码执行测试失败: {e}")
        
        print("\n" + "=" * 80)
        print("🎉 自主代码生成演示完成！")
        print("=" * 80)
        
        return {
            "success": True,
            "capability_name": capability_name,
            "implementation_type": implementation_type.value,
            "validation_score": validation_result.score,
            "code_file": str(code_file),
            "report_file": str(report_file)
        }
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }


async def demo_multiple_template_capabilities():
    """演示多种能力的模板生成"""
    print("\n" + "=" * 80)
    print("🔄 多能力模板生成演示")
    print("=" * 80)
    
    capabilities_to_test = [
        {
            "name": "image_processing",
            "description": "处理图像文件，包括调整大小、格式转换、基础滤镜应用"
        },
        {
            "name": "data_analysis", 
            "description": "分析CSV和Excel数据文件，生成统计报告和可视化图表"
        },
        {
            "name": "text_mining",
            "description": "从文本中提取关键词、进行情感分析和主题建模"
        }
    ]
    
    try:
        generator = CodeGenerator(api_key=None)
        analyzer = CapabilityAnalyzer(api_key=None)
        validator = CodeValidator()
        
        results = []
        
        for i, capability in enumerate(capabilities_to_test, 1):
            print(f"\n📋 测试 {i}/{len(capabilities_to_test)}: {capability['name']}")
            
            # 分析实现类型
            impl_type = await analyzer.analyze_capability_requirement(
                capability['name'], capability['description']
            )
            
            print(f"   实现类型: {impl_type.value}")
            
            # 如果可以自主实现，则生成代码
            if impl_type == ImplementationType.SELF_CODED:
                plan = generator._generate_template_plan(
                    capability['name'], capability['description']
                )
                
                validation = await validator.validate_code(plan)
                
                results.append({
                    "capability": capability['name'],
                    "implementation_type": impl_type.value,
                    "validation_score": validation.score,
                    "is_valid": validation.is_valid,
                    "complexity": plan.complexity_level,
                    "estimated_lines": plan.estimated_lines
                })
                
                print(f"   验证评分: {validation.score:.1f}")
                print(f"   验证通过: {'✅' if validation.is_valid else '❌'}")
            else:
                results.append({
                    "capability": capability['name'],
                    "implementation_type": impl_type.value,
                    "note": "需要API支持或无法实现"
                })
                
        print(f"\n📊 多能力测试总结:")
        self_coded = [r for r in results if r.get('implementation_type') == 'self_coded']
        print(f"   - 可自主实现: {len(self_coded)}/{len(results)}")
        
        if self_coded:
            avg_score = sum(r['validation_score'] for r in self_coded) / len(self_coded)
            valid_count = sum(1 for r in self_coded if r['is_valid'])
            print(f"   - 平均验证分数: {avg_score:.1f}")
            print(f"   - 验证通过率: {valid_count}/{len(self_coded)}")
            
        return results
        
    except Exception as e:
        print(f"❌ 多能力测试失败: {e}")
        return []


async def main():
    """主演示函数"""
    print("🚀 启动自主代码生成演示系统（模板版本）")
    
    # 演示1: PDF分析功能生成
    pdf_result = await demo_template_pdf_generation()
    
    # 演示2: 多种能力生成
    multi_result = await demo_multiple_template_capabilities()
    
    print(f"\n🎯 演示总结:")
    print(f"   - PDF分析生成: {'✅' if pdf_result['success'] else '❌'}")
    print(f"   - 多能力测试: {len(multi_result)} 个能力测试完成")
    
    if pdf_result['success']:
        print(f"   - 生成的代码文件: {pdf_result['code_file']}")
        print(f"   - 详细报告: {pdf_result['report_file']}")


if __name__ == "__main__":
    asyncio.run(main())
