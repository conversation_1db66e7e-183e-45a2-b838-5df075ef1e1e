"""
自进化智能体核心类
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .capability_discoverer import CapabilityDiscoverer
from .task_executor import TaskExecutor
from .decision_engine import DecisionEngine
from ..capability_management.capability_registry import CapabilityRegistry
from ..code_generation.code_generator import CodeGenerator
from ..learning.experience_recorder import ExperienceRecorder


class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CAPABILITY_MISSING = "capability_missing"


@dataclass
class Task:
    id: str
    description: str
    status: TaskStatus
    required_capabilities: List[str]
    missing_capabilities: List[str]
    result: Optional[Any] = None
    error: Optional[str] = None


class SelfEvolvingAgent:
    """
    自进化智能体主类
    
    核心功能：
    1. 接收和执行任务
    2. 发现能力缺失
    3. 请求用户提供API
    4. 自动生成和集成新功能
    5. 学习和优化
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.capability_discoverer = CapabilityDiscoverer()
        self.task_executor = TaskExecutor()
        self.decision_engine = DecisionEngine()
        
        # 管理系统
        self.capability_registry = CapabilityRegistry()
        self.code_generator = CodeGenerator()
        self.experience_recorder = ExperienceRecorder()
        
        # 状态管理
        self.current_task: Optional[Task] = None
        self.task_history: List[Task] = []
        
        # 用户交互回调
        self.user_interaction_callback: Optional[Callable] = None
        
    def set_user_interaction_callback(self, callback: Callable):
        """设置用户交互回调函数"""
        self.user_interaction_callback = callback
        
    async def execute_task(self, task_description: str) -> Dict[str, Any]:
        """
        执行任务的主入口
        
        Args:
            task_description: 任务描述
            
        Returns:
            执行结果字典
        """
        self.logger.info(f"开始执行任务: {task_description}")
        
        # 创建任务对象
        task = Task(
            id=f"task_{len(self.task_history)}",
            description=task_description,
            status=TaskStatus.PENDING,
            required_capabilities=[],
            missing_capabilities=[]
        )
        
        self.current_task = task
        task.status = TaskStatus.IN_PROGRESS
        
        try:
            # 1. 分析任务所需能力
            required_capabilities = await self.capability_discoverer.analyze_task(task_description)
            task.required_capabilities = required_capabilities
            
            # 2. 检查能力缺失
            missing_capabilities = await self._check_missing_capabilities(required_capabilities)
            task.missing_capabilities = missing_capabilities
            
            # 3. 如果有缺失能力，请求用户提供
            if missing_capabilities:
                task.status = TaskStatus.CAPABILITY_MISSING
                await self._request_missing_capabilities(missing_capabilities)
                
            # 4. 执行任务
            result = await self.task_executor.execute(task)
            task.result = result
            task.status = TaskStatus.COMPLETED
            
            # 5. 记录经验
            await self.experience_recorder.record_task_execution(task)
            
            self.logger.info(f"任务执行完成: {task.id}")
            return {
                "status": "success",
                "task_id": task.id,
                "result": result
            }
            
        except Exception as e:
            task.error = str(e)
            task.status = TaskStatus.FAILED
            self.logger.error(f"任务执行失败: {e}")
            return {
                "status": "error",
                "task_id": task.id,
                "error": str(e)
            }
            
        finally:
            self.task_history.append(task)
            self.current_task = None
            
    async def _check_missing_capabilities(self, required_capabilities: List[str]) -> List[str]:
        """检查缺失的能力"""
        missing = []
        for capability in required_capabilities:
            if not await self.capability_registry.has_capability(capability):
                missing.append(capability)
        return missing
        
    async def _request_missing_capabilities(self, missing_capabilities: List[str]):
        """请求用户提供缺失的能力"""
        for capability in missing_capabilities:
            self.logger.info(f"请求用户提供能力: {capability}")
            
            if self.user_interaction_callback:
                # 通过回调请求用户提供API
                api_info = await self.user_interaction_callback(
                    "capability_request",
                    {
                        "capability": capability,
                        "message": f"我需要 '{capability}' 能力来完成任务，请提供相关的API文档或实现。"
                    }
                )
                
                if api_info:
                    # 生成新的能力模块
                    await self._generate_capability(capability, api_info)
                    
    async def _generate_capability(self, capability_name: str, api_info: Dict[str, Any]):
        """生成新的能力模块"""
        self.logger.info(f"生成新能力: {capability_name}")
        
        try:
            # 使用代码生成器创建新功能
            module_code = await self.code_generator.generate_capability_module(
                capability_name, api_info
            )
            
            # 注册新能力
            await self.capability_registry.register_capability(
                capability_name, module_code
            )
            
            self.logger.info(f"成功生成并注册能力: {capability_name}")
            
        except Exception as e:
            self.logger.error(f"生成能力失败: {e}")
            raise
            
    def get_available_capabilities(self) -> List[str]:
        """获取当前可用的能力列表"""
        return self.capability_registry.list_capabilities()
        
    def get_task_history(self) -> List[Dict[str, Any]]:
        """获取任务历史"""
        return [
            {
                "id": task.id,
                "description": task.description,
                "status": task.status.value,
                "required_capabilities": task.required_capabilities,
                "missing_capabilities": task.missing_capabilities,
                "result": task.result,
                "error": task.error
            }
            for task in self.task_history
        ]
