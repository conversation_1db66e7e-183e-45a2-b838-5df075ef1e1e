#!/usr/bin/env python3
"""
自进化智能体安装脚本
自动安装所需依赖并设置环境
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败")
        print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def install_core_dependencies():
    """安装核心依赖"""
    print("\n📦 安装核心依赖...")
    
    core_packages = [
        "jinja2>=3.1.0",
        "requests>=2.31.0", 
        "aiosqlite>=0.19.0",
        "pyyaml>=6.0.0",
        "watchdog>=3.0.0"
    ]
    
    for package in core_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            return False
    
    return True


def install_optional_dependencies():
    """安装可选依赖"""
    print("\n📦 安装可选依赖...")
    
    optional_packages = [
        "rich>=13.0.0",
        "typer>=0.9.0"
    ]
    
    for package in optional_packages:
        run_command(f"pip install {package}", f"安装 {package} (可选)")


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "data",
        "data/files", 
        "logs",
        "src/capabilities",
        "src/code_generation/templates"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def create_basic_templates():
    """创建基本的代码模板"""
    print("\n📝 创建基本模板...")
    
    # 创建基本的能力模板
    template_content = '''"""
{{ capability_name }} 能力模块
自动生成于: {{ generation_time }}
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional


class {{ class_name }}:
    """
    {{ capability_description }}
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    {% for endpoint in endpoints %}
    async def {{ endpoint.name }}(self, {% for param in endpoint.parameters %}{{ param.name }}: {{ param.type }}{% if param.default %} = {{ param.default }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        {{ endpoint.description }}
        
        Args:
        {% for param in endpoint.parameters %}
            {{ param.name }}: {{ param.description }}
        {% endfor %}
            context: 执行上下文
            
        Returns:
            执行结果
        """
        self.logger.info(f"执行 {{ endpoint.name }}")
        
        try:
            # TODO: 实现具体功能
            result = {"status": "success", "message": "功能执行成功"}
            return result
            
        except Exception as e:
            self.logger.error(f"{{ endpoint.name }} 执行失败: {e}")
            raise
            
    {% endfor %}
    async def execute(self, params: Dict[str, Any] = {}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行 {{ capability_name }} 功能
        
        Args:
            params: 执行参数
            context: 执行上下文
            
        Returns:
            执行结果
        """
        self.logger.info(f"执行参数: {params}")
        
        # 根据参数选择执行的方法
        action = params.get("action", "{{ endpoints[0].name if endpoints else 'default' }}")
        
        {% for endpoint in endpoints %}
        {% if loop.first %}if{% else %}elif{% endif %} action == "{{ endpoint.name }}":
            return await self.{{ endpoint.name }}(
                {% for param in endpoint.parameters %}
                params.get("{{ param.name }}", {% if param.default %}"{{ param.default }}"{% else %}""{% endif %}){% if not loop.last %},{% endif %}
                {% endfor %}
            )
        {% endfor %}
        else:
            return {
                "status": "error",
                "message": f"未知操作: {action}"
            }
'''
    
    template_path = Path("src/code_generation/templates/capability_template.py.j2")
    template_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print(f"✅ 创建模板: {template_path}")


def run_basic_test():
    """运行基本测试"""
    print("\n🧪 运行基本测试...")
    
    test_code = '''
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

async def test_imports():
    """测试基本导入"""
    try:
        from src.core.capability_discoverer import CapabilityDiscoverer
        print("[OK] 能力发现器导入成功")

        from src.code_generation.code_generator import CodeGenerator
        print("[OK] 代码生成器导入成功")

        from src.capability_management.capability_registry import CapabilityRegistry
        print("[OK] 能力注册器导入成功")

        # 测试基本功能
        discoverer = CapabilityDiscoverer()
        capabilities = await discoverer.analyze_task("搜索网页内容")
        print(f"[OK] 能力发现测试: {capabilities}")

        return True

    except Exception as e:
        print(f"[ERROR] 导入测试失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_imports())
    if success:
        print("\\n[SUCCESS] 基本测试通过!")
    else:
        print("\\n[FAILED] 基本测试失败!")
'''
    
    with open("test_basic.py", 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    return run_command("python test_basic.py", "运行基本测试")


def main():
    """主安装函数"""
    print("🚀 自进化智能体安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装核心依赖
    if not install_core_dependencies():
        print("❌ 核心依赖安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 安装可选依赖
    install_optional_dependencies()
    
    # 创建目录结构
    create_directories()
    
    # 创建基本模板
    create_basic_templates()
    
    # 运行基本测试
    if run_basic_test():
        print("\n🎉 安装完成!")
        print("\n下一步:")
        print("1. 运行演示: python run_demo.py --mode demo")
        print("2. 交互模式: python run_demo.py --mode interactive") 
        print("3. 运行测试: python run_demo.py --mode test")
    else:
        print("\n⚠️ 安装完成但测试失败，请检查配置")
    
    # 清理测试文件
    if os.path.exists("test_basic.py"):
        os.remove("test_basic.py")


if __name__ == "__main__":
    main()
