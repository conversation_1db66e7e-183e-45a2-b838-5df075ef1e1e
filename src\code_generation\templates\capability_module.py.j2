"""
{{ capability_name }} 能力模块
自动生成于: {{ timestamp }}
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
{% for import_stmt in imports %}
{{ import_stmt }}
{% endfor %}


class {{ class_name }}:
    """
    {{ capability_description }}
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        {% for init_line in initialization %}
        {{ init_line }}
        {% endfor %}
        
    {% for method in methods %}
    async def {{ method.name }}(self, {% for param in method.params %}{{ param.name }}: {{ param.type }}{% if param.default %} = {{ param.default }}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}, context: Dict[str, Any] = None) -> {{ method.return_type }}:
        """
        {{ method.description }}
        
        Args:
            {% for param in method.params %}
            {{ param.name }}: {{ param.description }}
            {% endfor %}
            context: 执行上下文
            
        Returns:
            {{ method.return_description }}
        """
        self.logger.info(f"执行 {{ method.name }}")
        
        try:
            {% for line in method.implementation %}
            {{ line }}
            {% endfor %}
            
        except Exception as e:
            self.logger.error(f"{{ method.name }} 执行失败: {e}")
            raise
            
    {% endfor %}
