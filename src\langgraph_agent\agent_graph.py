"""
LangGraph Agent 图定义
定义自进化智能体的工作流图结构，支持动态工作流生成
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Literal, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .agent_state import AgentState, TaskStatus, create_initial_state, is_task_completed, get_missing_capabilities
from .workflow_nodes import WorkflowNodes
from .dynamic_workflow_generator import DynamicWorkflowGenerator, PromptType
from .intelligent_prompt_generator import IntelligentPromptGenerator, PromptStyle
from .workflow_optimizer import WorkflowOptimizer, OptimizationStrategy

logger = logging.getLogger(__name__)


class SelfEvolvingAgentGraph:
    """基于LangGraph的自进化智能体，支持动态工作流生成"""

    def __init__(self, deepseek_api_key: str = None):
        """初始化Agent图"""
        self.deepseek_api_key = deepseek_api_key

        # 初始化核心组件
        self.workflow_nodes = WorkflowNodes(deepseek_api_key)
        self.workflow_generator = DynamicWorkflowGenerator(self.workflow_nodes.llm_client)
        self.prompt_generator = IntelligentPromptGenerator(self.workflow_nodes.llm_client)
        self.workflow_optimizer = WorkflowOptimizer(self.workflow_nodes.llm_client)

        # 默认工作流图
        self.default_graph = self._build_default_graph()
        self.memory = MemorySaver()

        # 编译默认图
        self.compiled_graph = self.default_graph.compile(checkpointer=self.memory)

        # 动态工作流缓存
        self.dynamic_workflows = {}
        self.execution_history = []
    
    def _build_default_graph(self) -> StateGraph:
        """构建默认工作流图"""
        # 创建状态图
        workflow = StateGraph(AgentState)

        # 添加核心节点
        workflow.add_node("task_reception", self.workflow_nodes.task_reception_node)
        workflow.add_node("llm_analysis", self.workflow_nodes.llm_analysis_node)
        workflow.add_node("capability_check", self.workflow_nodes.capability_check_node)
        workflow.add_node("api_search", self.workflow_nodes.api_search_node)
        workflow.add_node("api_evaluation", self.workflow_nodes.api_evaluation_node)
        workflow.add_node("api_integration", self.workflow_nodes.api_integration_node)
        workflow.add_node("code_generation", self.workflow_nodes.code_generation_node)
        workflow.add_node("code_validation", self.workflow_nodes.code_validation_node)
        workflow.add_node("dependency_management", self.workflow_nodes.dependency_management_node)
        workflow.add_node("task_execution", self.workflow_nodes.task_execution_node)
        workflow.add_node("result_validation", self.workflow_nodes.result_validation_node)
        workflow.add_node("experience_learning", self.workflow_nodes.experience_learning_node)
        workflow.add_node("response_generation", self.workflow_nodes.response_generation_node)

        # 设置入口点
        workflow.set_entry_point("task_reception")

        # 定义基础流程边
        workflow.add_edge("task_reception", "llm_analysis")
        workflow.add_edge("llm_analysis", "capability_check")

        # 条件边：根据能力检查结果决定下一步
        workflow.add_conditional_edges(
            "capability_check",
            self._route_after_capability_check,
            {
                "search_apis": "api_search",
                "generate_code": "code_generation",
                "execute_directly": "task_execution"
            }
        )

        # API路径
        workflow.add_edge("api_search", "api_evaluation")
        workflow.add_conditional_edges(
            "api_evaluation",
            self._route_after_api_evaluation,
            {
                "integrate_api": "api_integration",
                "generate_code": "code_generation",
                "execute_directly": "task_execution"
            }
        )
        workflow.add_edge("api_integration", "dependency_management")

        # 代码生成路径
        workflow.add_edge("code_generation", "code_validation")
        workflow.add_edge("code_validation", "dependency_management")

        # 执行路径
        workflow.add_edge("dependency_management", "task_execution")
        workflow.add_edge("task_execution", "result_validation")
        workflow.add_edge("result_validation", "experience_learning")
        workflow.add_edge("experience_learning", "response_generation")

        # 条件边：根据执行结果决定是否结束
        workflow.add_conditional_edges(
            "response_generation",
            self._should_end,
            {
                "end": END,
                "retry": "llm_analysis"  # 如果需要重试
            }
        )

        return workflow
    
    def _route_after_capability_check(self, state: AgentState) -> Literal["search_apis", "generate_code", "execute_directly"]:
        """能力检查后的路由决策"""
        missing_capabilities = get_missing_capabilities(state)

        if not missing_capabilities:
            logger.info("✅ 所有能力都已可用，直接执行任务")
            return "execute_directly"

        # 检查是否应该优先搜索API
        api_suitable_capabilities = [cap for cap in missing_capabilities
                                   if any(keyword in cap.lower() for keyword in
                                         ["api", "service", "integration", "data", "search"])]

        if api_suitable_capabilities:
            logger.info(f"🔍 优先搜索API解决 {len(api_suitable_capabilities)} 个能力")
            return "search_apis"
        else:
            logger.info(f"🔧 需要生成 {len(missing_capabilities)} 个缺失能力的代码")
            return "generate_code"

    def _route_after_api_evaluation(self, state: AgentState) -> Literal["integrate_api", "generate_code", "execute_directly"]:
        """API评估后的路由决策"""
        api_evaluation_results = state.get("api_evaluation_results", {})

        # 检查是否有高质量的API可用
        high_quality_apis = []
        for capability, evaluation in api_evaluation_results.items():
            best_api = evaluation.get("best_api")
            if best_api and best_api.get("score", 0) > 0.7:
                high_quality_apis.append(capability)

        if high_quality_apis:
            logger.info(f"🔗 集成 {len(high_quality_apis)} 个高质量API")
            return "integrate_api"

        # 检查是否还有其他缺失能力需要代码生成
        missing_capabilities = get_missing_capabilities(state)
        if missing_capabilities:
            logger.info(f"🔧 API不可用，转为代码生成")
            return "generate_code"
        else:
            logger.info("✅ 直接执行任务")
            return "execute_directly"
    
    def _should_end(self, state: AgentState) -> Literal["end", "retry"]:
        """判断是否应该结束"""
        if is_task_completed(state):
            return "end"
        else:
            # 可以添加重试逻辑
            return "end"  # 暂时总是结束，避免无限循环
    
    async def process_task(self, user_task: str, task_id: str = None,
                          use_dynamic_workflow: bool = True) -> Dict[str, Any]:
        """处理用户任务，支持动态工作流生成"""
        if task_id is None:
            task_id = f"task_{int(time.time())}"

        logger.info(f"🚀 开始处理任务: {user_task}")
        start_time = time.time()

        try:
            # 创建初始状态
            initial_state = create_initial_state(user_task, task_id)

            # 决定使用哪个工作流
            if use_dynamic_workflow:
                # 生成动态工作流
                workflow_result = await self._generate_dynamic_workflow(user_task, initial_state)
                if workflow_result["success"]:
                    compiled_graph = workflow_result["compiled_graph"]
                    logger.info(f"✨ 使用动态生成的工作流: {workflow_result['workflow_name']}")
                else:
                    compiled_graph = self.compiled_graph
                    logger.warning("⚠️ 动态工作流生成失败，使用默认工作流")
            else:
                compiled_graph = self.compiled_graph
                logger.info("📋 使用默认工作流")

            # 创建线程配置
            config = {"configurable": {"thread_id": task_id}}

            # 执行图
            final_state = None
            step_count = 0
            execution_steps = []

            async for state in compiled_graph.astream(initial_state, config):
                step_count += 1
                current_node = list(state.keys())[0]
                current_state = list(state.values())[0]

                logger.info(f"📍 步骤 {step_count}: {current_node} - 状态: {current_state['current_status'].value}")

                # 记录执行步骤
                execution_steps.append({
                    "step": step_count,
                    "node": current_node,
                    "status": current_state['current_status'].value,
                    "timestamp": time.time(),
                    "duration": time.time() - start_time
                })

                # 更新执行时间
                current_state["execution_time"] = time.time() - start_time
                final_state = current_state

                # 检查是否有错误
                if current_state["current_status"] == TaskStatus.FAILED:
                    logger.error("❌ 任务执行失败，提前终止")
                    break

            # 记录执行历史
            execution_record = {
                "task_id": task_id,
                "user_task": user_task,
                "execution_time": time.time() - start_time,
                "step_count": step_count,
                "success": final_state["current_status"] == TaskStatus.COMPLETED,
                "execution_steps": execution_steps,
                "final_state": final_state,
                "timestamp": time.time()
            }
            self.execution_history.append(execution_record)

            # 返回执行结果
            execution_time = time.time() - start_time

            result = {
                "success": final_state["current_status"] == TaskStatus.COMPLETED,
                "task_id": task_id,
                "execution_time": execution_time,
                "step_count": step_count,
                "final_response": final_state.get("final_response"),
                "generated_capabilities": [
                    name for name, info in final_state["capabilities"].items()
                    if info.status.value in ["validated", "installed"]
                ],
                "error_messages": final_state.get("error_messages", []),
                "performance_metrics": final_state.get("performance_metrics"),
                "state_summary": self._get_execution_summary(final_state),
                "execution_steps": execution_steps,
                "used_dynamic_workflow": use_dynamic_workflow and workflow_result.get("success", False)
            }

            logger.info(f"🎉 任务处理完成: {result['success']}, 耗时: {execution_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"❌ 任务处理异常: {e}")
            return {
                "success": False,
                "task_id": task_id,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "final_response": f"任务处理失败: {str(e)}"
            }
    
    async def _generate_dynamic_workflow(self, user_task: str, initial_state: AgentState) -> Dict[str, Any]:
        """生成动态工作流"""
        try:
            logger.info("🎯 开始生成动态工作流")

            # 准备上下文
            context = {
                "task_description": user_task,
                "required_capabilities": initial_state["task_context"].required_capabilities,
                "missing_capabilities": initial_state["task_context"].missing_capabilities,
                "complexity": initial_state["task_context"].complexity,
                "domain": initial_state["task_context"].domain
            }

            # 生成工作流
            generated_workflow = await self.workflow_generator.generate_workflow(
                user_task, context
            )

            # 构建LangGraph工作流
            langgraph_workflow = self._build_langgraph_from_generated(generated_workflow)

            # 编译工作流
            compiled_graph = langgraph_workflow.compile(checkpointer=self.memory)

            # 缓存动态工作流
            workflow_id = generated_workflow.workflow_id
            self.dynamic_workflows[workflow_id] = {
                "generated_workflow": generated_workflow,
                "compiled_graph": compiled_graph,
                "creation_time": time.time()
            }

            logger.info(f"✅ 动态工作流生成成功: {generated_workflow.name}")

            return {
                "success": True,
                "workflow_id": workflow_id,
                "workflow_name": generated_workflow.name,
                "compiled_graph": compiled_graph,
                "generated_workflow": generated_workflow
            }

        except Exception as e:
            logger.error(f"动态工作流生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "compiled_graph": self.compiled_graph
            }

    def _build_langgraph_from_generated(self, generated_workflow) -> StateGraph:
        """从生成的工作流构建LangGraph"""
        workflow = StateGraph(AgentState)

        # 添加节点
        for node in generated_workflow.nodes:
            # 根据节点类型映射到实际的节点函数
            node_function = self._get_node_function(node.node_type)
            workflow.add_node(node.name, node_function)

        # 设置入口点
        if generated_workflow.nodes:
            workflow.set_entry_point(generated_workflow.nodes[0].name)

        # 添加边
        for edge in generated_workflow.edges:
            if edge.condition:
                # 条件边
                workflow.add_conditional_edges(
                    edge.from_node,
                    self._create_condition_function(edge.condition),
                    edge.condition_mapping or {"continue": edge.to_node, "end": END}
                )
            else:
                # 普通边
                if edge.to_node == "END":
                    workflow.add_edge(edge.from_node, END)
                else:
                    workflow.add_edge(edge.from_node, edge.to_node)

        return workflow

    def _get_node_function(self, node_type: str):
        """根据节点类型获取对应的函数"""
        node_mapping = {
            "reception": self.workflow_nodes.task_reception_node,
            "analysis": self.workflow_nodes.llm_analysis_node,
            "capability_check": self.workflow_nodes.capability_check_node,
            "search": self.workflow_nodes.api_search_node,
            "evaluation": self.workflow_nodes.api_evaluation_node,
            "integration": self.workflow_nodes.api_integration_node,
            "generation": self.workflow_nodes.code_generation_node,
            "validation": self.workflow_nodes.code_validation_node,
            "dependency": self.workflow_nodes.dependency_management_node,
            "execution": self.workflow_nodes.task_execution_node,
            "result_validation": self.workflow_nodes.result_validation_node,
            "learning": self.workflow_nodes.experience_learning_node,
            "response": self.workflow_nodes.response_generation_node
        }

        return node_mapping.get(node_type, self.workflow_nodes.task_execution_node)

    def _create_condition_function(self, condition: str):
        """创建条件函数"""
        def condition_func(state: AgentState) -> str:
            # 这里可以实现更复杂的条件逻辑
            if "missing_capabilities" in condition:
                missing_caps = get_missing_capabilities(state)
                return "continue" if missing_caps else "end"
            elif "success" in condition:
                return "continue" if state["current_status"] == TaskStatus.COMPLETED else "end"
            else:
                return "continue"

        return condition_func

    def _get_execution_summary(self, state: AgentState) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            "original_task": state["task_context"].original_task,
            "status": state["current_status"].value,
            "required_capabilities": state["task_context"].required_capabilities,
            "missing_capabilities": state["task_context"].missing_capabilities,
            "generated_capabilities_count": len([
                info for info in state["capabilities"].values()
                if info.status.value in ["validated", "installed"]
            ]),
            "total_steps": state["step_count"],
            "has_errors": len(state["error_messages"]) > 0,
            "execution_time": state["execution_time"]
        }
    
    async def optimize_workflow(self, workflow_id: str = None,
                               strategy: OptimizationStrategy = OptimizationStrategy.BALANCED) -> Dict[str, Any]:
        """优化工作流"""
        try:
            # 获取要优化的工作流
            if workflow_id and workflow_id in self.dynamic_workflows:
                workflow_data = self.dynamic_workflows[workflow_id]
                generated_workflow = workflow_data["generated_workflow"]
            else:
                # 如果没有指定工作流ID，创建一个默认工作流表示
                logger.warning("未找到指定工作流，使用默认工作流进行优化分析")
                return {"success": False, "error": "未找到指定的工作流"}

            # 获取相关的执行历史
            relevant_history = [
                record for record in self.execution_history
                if record.get("workflow_id") == workflow_id
            ]

            if not relevant_history:
                logger.warning("没有找到相关的执行历史，无法进行优化")
                return {"success": False, "error": "缺少执行历史数据"}

            # 执行优化
            optimization_result = await self.workflow_optimizer.optimize_workflow(
                generated_workflow, relevant_history, strategy
            )

            # 如果优化成功，更新缓存的工作流
            if optimization_result.optimization_score > 0.5:
                # 重新构建并编译优化后的工作流
                optimized_langgraph = self._build_langgraph_from_generated(
                    optimization_result.optimized_workflow
                )
                compiled_optimized = optimized_langgraph.compile(checkpointer=self.memory)

                # 更新缓存
                self.dynamic_workflows[workflow_id] = {
                    "generated_workflow": optimization_result.optimized_workflow,
                    "compiled_graph": compiled_optimized,
                    "creation_time": time.time(),
                    "optimization_history": self.dynamic_workflows[workflow_id].get("optimization_history", []) + [optimization_result]
                }

                logger.info(f"✅ 工作流优化完成，评分: {optimization_result.optimization_score:.2f}")

            return {
                "success": True,
                "optimization_result": optimization_result,
                "workflow_updated": optimization_result.optimization_score > 0.5
            }

        except Exception as e:
            logger.error(f"工作流优化失败: {e}")
            return {"success": False, "error": str(e)}

    async def generate_intelligent_prompt(self, prompt_type: PromptType,
                                        context: Dict[str, Any],
                                        style: Optional[PromptStyle] = None) -> Dict[str, Any]:
        """生成智能Prompt"""
        try:
            generated_prompt = await self.prompt_generator.generate_prompt(
                prompt_type, context, style
            )

            return {
                "success": True,
                "prompt": generated_prompt
            }

        except Exception as e:
            logger.error(f"智能Prompt生成失败: {e}")
            return {"success": False, "error": str(e)}

    async def get_workflow_analytics(self) -> Dict[str, Any]:
        """获取工作流分析数据"""
        try:
            analytics = {
                "total_executions": len(self.execution_history),
                "dynamic_workflows_count": len(self.dynamic_workflows),
                "success_rate": 0.0,
                "average_execution_time": 0.0,
                "most_common_errors": {},
                "performance_trends": {},
                "optimization_opportunities": []
            }

            if self.execution_history:
                successful_executions = sum(1 for record in self.execution_history if record["success"])
                analytics["success_rate"] = successful_executions / len(self.execution_history)

                total_time = sum(record["execution_time"] for record in self.execution_history)
                analytics["average_execution_time"] = total_time / len(self.execution_history)

                # 分析错误模式
                error_counts = {}
                for record in self.execution_history:
                    if not record["success"] and "error" in record:
                        error_type = type(record["error"]).__name__
                        error_counts[error_type] = error_counts.get(error_type, 0) + 1

                analytics["most_common_errors"] = error_counts

            return analytics

        except Exception as e:
            logger.error(f"获取工作流分析失败: {e}")
            return {"error": str(e)}

    async def get_graph_visualization(self) -> str:
        """获取图的可视化表示"""
        try:
            # 尝试生成Mermaid图
            mermaid_code = self._generate_mermaid_diagram()
            return mermaid_code
        except Exception as e:
            logger.warning(f"无法生成图可视化: {e}")
            return "图可视化生成失败"
    
    def _generate_mermaid_diagram(self) -> str:
        """生成Mermaid流程图"""
        return """
graph TD
    A[任务接收] --> B[LLM分析]
    B --> C[能力检查]
    C --> D{路由决策}
    D -->|搜索API| E[API搜索]
    D -->|生成代码| F[代码生成]
    D -->|直接执行| J[任务执行]

    E --> G[API评估]
    G --> H{API质量}
    H -->|高质量| I[API集成]
    H -->|低质量| F[代码生成]
    H -->|无可用| J[任务执行]

    F --> K[代码验证]
    I --> L[依赖管理]
    K --> L[依赖管理]
    L --> J[任务执行]

    J --> M[结果验证]
    M --> N[经验学习]
    N --> O[响应生成]
    O --> P[结束]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style E fill:#e8f5e8
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style I fill:#e0f2f1
    style J fill:#ffebee
    style K fill:#f9fbe7
    style L fill:#fef7e0
    style M fill:#e8eaf6
    style N fill:#f3e5f5
    style O fill:#e0f7fa
    style P fill:#ffcdd2
"""
    
    def get_state_history(self, task_id: str) -> List[Dict[str, Any]]:
        """获取状态历史"""
        try:
            config = {"configurable": {"thread_id": task_id}}
            history = []
            
            # 这里可以实现状态历史的获取逻辑
            # LangGraph的checkpointer会保存状态历史
            
            return history
        except Exception as e:
            logger.error(f"获取状态历史失败: {e}")
            return []
    
    async def resume_task(self, task_id: str) -> Dict[str, Any]:
        """恢复任务执行"""
        try:
            config = {"configurable": {"thread_id": task_id}}
            
            # 从检查点恢复执行
            final_state = None
            async for state in self.compiled_graph.astream(None, config):
                final_state = list(state.values())[0]
            
            return {
                "success": True,
                "resumed_state": final_state["current_status"].value if final_state else "unknown"
            }
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
