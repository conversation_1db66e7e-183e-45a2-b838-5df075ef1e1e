#!/usr/bin/env python3
"""
测试 LLM 集成功能
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.llm.deepseek_client import DeepSeekClient
from src.core.capability_discoverer import CapabilityDiscoverer
from src.code_generation.code_generator import CodeGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


async def test_deepseek_client():
    """测试 DeepSeek 客户端基本功能"""
    print("🧪 测试 DeepSeek 客户端...")
    
    try:
        async with DeepSeekClient() as client:
            # 测试简单对话
            messages = [
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ]
            
            result = await client.chat_completion(messages)
            print(f"✅ 基本对话测试成功")
            print(f"回复: {result['choices'][0]['message']['content'][:100]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ DeepSeek 客户端测试失败: {e}")
        return False


async def test_capability_analysis():
    """测试 LLM 增强的能力分析"""
    print("\n🔍 测试 LLM 能力分析...")
    
    try:
        async with DeepSeekClient() as client:
            # 测试任务分析
            test_tasks = [
                "搜索最新的人工智能新闻",
                "查询北京今天的天气情况", 
                "将数据保存到Excel文件中",
                "发送邮件给客户",
                "处理这张图片并调整大小",
                "翻译这段英文文本为中文"
            ]
            
            for task in test_tasks:
                capabilities = await client.analyze_task_capabilities(task)
                print(f"任务: {task}")
                print(f"识别能力: {capabilities}")
                print("-" * 50)
                
            return True
            
    except Exception as e:
        print(f"❌ 能力分析测试失败: {e}")
        return False


async def test_code_generation():
    """测试 LLM 代码生成"""
    print("\n⚙️ 测试 LLM 代码生成...")
    
    try:
        async with DeepSeekClient() as client:
            # 测试代码生成
            api_info = {
                "type": "rest",
                "base_url": "https://api.openweathermap.org/data/2.5",
                "endpoints": {
                    "current_weather": {
                        "method": "GET",
                        "path": "/weather",
                        "parameters": {
                            "q": "城市名称",
                            "appid": "API密钥",
                            "units": "metric"
                        }
                    }
                },
                "authentication": {
                    "type": "api_key",
                    "key_name": "appid"
                },
                "description": "OpenWeatherMap 天气API"
            }
            
            code = await client.generate_api_implementation("weather_api", api_info)
            
            if code:
                print("✅ 代码生成成功")
                print("生成的代码片段:")
                print("-" * 50)
                print(code[:500] + "..." if len(code) > 500 else code)
                print("-" * 50)
                return True
            else:
                print("❌ 代码生成失败：返回空代码")
                return False
                
    except Exception as e:
        print(f"❌ 代码生成测试失败: {e}")
        return False


async def test_enhanced_capability_discoverer():
    """测试增强的能力发现器"""
    print("\n🔍 测试增强的能力发现器...")
    
    try:
        discoverer = CapabilityDiscoverer()
        
        test_tasks = [
            "搜索最新的AI技术新闻并保存到文件",
            "查询上海的天气预报",
            "分析销售数据并生成报告",
            "发送邮件通知客户订单状态"
        ]
        
        for task in test_tasks:
            capabilities = await discoverer.analyze_task(task)
            print(f"任务: {task}")
            print(f"发现能力: {capabilities}")
            print("-" * 50)
            
        return True
        
    except Exception as e:
        print(f"❌ 增强能力发现器测试失败: {e}")
        return False


async def test_enhanced_code_generator():
    """测试增强的代码生成器"""
    print("\n⚙️ 测试增强的代码生成器...")
    
    try:
        generator = CodeGenerator()
        
        # 测试API信息
        api_info = {
            "type": "rest",
            "base_url": "https://newsapi.org/v2",
            "endpoints": {
                "search_news": {
                    "method": "GET", 
                    "path": "/everything",
                    "parameters": {
                        "q": "搜索关键词",
                        "apiKey": "API密钥",
                        "language": "zh"
                    }
                }
            },
            "authentication": {
                "type": "api_key",
                "key_name": "apiKey"
            },
            "description": "新闻搜索API"
        }
        
        code = await generator.generate_capability_module("news_search", api_info)
        
        if code:
            print("✅ 增强代码生成成功")
            print("生成的代码片段:")
            print("-" * 50)
            print(code[:500] + "..." if len(code) > 500 else code)
            print("-" * 50)
            return True
        else:
            print("❌ 增强代码生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 增强代码生成器测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 LLM 集成测试套件")
    print("=" * 60)
    
    tests = [
        ("DeepSeek 客户端", test_deepseek_client),
        ("LLM 能力分析", test_capability_analysis),
        ("LLM 代码生成", test_code_generation),
        ("增强能力发现器", test_enhanced_capability_discoverer),
        ("增强代码生成器", test_enhanced_code_generator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有 LLM 集成测试通过！")
    else:
        print(f"\n⚠️ 有 {total-passed} 个测试失败，请检查配置")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
