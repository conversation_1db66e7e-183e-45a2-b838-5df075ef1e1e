import asyncio
import logging
import aiohttp
from typing import Dict, Any

class WeatherApiCapability:
    def __init__(self, api_key: str):
        """
        初始化天气API能力
        
        Args:
            api_key: OpenWeatherMap API密钥
        """
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://api.openweathermap.org/data/2.5"
        self.api_key = api_key
        self.default_params = {
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn"
        }
        
        # 设置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    async def get_weather(self, params: Dict[str, Any] = {}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        获取当前天气信息
        
        Args:
            params: 请求参数，必须包含'q'参数(城市名称)
            context: 上下文信息(可选)
            
        Returns:
            天气数据字典
            
        Raises:
            ValueError: 当缺少必要参数时
            aiohttp.ClientError: 当API请求失败时
        """
        try:
            # 合并默认参数和传入参数
            request_params = {**self.default_params, **params}
            
            # 验证必要参数
            if 'q' not in request_params:
                raise ValueError("缺少必要参数: 'q'(城市名称)")
                
            self.logger.info(f"准备获取天气数据，参数: {request_params}")
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/weather"
                async with session.get(url, params=request_params) as response:
                    if response.status != 200:
                        error_msg = await response.text()
                        self.logger.error(f"API请求失败，状态码: {response.status}, 错误: {error_msg}")
                        raise aiohttp.ClientError(f"API请求失败: {error_msg}")
                    
                    data = await response.json()
                    self.logger.info(f"成功获取天气数据: {data}")
                    return data
                    
        except ValueError as ve:
            self.logger.error(f"参数验证错误: {str(ve)}")
            raise
        except aiohttp.ClientError as ce:
            self.logger.error(f"网络请求错误: {str(ce)}")
            raise
        except Exception as e:
            self.logger.error(f"获取天气数据时发生意外错误: {str(e)}")
            raise