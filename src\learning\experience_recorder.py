"""
经验记录器 - 记录和分析任务执行经验
"""
import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiosqlite
from pathlib import Path


@dataclass
class TaskExperience:
    """任务执行经验"""
    task_id: str
    task_description: str
    required_capabilities: List[str]
    missing_capabilities: List[str]
    execution_time: float
    success: bool
    error_message: Optional[str]
    performance_metrics: Dict[str, Any]
    user_feedback: Optional[str]
    timestamp: datetime


@dataclass
class CapabilityUsage:
    """能力使用记录"""
    capability_name: str
    task_id: str
    execution_time: float
    success: bool
    error_message: Optional[str]
    input_params: Dict[str, Any]
    output_result: Any
    timestamp: datetime


class ExperienceRecorder:
    """
    经验记录器
    
    负责记录任务执行过程中的各种经验数据，
    为后续的学习和优化提供数据基础
    """
    
    def __init__(self, db_path: str = "data/experience.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """初始化经验记录器"""
        await self._create_database_schema()
        
    async def _create_database_schema(self):
        """创建数据库模式"""
        async with aiosqlite.connect(self.db_path) as db:
            # 任务经验表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS task_experiences (
                    task_id TEXT PRIMARY KEY,
                    task_description TEXT NOT NULL,
                    required_capabilities TEXT,  -- JSON array
                    missing_capabilities TEXT,   -- JSON array
                    execution_time REAL,
                    success BOOLEAN,
                    error_message TEXT,
                    performance_metrics TEXT,    -- JSON object
                    user_feedback TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # 能力使用记录表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS capability_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    capability_name TEXT NOT NULL,
                    task_id TEXT NOT NULL,
                    execution_time REAL,
                    success BOOLEAN,
                    error_message TEXT,
                    input_params TEXT,       -- JSON object
                    output_result TEXT,      -- JSON object
                    timestamp TEXT NOT NULL,
                    FOREIGN KEY (task_id) REFERENCES task_experiences (task_id)
                )
            ''')
            
            # 学习模式表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,  -- JSON object
                    confidence REAL,
                    usage_count INTEGER DEFAULT 0,
                    success_rate REAL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            await db.commit()
            
    async def record_task_execution(self, task) -> bool:
        """
        记录任务执行经验
        
        Args:
            task: 任务对象
            
        Returns:
            记录是否成功
        """
        try:
            experience = TaskExperience(
                task_id=task.id,
                task_description=task.description,
                required_capabilities=task.required_capabilities,
                missing_capabilities=task.missing_capabilities,
                execution_time=0.0,  # TODO: 计算实际执行时间
                success=task.status.value == "completed",
                error_message=task.error,
                performance_metrics={},  # TODO: 收集性能指标
                user_feedback=None,
                timestamp=datetime.now()
            )
            
            await self._save_task_experience(experience)
            self.logger.info(f"记录任务经验: {task.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录任务经验失败: {e}")
            return False
            
    async def _save_task_experience(self, experience: TaskExperience):
        """保存任务经验到数据库"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT OR REPLACE INTO task_experiences 
                (task_id, task_description, required_capabilities, missing_capabilities,
                 execution_time, success, error_message, performance_metrics, 
                 user_feedback, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                experience.task_id,
                experience.task_description,
                json.dumps(experience.required_capabilities),
                json.dumps(experience.missing_capabilities),
                experience.execution_time,
                experience.success,
                experience.error_message,
                json.dumps(experience.performance_metrics),
                experience.user_feedback,
                experience.timestamp.isoformat()
            ))
            await db.commit()
            
    async def record_capability_usage(
        self,
        capability_name: str,
        task_id: str,
        execution_time: float,
        success: bool,
        error_message: Optional[str] = None,
        input_params: Dict[str, Any] = None,
        output_result: Any = None
    ) -> bool:
        """记录能力使用情况"""
        try:
            usage = CapabilityUsage(
                capability_name=capability_name,
                task_id=task_id,
                execution_time=execution_time,
                success=success,
                error_message=error_message,
                input_params=input_params or {},
                output_result=output_result,
                timestamp=datetime.now()
            )
            
            await self._save_capability_usage(usage)
            return True
            
        except Exception as e:
            self.logger.error(f"记录能力使用失败: {e}")
            return False
            
    async def _save_capability_usage(self, usage: CapabilityUsage):
        """保存能力使用记录"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute('''
                INSERT INTO capability_usage 
                (capability_name, task_id, execution_time, success, error_message,
                 input_params, output_result, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                usage.capability_name,
                usage.task_id,
                usage.execution_time,
                usage.success,
                usage.error_message,
                json.dumps(usage.input_params),
                json.dumps(usage.output_result, default=str),
                usage.timestamp.isoformat()
            ))
            await db.commit()
            
    async def get_task_success_rate(self, days: int = 30) -> float:
        """获取任务成功率"""
        since_date = datetime.now() - timedelta(days=days)
        
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('''
                SELECT COUNT(*) as total, SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful
                FROM task_experiences 
                WHERE timestamp > ?
            ''', (since_date.isoformat(),)) as cursor:
                row = await cursor.fetchone()
                if row and row[0] > 0:
                    return row[1] / row[0]
                return 0.0
                
    async def get_capability_performance(self, capability_name: str, days: int = 30) -> Dict[str, Any]:
        """获取能力性能统计"""
        since_date = datetime.now() - timedelta(days=days)
        
        async with aiosqlite.connect(self.db_path) as db:
            # 基本统计
            async with db.execute('''
                SELECT COUNT(*) as total_usage,
                       SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful_usage,
                       AVG(execution_time) as avg_execution_time,
                       MIN(execution_time) as min_execution_time,
                       MAX(execution_time) as max_execution_time
                FROM capability_usage 
                WHERE capability_name = ? AND timestamp > ?
            ''', (capability_name, since_date.isoformat())) as cursor:
                row = await cursor.fetchone()
                
                if not row or row[0] == 0:
                    return {
                        "total_usage": 0,
                        "success_rate": 0.0,
                        "avg_execution_time": 0.0,
                        "min_execution_time": 0.0,
                        "max_execution_time": 0.0,
                        "error_patterns": []
                    }
                    
                stats = {
                    "total_usage": row[0],
                    "success_rate": row[1] / row[0] if row[0] > 0 else 0.0,
                    "avg_execution_time": row[2] or 0.0,
                    "min_execution_time": row[3] or 0.0,
                    "max_execution_time": row[4] or 0.0
                }
                
            # 错误模式分析
            async with db.execute('''
                SELECT error_message, COUNT(*) as count
                FROM capability_usage 
                WHERE capability_name = ? AND timestamp > ? AND success = 0
                GROUP BY error_message
                ORDER BY count DESC
                LIMIT 5
            ''', (capability_name, since_date.isoformat())) as cursor:
                error_patterns = []
                async for row in cursor:
                    if row[0]:
                        error_patterns.append({
                            "error": row[0],
                            "count": row[1]
                        })
                        
                stats["error_patterns"] = error_patterns
                
        return stats
        
    async def analyze_missing_capabilities(self, days: int = 30) -> List[Dict[str, Any]]:
        """分析缺失能力的模式"""
        since_date = datetime.now() - timedelta(days=days)
        
        capability_requests = {}
        
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('''
                SELECT missing_capabilities, task_description, timestamp
                FROM task_experiences 
                WHERE timestamp > ? AND missing_capabilities != '[]'
            ''', (since_date.isoformat(),)) as cursor:
                async for row in cursor:
                    missing_caps = json.loads(row[0])
                    task_desc = row[1]
                    timestamp = row[2]
                    
                    for cap in missing_caps:
                        if cap not in capability_requests:
                            capability_requests[cap] = {
                                "capability": cap,
                                "request_count": 0,
                                "task_examples": [],
                                "first_requested": timestamp,
                                "last_requested": timestamp
                            }
                            
                        capability_requests[cap]["request_count"] += 1
                        capability_requests[cap]["last_requested"] = max(
                            capability_requests[cap]["last_requested"], timestamp
                        )
                        
                        if len(capability_requests[cap]["task_examples"]) < 3:
                            capability_requests[cap]["task_examples"].append(task_desc)
                            
        # 按请求次数排序
        sorted_requests = sorted(
            capability_requests.values(),
            key=lambda x: x["request_count"],
            reverse=True
        )
        
        return sorted_requests
        
    async def get_learning_insights(self) -> Dict[str, Any]:
        """获取学习洞察"""
        insights = {
            "task_success_rate": await self.get_task_success_rate(),
            "most_requested_capabilities": await self.analyze_missing_capabilities(),
            "capability_performance": {},
            "improvement_suggestions": []
        }
        
        # 获取所有能力的性能数据
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute('''
                SELECT DISTINCT capability_name FROM capability_usage
            ''') as cursor:
                async for row in cursor:
                    cap_name = row[0]
                    insights["capability_performance"][cap_name] = await self.get_capability_performance(cap_name)
                    
        # 生成改进建议
        insights["improvement_suggestions"] = await self._generate_improvement_suggestions(insights)
        
        return insights
        
    async def _generate_improvement_suggestions(self, insights: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于成功率的建议
        if insights["task_success_rate"] < 0.8:
            suggestions.append("任务成功率较低，建议优化错误处理机制")
            
        # 基于缺失能力的建议
        missing_caps = insights["most_requested_capabilities"]
        if missing_caps:
            top_missing = missing_caps[0]
            suggestions.append(f"建议优先实现 '{top_missing['capability']}' 能力，已被请求 {top_missing['request_count']} 次")
            
        # 基于性能的建议
        for cap_name, perf in insights["capability_performance"].items():
            if perf["success_rate"] < 0.7:
                suggestions.append(f"'{cap_name}' 能力成功率较低({perf['success_rate']:.1%})，需要优化")
            if perf["avg_execution_time"] > 10.0:
                suggestions.append(f"'{cap_name}' 能力执行时间较长({perf['avg_execution_time']:.1f}s)，建议优化性能")
                
        return suggestions
