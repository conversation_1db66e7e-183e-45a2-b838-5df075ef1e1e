import asyncio
import logging
import aiohttp
from typing import Dict, Any
from urllib.parse import quote

class TranslationCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = "https://api.mymemory.translated.net"
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.session = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self.session

    async def close(self):
        if self.session and not self.session.closed:
            await self.session.close()

    async def translate_text(self, params: Dict[str, Any] = {}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Translate text using MyMemory API
        
        Args:
            params: Dictionary containing translation parameters
                - q: Text to translate (required)
                - langpair: Language pair in format "source|target" (default: "en|zh")
            context: Additional context data (unused in this implementation)
            
        Returns:
            Dictionary containing:
                - status: "success" or "error"
                - result: Translated text if successful
                - error: Error message if failed
        """
        result = {
            "status": "error",
            "result": None,
            "error": None
        }
        
        try:
            # Validate required parameters
            if "q" not in params or not params["q"]:
                result["error"] = "Missing required parameter 'q' (text to translate)"
                return result
                
            # Prepare parameters
            langpair = params.get("langpair", "en|zh")
            if "|" not in langpair:
                result["error"] = "Invalid langpair format, expected 'source|target'"
                return result
                
            # Build URL
            encoded_text = quote(params["q"])
            url = f"{self.base_url}/get?q={encoded_text}&langpair={langpair}"
            
            self.logger.debug(f"Translation request URL: {url}")
            
            # Make API request
            session = await self._get_session()
            async with session.get(url) as response:
                if response.status != 200:
                    error_msg = f"API request failed with status {response.status}"
                    self.logger.error(error_msg)
                    result["error"] = error_msg
                    return result
                    
                data = await response.json()
                self.logger.debug(f"API response: {data}")
                
                # Parse response
                if "responseData" not in data or "translatedText" not in data["responseData"]:
                    result["error"] = "Invalid API response format"
                    return result
                    
                translated_text = data["responseData"]["translatedText"]
                if not translated_text:
                    result["error"] = "Empty translation result"
                    return result
                    
                result["status"] = "success"
                result["result"] = translated_text
                return result
                
        except aiohttp.ClientError as e:
            error_msg = f"Network error occurred: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result["error"] = error_msg
            return result
            
        except asyncio.TimeoutError:
            error_msg = "Request timed out"
            self.logger.error(error_msg)
            result["error"] = error_msg
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error occurred: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result["error"] = error_msg
            return result