#!/usr/bin/env python3
"""
自进化智能体演示启动脚本
"""
import asyncio
import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自进化智能体演示")
    parser.add_argument(
        "--mode",
        choices=["demo", "interactive", "test"],
        default="demo",
        help="运行模式: demo(演示), interactive(交互), test(测试)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细日志"
    )
    
    args = parser.parse_args()
    
    if args.mode == "demo":
        print("🚀 启动自进化智能体演示...")
        from examples.demo_scenario import main as demo_main
        asyncio.run(demo_main())
        
    elif args.mode == "interactive":
        print("🚀 启动自进化智能体交互模式...")
        from main import main as interactive_main
        asyncio.run(interactive_main())
        
    elif args.mode == "test":
        print("🚀 启动自进化智能体测试...")
        from test_agent import main as test_main
        asyncio.run(test_main())


if __name__ == "__main__":
    main()
