"""
热更新机制 - 支持运行时动态加载新功能
"""
import asyncio
import logging
import importlib
import sys
import threading
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import time


class CapabilityFileHandler(FileSystemEventHandler):
    """能力文件变化处理器"""
    
    def __init__(self, hot_reloader):
        self.hot_reloader = hot_reloader
        self.logger = logging.getLogger(__name__)
        
    def on_modified(self, event):
        """文件修改事件"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        
        # 只处理Python文件
        if file_path.suffix != '.py':
            return
            
        # 只处理能力模块文件
        if 'capabilities' not in file_path.parts:
            return
            
        self.logger.info(f"检测到能力文件变化: {file_path}")
        
        # 异步处理文件变化
        asyncio.create_task(self.hot_reloader.handle_file_change(file_path))
        
    def on_created(self, event):
        """文件创建事件"""
        self.on_modified(event)


class HotReloader:
    """
    热更新器
    
    监控能力模块文件的变化，自动重新加载更新的模块
    """
    
    def __init__(self, capability_registry, task_executor):
        self.logger = logging.getLogger(__name__)
        self.capability_registry = capability_registry
        self.task_executor = task_executor
        
        # 文件监控
        self.observer = Observer()
        self.file_handler = CapabilityFileHandler(self)
        
        # 重载状态
        self.reloading_capabilities: Dict[str, bool] = {}
        self.reload_callbacks: List[Callable] = []
        
        # 防抖动
        self.last_reload_time: Dict[str, float] = {}
        self.debounce_delay = 1.0  # 1秒防抖动
        
    async def start_monitoring(self):
        """开始监控文件变化"""
        capabilities_dir = Path("src/capabilities")
        if not capabilities_dir.exists():
            capabilities_dir.mkdir(parents=True, exist_ok=True)
            
        self.observer.schedule(
            self.file_handler,
            str(capabilities_dir),
            recursive=True
        )
        
        self.observer.start()
        self.logger.info("开始监控能力文件变化")
        
    def stop_monitoring(self):
        """停止监控"""
        self.observer.stop()
        self.observer.join()
        self.logger.info("停止监控能力文件变化")
        
    async def handle_file_change(self, file_path: Path):
        """处理文件变化"""
        try:
            # 提取能力名称
            capability_name = file_path.stem
            
            # 防抖动检查
            current_time = time.time()
            if capability_name in self.last_reload_time:
                if current_time - self.last_reload_time[capability_name] < self.debounce_delay:
                    return
                    
            self.last_reload_time[capability_name] = current_time
            
            # 检查是否正在重载
            if self.reloading_capabilities.get(capability_name, False):
                self.logger.info(f"能力 {capability_name} 正在重载中，跳过")
                return
                
            self.logger.info(f"开始热更新能力: {capability_name}")
            await self.reload_capability(capability_name)
            
        except Exception as e:
            self.logger.error(f"处理文件变化失败: {e}")
            
    async def reload_capability(self, capability_name: str) -> bool:
        """
        重新加载指定能力
        
        Args:
            capability_name: 能力名称
            
        Returns:
            重载是否成功
        """
        if self.reloading_capabilities.get(capability_name, False):
            self.logger.warning(f"能力 {capability_name} 已在重载中")
            return False
            
        self.reloading_capabilities[capability_name] = True
        
        try:
            # 1. 检查能力是否存在
            capability_info = await self.capability_registry.get_capability_info(capability_name)
            if not capability_info:
                self.logger.warning(f"能力 {capability_name} 不存在于注册表中")
                return False
                
            # 2. 备份当前状态
            backup_info = await self._backup_capability_state(capability_name)
            
            # 3. 卸载旧模块
            await self._unload_capability_module(capability_name, capability_info.module_path)
            
            # 4. 重新加载模块
            success = await self._reload_capability_module(capability_name, capability_info.module_path)
            
            if success:
                # 5. 更新任务执行器中的能力实例
                await self.task_executor.reload_capability(capability_name)
                
                # 6. 更新注册表状态
                await self.capability_registry.update_capability_status(capability_name, "active")
                
                # 7. 通知回调
                await self._notify_reload_callbacks(capability_name, "success")
                
                self.logger.info(f"成功热更新能力: {capability_name}")
                return True
            else:
                # 回滚
                await self._rollback_capability(capability_name, backup_info)
                return False
                
        except Exception as e:
            self.logger.error(f"热更新能力失败 {capability_name}: {e}")
            
            # 尝试回滚
            try:
                backup_info = await self._backup_capability_state(capability_name)
                await self._rollback_capability(capability_name, backup_info)
            except:
                pass
                
            await self._notify_reload_callbacks(capability_name, "error", str(e))
            return False
            
        finally:
            self.reloading_capabilities[capability_name] = False
            
    async def _backup_capability_state(self, capability_name: str) -> Dict[str, Any]:
        """备份能力状态"""
        backup = {
            "capability_name": capability_name,
            "timestamp": time.time()
        }
        
        # 备份任务执行器中的实例
        if hasattr(self.task_executor, 'loaded_capabilities'):
            if capability_name in self.task_executor.loaded_capabilities:
                backup["had_instance"] = True
            else:
                backup["had_instance"] = False
        else:
            backup["had_instance"] = False
            
        return backup
        
    async def _unload_capability_module(self, capability_name: str, module_path: str):
        """卸载能力模块"""
        # 从任务执行器中移除实例
        if hasattr(self.task_executor, 'loaded_capabilities'):
            if capability_name in self.task_executor.loaded_capabilities:
                del self.task_executor.loaded_capabilities[capability_name]
                
        # 从sys.modules中移除模块
        if module_path in sys.modules:
            del sys.modules[module_path]
            
        self.logger.debug(f"卸载模块: {module_path}")
        
    async def _reload_capability_module(self, capability_name: str, module_path: str) -> bool:
        """重新加载能力模块"""
        try:
            # 重新导入模块
            module = importlib.import_module(module_path)
            
            # 验证模块结构
            class_name = self._get_capability_class_name(capability_name)
            if not hasattr(module, class_name):
                raise ValueError(f"模块中未找到类: {class_name}")
                
            capability_class = getattr(module, class_name)
            
            # 尝试实例化以验证
            test_instance = capability_class()
            
            self.logger.debug(f"成功重载模块: {module_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"重载模块失败 {module_path}: {e}")
            return False
            
    def _get_capability_class_name(self, capability_name: str) -> str:
        """获取能力类名"""
        words = capability_name.split('_')
        class_name = ''.join(word.capitalize() for word in words) + 'Capability'
        return class_name
        
    async def _rollback_capability(self, capability_name: str, backup_info: Dict[str, Any]):
        """回滚能力状态"""
        self.logger.warning(f"回滚能力状态: {capability_name}")
        
        # 更新状态为错误
        await self.capability_registry.update_capability_status(capability_name, "error")
        
        # TODO: 实现更完整的回滚逻辑
        
    async def _notify_reload_callbacks(self, capability_name: str, status: str, error: str = None):
        """通知重载回调"""
        for callback in self.reload_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(capability_name, status, error)
                else:
                    callback(capability_name, status, error)
            except Exception as e:
                self.logger.error(f"回调通知失败: {e}")
                
    def add_reload_callback(self, callback: Callable):
        """添加重载回调"""
        self.reload_callbacks.append(callback)
        
    def remove_reload_callback(self, callback: Callable):
        """移除重载回调"""
        if callback in self.reload_callbacks:
            self.reload_callbacks.remove(callback)
            
    async def reload_all_capabilities(self) -> Dict[str, bool]:
        """重新加载所有能力"""
        capabilities = self.capability_registry.list_capabilities()
        results = {}
        
        for capability_name in capabilities:
            results[capability_name] = await self.reload_capability(capability_name)
            
        return results
        
    async def get_reload_status(self) -> Dict[str, Any]:
        """获取重载状态"""
        return {
            "monitoring": self.observer.is_alive(),
            "reloading_capabilities": dict(self.reloading_capabilities),
            "last_reload_times": dict(self.last_reload_time),
            "callback_count": len(self.reload_callbacks)
        }
        
    async def force_reload_capability(self, capability_name: str) -> bool:
        """强制重新加载能力（忽略防抖动）"""
        # 清除防抖动时间
        if capability_name in self.last_reload_time:
            del self.last_reload_time[capability_name]
            
        return await self.reload_capability(capability_name)
