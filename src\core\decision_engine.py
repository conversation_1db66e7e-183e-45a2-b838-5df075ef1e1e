"""
决策引擎 - 智能决策和策略优化
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import time


class DecisionType(Enum):
    CAPABILITY_SELECTION = "capability_selection"
    EXECUTION_STRATEGY = "execution_strategy"
    ERROR_HANDLING = "error_handling"
    OPTIMIZATION = "optimization"


@dataclass
class Decision:
    """决策结果"""
    type: DecisionType
    action: str
    confidence: float
    reasoning: str
    alternatives: List[str]
    metadata: Dict[str, Any]


class DecisionEngine:
    """
    决策引擎
    
    负责在任务执行过程中做出智能决策：
    1. 选择最佳的能力组合
    2. 优化执行策略
    3. 处理异常情况
    4. 学习和改进决策
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.decision_history = []
        self.performance_metrics = {}
        
    async def decide_capability_selection(
        self, 
        task_description: str,
        available_capabilities: List[str],
        required_capabilities: List[str]
    ) -> Decision:
        """
        决定能力选择策略
        
        Args:
            task_description: 任务描述
            available_capabilities: 可用能力列表
            required_capabilities: 所需能力列表
            
        Returns:
            决策结果
        """
        self.logger.info("进行能力选择决策")
        
        # 检查能力覆盖率
        missing_capabilities = [
            cap for cap in required_capabilities 
            if cap not in available_capabilities
        ]
        
        if not missing_capabilities:
            # 所有能力都可用
            decision = Decision(
                type=DecisionType.CAPABILITY_SELECTION,
                action="proceed_with_available",
                confidence=0.9,
                reasoning="所有必需能力都可用，可以直接执行",
                alternatives=["request_additional_capabilities"],
                metadata={
                    "available_capabilities": available_capabilities,
                    "required_capabilities": required_capabilities
                }
            )
        else:
            # 有缺失能力
            # 检查是否有替代方案
            alternatives = await self._find_capability_alternatives(
                missing_capabilities, available_capabilities
            )
            
            if alternatives:
                decision = Decision(
                    type=DecisionType.CAPABILITY_SELECTION,
                    action="use_alternatives",
                    confidence=0.7,
                    reasoning=f"使用替代能力: {alternatives}",
                    alternatives=["request_missing_capabilities"],
                    metadata={
                        "missing_capabilities": missing_capabilities,
                        "alternatives": alternatives
                    }
                )
            else:
                decision = Decision(
                    type=DecisionType.CAPABILITY_SELECTION,
                    action="request_missing_capabilities",
                    confidence=0.8,
                    reasoning=f"需要请求缺失能力: {missing_capabilities}",
                    alternatives=["partial_execution"],
                    metadata={
                        "missing_capabilities": missing_capabilities
                    }
                )
        
        self._record_decision(decision)
        return decision
        
    async def decide_execution_strategy(
        self,
        task_complexity: str,
        available_resources: Dict[str, Any],
        time_constraints: Optional[int] = None
    ) -> Decision:
        """
        决定执行策略
        
        Args:
            task_complexity: 任务复杂度 (simple/medium/complex)
            available_resources: 可用资源
            time_constraints: 时间约束（秒）
            
        Returns:
            决策结果
        """
        self.logger.info(f"进行执行策略决策，复杂度: {task_complexity}")
        
        if task_complexity == "simple":
            strategy = "sequential"
            confidence = 0.9
            reasoning = "简单任务使用顺序执行"
        elif task_complexity == "medium":
            if time_constraints and time_constraints < 60:
                strategy = "parallel"
                confidence = 0.8
                reasoning = "中等复杂度任务在时间约束下使用并行执行"
            else:
                strategy = "sequential"
                confidence = 0.7
                reasoning = "中等复杂度任务使用顺序执行以确保稳定性"
        else:  # complex
            strategy = "hybrid"
            confidence = 0.6
            reasoning = "复杂任务使用混合策略，部分并行部分顺序"
            
        decision = Decision(
            type=DecisionType.EXECUTION_STRATEGY,
            action=strategy,
            confidence=confidence,
            reasoning=reasoning,
            alternatives=["sequential", "parallel", "hybrid"],
            metadata={
                "task_complexity": task_complexity,
                "time_constraints": time_constraints,
                "available_resources": available_resources
            }
        )
        
        self._record_decision(decision)
        return decision
        
    async def decide_error_handling(
        self,
        error_type: str,
        error_message: str,
        retry_count: int,
        context: Dict[str, Any]
    ) -> Decision:
        """
        决定错误处理策略
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            retry_count: 重试次数
            context: 上下文信息
            
        Returns:
            决策结果
        """
        self.logger.info(f"进行错误处理决策: {error_type}")
        
        if retry_count >= 3:
            action = "abort"
            confidence = 0.9
            reasoning = "重试次数过多，终止执行"
        elif error_type in ["network_error", "timeout"]:
            action = "retry_with_backoff"
            confidence = 0.8
            reasoning = "网络错误或超时，使用退避重试"
        elif error_type == "capability_error":
            action = "request_alternative"
            confidence = 0.7
            reasoning = "能力错误，请求替代方案"
        elif error_type == "permission_error":
            action = "request_permission"
            confidence = 0.6
            reasoning = "权限错误，请求用户授权"
        else:
            action = "retry"
            confidence = 0.5
            reasoning = "未知错误，尝试重试"
            
        decision = Decision(
            type=DecisionType.ERROR_HANDLING,
            action=action,
            confidence=confidence,
            reasoning=reasoning,
            alternatives=["retry", "abort", "request_help"],
            metadata={
                "error_type": error_type,
                "error_message": error_message,
                "retry_count": retry_count,
                "context": context
            }
        )
        
        self._record_decision(decision)
        return decision
        
    async def optimize_performance(
        self,
        performance_data: Dict[str, Any]
    ) -> Decision:
        """
        性能优化决策
        
        Args:
            performance_data: 性能数据
            
        Returns:
            优化决策
        """
        self.logger.info("进行性能优化决策")
        
        # 分析性能瓶颈
        bottlenecks = self._identify_bottlenecks(performance_data)
        
        if not bottlenecks:
            decision = Decision(
                type=DecisionType.OPTIMIZATION,
                action="maintain_current",
                confidence=0.8,
                reasoning="性能表现良好，维持当前策略",
                alternatives=["proactive_optimization"],
                metadata={"performance_data": performance_data}
            )
        else:
            # 选择优化策略
            optimization_strategy = self._select_optimization_strategy(bottlenecks)
            
            decision = Decision(
                type=DecisionType.OPTIMIZATION,
                action=optimization_strategy,
                confidence=0.7,
                reasoning=f"检测到瓶颈: {bottlenecks}，应用优化策略",
                alternatives=["alternative_optimization", "manual_tuning"],
                metadata={
                    "bottlenecks": bottlenecks,
                    "performance_data": performance_data
                }
            )
            
        self._record_decision(decision)
        return decision
        
    async def _find_capability_alternatives(
        self,
        missing_capabilities: List[str],
        available_capabilities: List[str]
    ) -> Dict[str, str]:
        """寻找能力替代方案"""
        alternatives = {}
        
        # 简单的替代逻辑
        capability_alternatives = {
            "web_search": ["web_scraping", "api_integration"],
            "web_scraping": ["web_search", "api_integration"],
            "database_operations": ["file_operations"],
            "email_operations": ["api_integration"],
        }
        
        for missing in missing_capabilities:
            if missing in capability_alternatives:
                for alt in capability_alternatives[missing]:
                    if alt in available_capabilities:
                        alternatives[missing] = alt
                        break
                        
        return alternatives
        
    def _identify_bottlenecks(self, performance_data: Dict[str, Any]) -> List[str]:
        """识别性能瓶颈"""
        bottlenecks = []
        
        # 检查执行时间
        if performance_data.get("execution_time", 0) > 30:
            bottlenecks.append("slow_execution")
            
        # 检查内存使用
        if performance_data.get("memory_usage", 0) > 0.8:
            bottlenecks.append("high_memory")
            
        # 检查错误率
        if performance_data.get("error_rate", 0) > 0.1:
            bottlenecks.append("high_error_rate")
            
        return bottlenecks
        
    def _select_optimization_strategy(self, bottlenecks: List[str]) -> str:
        """选择优化策略"""
        if "slow_execution" in bottlenecks:
            return "parallel_execution"
        elif "high_memory" in bottlenecks:
            return "memory_optimization"
        elif "high_error_rate" in bottlenecks:
            return "error_reduction"
        else:
            return "general_optimization"
            
    def _record_decision(self, decision: Decision):
        """记录决策"""
        decision_record = {
            "timestamp": time.time(),
            "decision": decision,
        }
        self.decision_history.append(decision_record)
        
        # 限制历史记录数量
        if len(self.decision_history) > 1000:
            self.decision_history = self.decision_history[-500:]
            
    def get_decision_history(self) -> List[Dict[str, Any]]:
        """获取决策历史"""
        return [
            {
                "timestamp": record["timestamp"],
                "type": record["decision"].type.value,
                "action": record["decision"].action,
                "confidence": record["decision"].confidence,
                "reasoning": record["decision"].reasoning
            }
            for record in self.decision_history
        ]
        
    def analyze_decision_patterns(self) -> Dict[str, Any]:
        """分析决策模式"""
        if not self.decision_history:
            return {"message": "暂无决策历史"}
            
        # 统计决策类型分布
        type_counts = {}
        action_counts = {}
        confidence_sum = 0
        
        for record in self.decision_history:
            decision = record["decision"]
            
            decision_type = decision.type.value
            type_counts[decision_type] = type_counts.get(decision_type, 0) + 1
            
            action = decision.action
            action_counts[action] = action_counts.get(action, 0) + 1
            
            confidence_sum += decision.confidence
            
        avg_confidence = confidence_sum / len(self.decision_history)
        
        return {
            "total_decisions": len(self.decision_history),
            "decision_types": type_counts,
            "common_actions": action_counts,
            "average_confidence": avg_confidence
        }
