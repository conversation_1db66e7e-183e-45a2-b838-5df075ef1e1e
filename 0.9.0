Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting typer
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl (46 kB)
Requirement already satisfied: click>=8.0.0 in e:\anaconda3\lib\site-packages (from typer) (8.1.7)
Requirement already satisfied: typing-extensions>=3.7.4.3 in e:\anaconda3\lib\site-packages (from typer) (4.11.0)
Collecting shellingham>=1.3.0 (from typer)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Requirement already satisfied: rich>=10.11.0 in e:\anaconda3\lib\site-packages (from typer) (13.7.1)
Requirement already satisfied: colorama in e:\anaconda3\lib\site-packages (from click>=8.0.0->typer) (0.4.6)
Requirement already satisfied: markdown-it-py>=2.2.0 in e:\anaconda3\lib\site-packages (from rich>=10.11.0->typer) (2.2.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in e:\anaconda3\lib\site-packages (from rich>=10.11.0->typer) (2.15.1)
Requirement already satisfied: mdurl~=0.1 in e:\anaconda3\lib\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer) (0.1.0)
Installing collected packages: shellingham, typer
Successfully installed shellingham-1.5.4 typer-0.16.0
