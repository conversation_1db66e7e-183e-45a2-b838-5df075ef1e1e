"""
增强的自进化智能体
集成自主API发现、评估和集成功能
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

from ..core.agent import SelfEvolvingAgent
from .api_discovery_system import APIDiscoverySystem, APIEvaluator, APICandidate
from .api_integrator import APIIntegrator, APIIntegrationPlan

# 尝试导入LLM客户端
try:
    from ..llm.deepseek_client import DeepSeekClient
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False


class EnhancedSelfEvolvingAgent(SelfEvolvingAgent):
    """
    增强的自进化智能体
    具备自主API发现、评估和集成能力
    """
    
    def __init__(self, deepseek_api_key: str = None):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.deepseek_api_key = deepseek_api_key
        
        # 初始化自主发现组件
        self.api_discovery = None
        self.api_evaluator = None
        self.api_integrator = None
        
        # 学习和反馈系统
        self.api_usage_stats = {}
        self.integration_history = []
        self.user_feedback = {}
        
    async def initialize_autonomous_systems(self):
        """初始化自主系统组件"""
        try:
            self.api_discovery = APIDiscoverySystem(self.deepseek_api_key)
            self.api_evaluator = APIEvaluator()
            self.api_integrator = APIIntegrator(self.deepseek_api_key)
            
            # 设置LLM客户端
            if LLM_AVAILABLE and self.deepseek_api_key:
                llm_client = DeepSeekClient(self.deepseek_api_key)
                self.api_evaluator.llm_client = llm_client
                
            self.logger.info("自主系统组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"自主系统初始化失败: {e}")
            raise
            
    async def execute_task_with_autonomous_discovery(self, task_description: str) -> Dict[str, Any]:
        """
        执行任务，如果缺少能力则自主发现和集成API
        
        Args:
            task_description: 任务描述
            
        Returns:
            任务执行结果
        """
        self.logger.info(f"开始执行任务（自主模式）: {task_description}")
        
        try:
            # 首先尝试正常执行
            result = await self.execute_task(task_description)
            
            if result.get('status') == 'success':
                return result
                
        except Exception as e:
            self.logger.info(f"常规执行失败，启动自主发现模式: {e}")
            
        # 如果常规执行失败，启动自主发现模式
        return await self._autonomous_capability_discovery(task_description)
        
    async def _autonomous_capability_discovery(self, task_description: str) -> Dict[str, Any]:
        """自主能力发现和集成流程"""
        try:
            # 1. 分析任务需求
            required_capabilities = await self._analyze_task_requirements(task_description)
            self.logger.info(f"分析得出需要的能力: {required_capabilities}")
            
            # 2. 检查缺失的能力
            missing_capabilities = []
            for capability in required_capabilities:
                if not self.capability_registry.has_capability(capability):
                    missing_capabilities.append(capability)
                    
            if not missing_capabilities:
                self.logger.info("所有必需能力都已存在")
                return await self.execute_task(task_description)
                
            self.logger.info(f"发现缺失能力: {missing_capabilities}")
            
            # 3. 为每个缺失能力自主发现API
            integration_results = []
            for capability in missing_capabilities:
                result = await self._discover_and_integrate_capability(capability, task_description)
                integration_results.append(result)
                
            # 4. 重新尝试执行任务
            if all(r['success'] for r in integration_results):
                self.logger.info("所有能力集成成功，重新执行任务")
                return await self.execute_task(task_description)
            else:
                failed_capabilities = [r['capability'] for r in integration_results if not r['success']]
                return {
                    'status': 'failed',
                    'error': f"无法集成以下能力: {failed_capabilities}",
                    'integration_results': integration_results
                }
                
        except Exception as e:
            self.logger.error(f"自主发现流程失败: {e}")
            return {
                'status': 'failed',
                'error': f"自主发现流程异常: {e}"
            }
            
    async def _analyze_task_requirements(self, task_description: str) -> List[str]:
        """分析任务需求，确定所需能力"""
        # 使用现有的能力发现器
        discovered_capabilities = await self.capability_discoverer.discover_capabilities(task_description)
        return discovered_capabilities
        
    async def _discover_and_integrate_capability(self, capability_name: str, 
                                               task_description: str) -> Dict[str, Any]:
        """为单个能力执行发现和集成流程"""
        integration_result = {
            'capability': capability_name,
            'success': False,
            'api_candidates': [],
            'selected_api': None,
            'integration_plan': None,
            'error': None
        }
        
        try:
            self.logger.info(f"开始为能力 '{capability_name}' 发现API")
            
            # 1. 发现API候选项
            async with self.api_discovery as discovery:
                candidates = await discovery.discover_apis_for_capability(
                    capability_name, task_description
                )
                
            integration_result['api_candidates'] = [
                {
                    'name': c.name,
                    'description': c.description,
                    'url': c.url,
                    'rating': c.rating
                } for c in candidates
            ]
            
            if not candidates:
                integration_result['error'] = "未找到合适的API候选项"
                return integration_result
                
            self.logger.info(f"发现 {len(candidates)} 个API候选项")
            
            # 2. 评估候选项
            best_candidate = await self._select_best_api_candidate(candidates)
            integration_result['selected_api'] = {
                'name': best_candidate.name,
                'url': best_candidate.url,
                'rating': best_candidate.rating
            }
            
            self.logger.info(f"选择最佳API: {best_candidate.name}")
            
            # 3. 创建集成计划
            async with self.api_integrator as integrator:
                integration_plan = await integrator.create_integration_plan(
                    best_candidate, capability_name
                )
                
            integration_result['integration_plan'] = {
                'method': integration_plan.integration_method,
                'complexity': integration_plan.estimated_complexity,
                'dependencies': integration_plan.required_dependencies
            }
            
            # 4. 执行集成
            success = await self._execute_integration_plan(integration_plan)
            integration_result['success'] = success
            
            if success:
                self.logger.info(f"能力 '{capability_name}' 集成成功")
                # 记录集成历史
                self.integration_history.append({
                    'capability': capability_name,
                    'api': best_candidate.name,
                    'timestamp': asyncio.get_event_loop().time(),
                    'success': True
                })
            else:
                integration_result['error'] = "集成执行失败"
                
        except Exception as e:
            self.logger.error(f"能力 '{capability_name}' 发现和集成失败: {e}")
            integration_result['error'] = str(e)
            
        return integration_result
        
    async def _select_best_api_candidate(self, candidates: List[APICandidate]) -> APICandidate:
        """选择最佳API候选项"""
        if not candidates:
            raise ValueError("没有可用的API候选项")
            
        # 对所有候选项进行详细评估
        evaluated_candidates = []
        
        for candidate in candidates:
            try:
                evaluation = await self.api_evaluator.evaluate_api_quality(candidate)
                candidate.rating = evaluation['overall_score']
                evaluated_candidates.append((candidate, evaluation))
                
            except Exception as e:
                self.logger.warning(f"评估候选项 {candidate.name} 失败: {e}")
                evaluated_candidates.append((candidate, {'overall_score': 1.0}))
                
        # 按综合评分排序
        evaluated_candidates.sort(key=lambda x: x[1]['overall_score'], reverse=True)
        
        best_candidate, best_evaluation = evaluated_candidates[0]
        
        self.logger.info(f"选择最佳候选项: {best_candidate.name} (评分: {best_evaluation['overall_score']:.2f})")
        
        return best_candidate
        
    async def _execute_integration_plan(self, plan: APIIntegrationPlan) -> bool:
        """执行集成计划"""
        try:
            self.logger.info(f"开始执行集成计划: {plan.candidate.name}")
            
            # 1. 检查和安装依赖
            await self._ensure_dependencies(plan.required_dependencies)
            
            # 2. 生成能力代码
            capability_code = plan.code_template
            
            # 3. 保存代码文件
            capability_file = Path(f"src/capabilities/{plan.candidate.name.lower().replace(' ', '_')}.py")
            capability_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(capability_file, 'w', encoding='utf-8') as f:
                f.write(capability_code)
                
            self.logger.info(f"能力代码已保存到: {capability_file}")
            
            # 4. 热加载能力
            capability_name = plan.candidate.name.lower().replace(' ', '_')
            success = await self._hot_load_capability(capability_name, capability_file)
            
            if success:
                self.logger.info(f"能力 '{capability_name}' 热加载成功")
                return True
            else:
                self.logger.error(f"能力 '{capability_name}' 热加载失败")
                return False
                
        except Exception as e:
            self.logger.error(f"集成计划执行失败: {e}")
            return False
            
    async def _ensure_dependencies(self, dependencies: List[str]) -> bool:
        """确保依赖包已安装"""
        # 这里可以实现自动安装依赖的逻辑
        # 目前只是记录日志
        if dependencies:
            self.logger.info(f"需要的依赖包: {dependencies}")
            # TODO: 实现自动安装逻辑
            
        return True
        
    async def _hot_load_capability(self, capability_name: str, capability_file: Path) -> bool:
        """热加载新能力"""
        try:
            # 使用现有的热加载机制
            return self.capability_registry.load_capability_from_file(
                capability_name, str(capability_file)
            )
            
        except Exception as e:
            self.logger.error(f"热加载能力失败: {e}")
            return False
            
    def get_autonomous_discovery_stats(self) -> Dict[str, Any]:
        """获取自主发现统计信息"""
        return {
            'integration_history': self.integration_history,
            'api_usage_stats': self.api_usage_stats,
            'total_integrations': len(self.integration_history),
            'successful_integrations': len([h for h in self.integration_history if h['success']]),
            'available_capabilities': self.capability_registry.list_capabilities()
        }
        
    async def provide_integration_feedback(self, capability_name: str, 
                                         feedback: Dict[str, Any]) -> None:
        """提供集成反馈，用于持续改进"""
        self.user_feedback[capability_name] = {
            'feedback': feedback,
            'timestamp': asyncio.get_event_loop().time()
        }
        
        self.logger.info(f"收到能力 '{capability_name}' 的用户反馈")
        
        # 这里可以实现基于反馈的学习和优化逻辑
