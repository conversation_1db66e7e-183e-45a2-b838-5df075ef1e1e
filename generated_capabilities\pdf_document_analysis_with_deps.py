"""
# Pdf Document Analysis 功能文档

## 功能描述
实现pdf_document_analysis功能

## 主要特性
- 数据处理
- 结果输出

## 输入类型
- 文件
- 文本
- 数据

## 输出类型
- 处理结果
- 分析报告

## 使用示例

```python
# 创建能力实例
capability = PdfDocumentAnalysisCapability()

# 执行功能
result = await capability.pdf_document_analysis(
    param1="value1",
    param2="value2"
)

# 检查结果
if result['status'] == 'success':
    data = result['data']
    print("处理成功:", data)
else:
    print("处理失败:", result['error'])
```

## 错误处理
- 输入验证失败时返回错误状态
- 处理过程中的异常会被捕获并记录
- 所有错误都包含详细的错误信息

## 性能要求
高效稳定
"""


import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import PyPDF2
import pdfplumber

class PdfProcessingCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def process_pdf(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        PDF文档处理功能
        """
        self.logger.info("开始PDF处理")
        
        file_path = kwargs.get('file_path')
        operation = kwargs.get('operation', 'extract_text')
        
        if not file_path:
            raise ValueError("需要提供file_path参数")
            
        pdf_path = Path(file_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF文件不存在: {file_path}")
            
        try:
            if operation == 'extract_text':
                return await self._extract_text(pdf_path)
            elif operation == 'get_info':
                return await self._get_pdf_info(pdf_path)
            elif operation == 'extract_pages':
                return await self._extract_pages(pdf_path, kwargs)
            else:
                raise ValueError(f"不支持的操作: {operation}")
                
        except Exception as e:
            self.logger.error(f"PDF处理失败: {e}")
            raise
            
    async def _extract_text(self, pdf_path: Path) -> Dict[str, Any]:
        """提取PDF文本"""
        text_content = []
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                text_content.append({
                    'page': page_num + 1,
                    'text': text.strip()
                })
                
        return {
            'status': 'success',
            'total_pages': len(text_content),
            'content': text_content,
            'full_text': '\n'.join([page['text'] for page in text_content])
        }
        
    async def _get_pdf_info(self, pdf_path: Path) -> Dict[str, Any]:
        """获取PDF信息"""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            info = {
                'status': 'success',
                'file_name': pdf_path.name,
                'file_size': pdf_path.stat().st_size,
                'total_pages': len(pdf_reader.pages),
                'metadata': {}
            }
            
            if pdf_reader.metadata:
                info['metadata'] = {
                    'title': pdf_reader.metadata.get('/Title', ''),
                    'author': pdf_reader.metadata.get('/Author', ''),
                    'subject': pdf_reader.metadata.get('/Subject', ''),
                    'creator': pdf_reader.metadata.get('/Creator', ''),
                    'producer': pdf_reader.metadata.get('/Producer', ''),
                    'creation_date': str(pdf_reader.metadata.get('/CreationDate', '')),
                    'modification_date': str(pdf_reader.metadata.get('/ModDate', ''))
                }
                
        return info
        
    async def _extract_pages(self, pdf_path: Path, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """提取指定页面"""
        start_page = kwargs.get('start_page', 1)
        end_page = kwargs.get('end_page', None)
        
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            
            if end_page is None:
                end_page = total_pages
                
            extracted_pages = []
            
            for page_num in range(start_page - 1, min(end_page, total_pages)):
                page = pdf.pages[page_num]
                
                page_data = {
                    'page_number': page_num + 1,
                    'text': page.extract_text() or '',
                    'tables': [],
                    'images': len(page.images) if hasattr(page, 'images') else 0
                }
                
                # 提取表格
                tables = page.extract_tables()
                if tables:
                    page_data['tables'] = tables
                    
                extracted_pages.append(page_data)
                
        return {
            'status': 'success',
            'total_pages': total_pages,
            'extracted_pages': len(extracted_pages),
            'pages': extracted_pages
        }
