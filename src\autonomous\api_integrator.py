"""
自动API集成器
能够自动解析API文档、生成集成代码、测试验证
"""
import asyncio
import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import aiohttp
from urllib.parse import urljoin, urlparse
from pathlib import Path

# 尝试导入LLM客户端
try:
    from ..llm.deepseek_client import DeepSeekClient
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False

from .api_discovery_system import APICandidate, APISource


@dataclass
class APIIntegrationPlan:
    """API集成计划"""
    candidate: APICandidate
    integration_method: str  # "direct", "wrapper", "adapter"
    required_dependencies: List[str]
    authentication_setup: Dict[str, Any]
    code_template: str
    test_cases: List[Dict[str, Any]]
    estimated_complexity: str  # "simple", "moderate", "complex"
    integration_steps: List[str]


class APIIntegrator:
    """自动API集成器"""
    
    def __init__(self, deepseek_api_key: str = None):
        self.logger = logging.getLogger(__name__)
        self.session = None
        
        # 初始化LLM客户端
        if LLM_AVAILABLE and deepseek_api_key:
            self.llm_client = DeepSeekClient(deepseek_api_key)
            self.llm_available = True
        else:
            self.llm_client = None
            self.llm_available = False
            
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        if self.llm_client:
            await self.llm_client.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        if self.llm_client:
            await self.llm_client.__aexit__(exc_type, exc_val, exc_tb)
            
    async def create_integration_plan(self, candidate: APICandidate, 
                                    capability_name: str) -> APIIntegrationPlan:
        """
        为API候选项创建集成计划
        
        Args:
            candidate: API候选项
            capability_name: 目标能力名称
            
        Returns:
            API集成计划
        """
        self.logger.info(f"为 {candidate.name} 创建集成计划")
        
        # 分析API文档
        api_spec = await self._analyze_api_documentation(candidate)
        
        # 确定集成方法
        integration_method = self._determine_integration_method(candidate, api_spec)
        
        # 分析依赖需求
        dependencies = self._analyze_dependencies(candidate, api_spec)
        
        # 设计认证方案
        auth_setup = self._design_authentication(candidate, api_spec)
        
        # 生成代码模板
        code_template = await self._generate_code_template(candidate, capability_name, api_spec)
        
        # 创建测试用例
        test_cases = self._create_test_cases(candidate, capability_name, api_spec)
        
        # 评估复杂度
        complexity = self._estimate_complexity(candidate, api_spec)
        
        # 生成集成步骤
        steps = self._generate_integration_steps(candidate, integration_method)
        
        plan = APIIntegrationPlan(
            candidate=candidate,
            integration_method=integration_method,
            required_dependencies=dependencies,
            authentication_setup=auth_setup,
            code_template=code_template,
            test_cases=test_cases,
            estimated_complexity=complexity,
            integration_steps=steps
        )
        
        self.logger.info(f"集成计划创建完成，复杂度: {complexity}")
        return plan
        
    async def _analyze_api_documentation(self, candidate: APICandidate) -> Dict[str, Any]:
        """分析API文档"""
        api_spec = {
            "endpoints": [],
            "authentication": {},
            "parameters": {},
            "response_format": {},
            "rate_limits": {},
            "examples": []
        }
        
        if not candidate.documentation_url:
            # 如果没有文档URL，尝试从API URL推断
            api_spec = self._infer_api_spec_from_url(candidate)
            return api_spec
            
        try:
            # 尝试获取文档内容
            async with self.session.get(candidate.documentation_url) as response:
                if response.status == 200:
                    content = await response.text()
                    api_spec = await self._parse_documentation_content(content, candidate)
                    
        except Exception as e:
            self.logger.warning(f"无法获取API文档: {e}")
            api_spec = self._infer_api_spec_from_url(candidate)
            
        return api_spec
        
    def _infer_api_spec_from_url(self, candidate: APICandidate) -> Dict[str, Any]:
        """从URL推断API规格"""
        # 基于已知API的模式推断
        known_patterns = {
            "duckduckgo.com": {
                "endpoints": [{"path": "/", "method": "GET", "params": ["q", "format"]}],
                "authentication": {"type": "none"},
                "response_format": {"type": "json"}
            },
            "openweathermap.org": {
                "endpoints": [{"path": "/data/2.5/weather", "method": "GET", "params": ["q", "appid"]}],
                "authentication": {"type": "api_key", "param": "appid"},
                "response_format": {"type": "json"}
            },
            "api.github.com": {
                "endpoints": [{"path": "/search/repositories", "method": "GET", "params": ["q"]}],
                "authentication": {"type": "bearer", "header": "Authorization"},
                "response_format": {"type": "json"}
            }
        }
        
        domain = urlparse(candidate.url).netloc
        for pattern, spec in known_patterns.items():
            if pattern in domain:
                return spec
                
        # 默认规格
        return {
            "endpoints": [{"path": "/", "method": "GET", "params": []}],
            "authentication": {"type": "unknown"},
            "response_format": {"type": "json"}
        }
        
    async def _parse_documentation_content(self, content: str, 
                                         candidate: APICandidate) -> Dict[str, Any]:
        """解析文档内容"""
        # 这里可以实现更复杂的文档解析逻辑
        # 目前使用简单的模式匹配
        
        api_spec = {
            "endpoints": [],
            "authentication": {},
            "parameters": {},
            "response_format": {},
            "examples": []
        }
        
        # 如果有LLM，使用智能解析
        if self.llm_available:
            try:
                api_spec = await self._llm_parse_documentation(content, candidate)
            except Exception as e:
                self.logger.warning(f"LLM文档解析失败: {e}")
                
        return api_spec
        
    async def _llm_parse_documentation(self, content: str, 
                                     candidate: APICandidate) -> Dict[str, Any]:
        """使用LLM解析API文档"""
        # 限制内容长度以避免token限制
        content_preview = content[:3000] if len(content) > 3000 else content
        
        prompt = f"""
        请分析以下API文档内容，提取关键信息：
        
        API名称: {candidate.name}
        API URL: {candidate.url}
        
        文档内容:
        {content_preview}
        
        请提取以下信息并以JSON格式返回：
        1. 主要端点 (endpoints)
        2. 认证方式 (authentication)
        3. 请求参数 (parameters)
        4. 响应格式 (response_format)
        5. 使用示例 (examples)
        
        返回格式:
        {{
            "endpoints": [
                {{"path": "/endpoint", "method": "GET", "description": "描述"}}
            ],
            "authentication": {{"type": "api_key", "location": "header"}},
            "parameters": {{"param1": "description"}},
            "response_format": {{"type": "json", "structure": "描述"}},
            "examples": ["示例代码"]
        }}
        """
        
        response = await self.llm_client.chat_completion(prompt)
        
        try:
            # 尝试解析JSON响应
            if "```json" in response:
                json_content = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_content)
        except:
            pass
            
        # 如果解析失败，返回基础规格
        return self._infer_api_spec_from_url(candidate)
        
    def _determine_integration_method(self, candidate: APICandidate, 
                                    api_spec: Dict[str, Any]) -> str:
        """确定集成方法"""
        # 基于API复杂度和类型确定集成方法
        
        if candidate.source == APISource.GITHUB:
            return "wrapper"  # GitHub项目通常需要包装
            
        auth_type = api_spec.get("authentication", {}).get("type", "unknown")
        if auth_type in ["none", "api_key"]:
            return "direct"  # 简单认证可以直接集成
        elif auth_type in ["oauth2", "bearer"]:
            return "adapter"  # 复杂认证需要适配器
        else:
            return "wrapper"  # 未知类型使用包装器
            
    def _analyze_dependencies(self, candidate: APICandidate, 
                            api_spec: Dict[str, Any]) -> List[str]:
        """分析依赖需求"""
        dependencies = ["aiohttp"]  # 基础HTTP客户端
        
        # 根据认证类型添加依赖
        auth_type = api_spec.get("authentication", {}).get("type", "unknown")
        if auth_type == "oauth2":
            dependencies.append("authlib")
        elif auth_type == "jwt":
            dependencies.append("pyjwt")
            
        # 根据响应格式添加依赖
        response_type = api_spec.get("response_format", {}).get("type", "json")
        if response_type == "xml":
            dependencies.append("lxml")
        elif response_type == "yaml":
            dependencies.append("pyyaml")
            
        return dependencies
        
    def _design_authentication(self, candidate: APICandidate, 
                             api_spec: Dict[str, Any]) -> Dict[str, Any]:
        """设计认证方案"""
        auth_spec = api_spec.get("authentication", {})
        auth_type = auth_spec.get("type", "unknown")
        
        auth_setup = {
            "type": auth_type,
            "required_credentials": [],
            "setup_instructions": [],
            "code_snippet": ""
        }
        
        if auth_type == "api_key":
            auth_setup["required_credentials"] = ["api_key"]
            auth_setup["setup_instructions"] = [
                "获取API密钥",
                "在请求中添加API密钥参数或头部"
            ]
            auth_setup["code_snippet"] = """
headers = {"X-API-Key": api_key}
# 或
params = {"api_key": api_key}
"""
        elif auth_type == "bearer":
            auth_setup["required_credentials"] = ["access_token"]
            auth_setup["setup_instructions"] = [
                "获取访问令牌",
                "在Authorization头部添加Bearer令牌"
            ]
            auth_setup["code_snippet"] = """
headers = {"Authorization": f"Bearer {access_token}"}
"""
        elif auth_type == "oauth2":
            auth_setup["required_credentials"] = ["client_id", "client_secret"]
            auth_setup["setup_instructions"] = [
                "注册OAuth2应用",
                "实现OAuth2授权流程",
                "获取访问令牌"
            ]
            
        return auth_setup
        
    async def _generate_code_template(self, candidate: APICandidate, 
                                    capability_name: str, 
                                    api_spec: Dict[str, Any]) -> str:
        """生成代码模板"""
        if self.llm_available:
            return await self._llm_generate_code_template(candidate, capability_name, api_spec)
        else:
            return self._generate_basic_code_template(candidate, capability_name, api_spec)
            
    async def _llm_generate_code_template(self, candidate: APICandidate, 
                                        capability_name: str, 
                                        api_spec: Dict[str, Any]) -> str:
        """使用LLM生成代码模板"""
        prompt = f"""
        请为以下API生成Python集成代码模板：
        
        API信息:
        - 名称: {candidate.name}
        - URL: {candidate.url}
        - 描述: {candidate.description}
        
        目标能力: {capability_name}
        
        API规格:
        {json.dumps(api_spec, indent=2)}
        
        请生成一个完整的Python类，包括：
        1. 异步HTTP客户端
        2. 认证处理
        3. 主要方法实现
        4. 错误处理
        5. 日志记录
        
        类名应该是: {capability_name.title().replace('_', '')}Capability
        主要方法名应该符合能力名称，使用**kwargs参数格式
        
        返回完整的Python代码。
        """
        
        response = await self.llm_client.chat_completion(prompt)
        
        # 提取代码块
        if "```python" in response:
            code = response.split("```python")[1].split("```")[0].strip()
            return code
        elif "```" in response:
            code = response.split("```")[1].split("```")[0].strip()
            return code
        else:
            return response.strip()
            
    def _generate_basic_code_template(self, candidate: APICandidate, 
                                    capability_name: str, 
                                    api_spec: Dict[str, Any]) -> str:
        """生成基础代码模板"""
        class_name = capability_name.title().replace('_', '') + 'Capability'
        method_name = capability_name
        
        template = f'''
import asyncio
import logging
import aiohttp
from typing import Dict, Any, Optional

class {class_name}:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = "{candidate.url}"
        
    async def {method_name}(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        {candidate.description}
        """
        self.logger.info("Starting {method_name}")
        
        try:
            async with aiohttp.ClientSession() as session:
                # 构建请求参数
                params = {{}}
                headers = {{}}
                
                # 添加认证信息
                # TODO: 根据API要求添加认证
                
                # 发送请求
                async with session.get(self.base_url, params=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result
                    else:
                        raise Exception(f"API request failed with status {{response.status}}")
                        
        except Exception as e:
            self.logger.error(f"Failed to execute {method_name}: {{e}}")
            raise
'''
        return template.strip()
        
    def _create_test_cases(self, candidate: APICandidate, 
                          capability_name: str, 
                          api_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建测试用例"""
        test_cases = []
        
        # 基础功能测试
        basic_test = {
            "name": f"test_{capability_name}_basic",
            "description": f"测试{capability_name}基础功能",
            "input": {"query": "test"},
            "expected_type": "dict",
            "assertions": ["result is not None", "isinstance(result, dict)"]
        }
        test_cases.append(basic_test)
        
        # 错误处理测试
        error_test = {
            "name": f"test_{capability_name}_error_handling",
            "description": f"测试{capability_name}错误处理",
            "input": {},  # 空输入应该触发错误
            "expected_exception": "ValueError",
            "assertions": ["ValueError is raised"]
        }
        test_cases.append(error_test)
        
        return test_cases
        
    def _estimate_complexity(self, candidate: APICandidate, 
                           api_spec: Dict[str, Any]) -> str:
        """评估集成复杂度"""
        complexity_score = 0
        
        # 认证复杂度
        auth_type = api_spec.get("authentication", {}).get("type", "unknown")
        auth_complexity = {
            "none": 0,
            "api_key": 1,
            "bearer": 2,
            "oauth2": 3,
            "unknown": 2
        }
        complexity_score += auth_complexity.get(auth_type, 2)
        
        # 端点数量
        endpoints = api_spec.get("endpoints", [])
        if len(endpoints) > 5:
            complexity_score += 2
        elif len(endpoints) > 2:
            complexity_score += 1
            
        # 参数复杂度
        params = api_spec.get("parameters", {})
        if len(params) > 10:
            complexity_score += 2
        elif len(params) > 5:
            complexity_score += 1
            
        # 返回复杂度评估
        if complexity_score <= 2:
            return "simple"
        elif complexity_score <= 5:
            return "moderate"
        else:
            return "complex"
            
    def _generate_integration_steps(self, candidate: APICandidate, 
                                  integration_method: str) -> List[str]:
        """生成集成步骤"""
        base_steps = [
            "分析API文档和要求",
            "安装必要的依赖包",
            "配置认证信息",
            "实现API客户端类",
            "编写测试用例",
            "验证功能正确性",
            "集成到系统中"
        ]
        
        if integration_method == "wrapper":
            base_steps.insert(4, "创建包装器层")
        elif integration_method == "adapter":
            base_steps.insert(4, "实现适配器模式")
            
        return base_steps
