"""
LangGraph 工作流节点
重构后的完整工作流节点实现
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from .agent_state import (
    AgentState, TaskStatus, CapabilityStatus, APIStatus, CodeStatus,
    APICandidate, CodeArtifact, DependencyInfo,
    update_state_status, add_error_message, add_warning_message, add_debug_info,
    update_capability_status, add_api_candidate, add_code_artifact,
    update_dependency_status, get_missing_capabilities, increment_retry_count
)

# 导入现有组件
try:
    from ..llm.deepseek_client import DeepSeekClient
    from ..autonomous.enhanced_agent import EnhancedSelfEvolvingAgent
    from ..autonomous.api_discovery_system import APIDiscoverySystem, APIEvaluator
    from ..autonomous.api_integrator import APIIntegrator
    from ..autonomous.code_generator import CodeGenerator, CapabilityAnalyzer
    from ..autonomous.code_validator import CodeValidator
    from ..autonomous.dependency_manager import DependencyManager
except ImportError as e:
    logging.warning(f"导入组件失败: {e}")

logger = logging.getLogger(__name__)


class WorkflowNodes:
    """LangGraph工作流节点集合"""
    
    def __init__(self, deepseek_api_key: str = None):
        self.deepseek_api_key = deepseek_api_key
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self._init_components()
    
    def _init_components(self):
        """初始化所有组件"""
        try:
            # LLM客户端
            if self.deepseek_api_key:
                self.llm_client = DeepSeekClient(self.deepseek_api_key)
            else:
                self.llm_client = None
                
            # 增强智能体
            self.enhanced_agent = EnhancedSelfEvolvingAgent(self.deepseek_api_key)
            
            # API相关组件
            self.api_discovery = APIDiscoverySystem()
            self.api_evaluator = APIEvaluator()
            self.api_integrator = APIIntegrator()
            
            # 代码相关组件
            self.capability_analyzer = CapabilityAnalyzer()
            self.code_generator = CodeGenerator()
            self.code_validator = CodeValidator()
            
            # 依赖管理
            self.dependency_manager = DependencyManager()
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            # 设置为None，节点中会处理
            self.llm_client = None
            self.enhanced_agent = None
    
    # ==================== 核心工作流节点 ====================
    
    async def task_reception_node(self, state: AgentState) -> AgentState:
        """📝 任务接收节点"""
        node_name = "task_reception"
        self.logger.info(f"🚀 开始任务接收: {state['task_context'].original_task}")
        
        try:
            # 更新状态
            state = update_state_status(state, TaskStatus.ANALYZING, node_name)
            
            # 记录任务开始时间
            state["task_context"].started_at = time.time()
            
            # 添加调试信息
            state = add_debug_info(state, "task_reception", {
                "task_length": len(state['task_context'].original_task),
                "task_id": state['task_context'].task_id,
                "timestamp": time.time()
            }, node_name)
            
            self.logger.info(f"✅ 任务接收完成")
            return state
            
        except Exception as e:
            error_msg = f"任务接收失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            state = update_state_status(state, TaskStatus.FAILED, node_name)
            return state
    
    async def llm_analysis_node(self, state: AgentState) -> AgentState:
        """🧠 LLM任务分析节点"""
        node_name = "llm_analysis"
        self.logger.info(f"🔍 开始LLM任务分析")
        
        try:
            state = update_state_status(state, TaskStatus.ANALYZING, node_name)
            
            # 使用LLM分析任务
            if self.llm_client:
                required_capabilities = await self.llm_client.analyze_task_capabilities(
                    state['task_context'].original_task
                )
                
                # 更新状态
                state["task_context"].llm_analysis = {
                    "required_capabilities": required_capabilities,
                    "analysis_method": "deepseek_llm",
                    "timestamp": time.time()
                }
                state["task_context"].required_capabilities = required_capabilities
                
                # 更新性能指标
                state["performance_metrics"]["api_calls_count"] += 1
                
            else:
                # 回退到基础分析
                self.logger.warning("LLM客户端不可用，使用基础分析")
                basic_capabilities = self._basic_task_analysis(state['task_context'].original_task)
                
                state["task_context"].llm_analysis = {
                    "required_capabilities": basic_capabilities,
                    "analysis_method": "basic_fallback",
                    "timestamp": time.time()
                }
                state["task_context"].required_capabilities = basic_capabilities
            
            # 添加调试信息
            state = add_debug_info(state, "llm_analysis", {
                "required_capabilities": state["task_context"].required_capabilities,
                "analysis_method": state["task_context"].llm_analysis["analysis_method"]
            }, node_name)
            
            self.logger.info(f"✅ LLM分析完成，识别能力: {state['task_context'].required_capabilities}")
            return state
            
        except Exception as e:
            error_msg = f"LLM分析失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state
    
    async def capability_check_node(self, state: AgentState) -> AgentState:
        """🔍 能力检查节点"""
        node_name = "capability_check"
        self.logger.info(f"🔍 开始能力检查")
        
        try:
            state = update_state_status(state, TaskStatus.CAPABILITY_CHECK, node_name)
            
            # 获取所需能力和可用能力
            required_capabilities = state['task_context'].required_capabilities
            
            if self.enhanced_agent:
                available_capabilities = self.enhanced_agent.get_available_capabilities()
            else:
                available_capabilities = []
                self.logger.warning("增强智能体不可用，假设无可用能力")
            
            state["available_capabilities"] = available_capabilities
            
            # 分析缺失能力
            missing_capabilities = []
            for capability in required_capabilities:
                if capability not in available_capabilities:
                    missing_capabilities.append(capability)
                    # 创建能力信息
                    state = update_capability_status(
                        state, capability, CapabilityStatus.MISSING,
                        description=f"执行任务所需的{capability}能力"
                    )
                else:
                    # 标记为可用
                    state = update_capability_status(
                        state, capability, CapabilityStatus.AVAILABLE,
                        description=f"系统已有的{capability}能力"
                    )
            
            state["task_context"].missing_capabilities = missing_capabilities
            
            # 添加调试信息
            state = add_debug_info(state, "capability_check", {
                "required": required_capabilities,
                "available": available_capabilities,
                "missing": missing_capabilities
            }, node_name)
            
            if missing_capabilities:
                self.logger.info(f"🔍 发现缺失能力: {missing_capabilities}")
            else:
                self.logger.info(f"✅ 所有能力都可用")
            
            return state
            
        except Exception as e:
            error_msg = f"能力检查失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state
    
    def _basic_task_analysis(self, task: str) -> List[str]:
        """基础任务分析（LLM不可用时的回退方案）"""
        capabilities = []
        
        # 简单的关键词匹配
        task_lower = task.lower()
        
        if any(word in task_lower for word in ['pdf', '文档', 'document']):
            capabilities.append('pdf_processing')
        
        if any(word in task_lower for word in ['搜索', 'search', '查找']):
            capabilities.append('web_search')
        
        if any(word in task_lower for word in ['天气', 'weather']):
            capabilities.append('weather_query')
        
        if any(word in task_lower for word in ['图片', 'image', '图像']):
            capabilities.append('image_processing')
        
        if any(word in task_lower for word in ['邮件', 'email', '发送']):
            capabilities.append('email_sending')
        
        # 如果没有匹配到特定能力，添加通用能力
        if not capabilities:
            capabilities.append('general_processing')
        
        return capabilities

    async def api_search_node(self, state: AgentState) -> AgentState:
        """🌐 API搜索节点"""
        node_name = "api_search"
        self.logger.info(f"🔍 开始API搜索")

        try:
            state = update_state_status(state, TaskStatus.API_SEARCH, node_name)

            missing_capabilities = get_missing_capabilities(state)

            for capability in missing_capabilities:
                self.logger.info(f"🔍 搜索能力: {capability}")

                # 更新能力状态
                state = update_capability_status(state, capability, CapabilityStatus.SEARCHING_API)

                if self.api_discovery:
                    try:
                        # 搜索相关API
                        search_results = await self.api_discovery.search_apis(
                            capability, max_results=5
                        )

                        # 转换为APICandidate对象
                        api_candidates = []
                        for result in search_results:
                            candidate = APICandidate(
                                name=result.get("name", "Unknown API"),
                                url=result.get("url", ""),
                                description=result.get("description", ""),
                                documentation_url=result.get("docs_url"),
                                quality_score=result.get("score", 0.0),
                                status=APIStatus.FOUND
                            )
                            api_candidates.append(candidate)
                            state = add_api_candidate(state, capability, candidate)

                        if api_candidates:
                            state = update_capability_status(state, capability, CapabilityStatus.API_FOUND)
                            self.logger.info(f"✅ 找到 {len(api_candidates)} 个API候选")
                        else:
                            self.logger.warning(f"⚠️ 未找到合适的API")

                    except Exception as e:
                        error_msg = f"API搜索失败: {e}"
                        self.logger.error(error_msg)
                        state = add_error_message(state, error_msg, node_name)
                else:
                    self.logger.warning("API发现系统不可用")

            # 添加调试信息
            state = add_debug_info(state, "api_search", {
                "searched_capabilities": missing_capabilities,
                "total_apis_found": sum(len(apis) for apis in state["api_search_results"].values())
            }, node_name)

            return state

        except Exception as e:
            error_msg = f"API搜索节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def api_evaluation_node(self, state: AgentState) -> AgentState:
        """⚖️ API评估节点"""
        node_name = "api_evaluation"
        self.logger.info(f"⚖️ 开始API评估")

        try:
            state = update_state_status(state, TaskStatus.API_EVALUATION, node_name)

            for capability, api_candidates in state["api_search_results"].items():
                if not api_candidates:
                    continue

                self.logger.info(f"⚖️ 评估能力 {capability} 的 {len(api_candidates)} 个API")

                if self.api_evaluator:
                    try:
                        # 评估每个API
                        evaluated_apis = []
                        for candidate in api_candidates:
                            evaluation = await self.api_evaluator.evaluate_api(candidate)

                            # 更新候选API信息
                            candidate.quality_score = evaluation.get("quality_score", 0.0)
                            candidate.integration_difficulty = evaluation.get("difficulty", "unknown")
                            candidate.cost = evaluation.get("cost", "unknown")
                            candidate.status = APIStatus.EVALUATED

                            evaluated_apis.append({
                                "candidate": candidate,
                                "evaluation": evaluation
                            })

                        # 选择最佳API
                        if evaluated_apis:
                            best_api = max(evaluated_apis, key=lambda x: x["candidate"].quality_score)

                            # 更新能力信息
                            if capability in state["capabilities"]:
                                state["capabilities"][capability].selected_api = best_api["candidate"]

                            # 保存评估结果
                            state["api_evaluation_results"][capability] = {
                                "best_api": best_api,
                                "all_evaluations": evaluated_apis,
                                "timestamp": time.time()
                            }

                            self.logger.info(f"✅ 选择最佳API: {best_api['candidate'].name} (评分: {best_api['candidate'].quality_score})")

                    except Exception as e:
                        error_msg = f"API评估失败: {e}"
                        self.logger.error(error_msg)
                        state = add_error_message(state, error_msg, node_name)
                else:
                    self.logger.warning("API评估器不可用")

            return state

        except Exception as e:
            error_msg = f"API评估节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def api_integration_node(self, state: AgentState) -> AgentState:
        """🔗 API集成节点"""
        node_name = "api_integration"
        self.logger.info(f"🔗 开始API集成")

        try:
            state = update_state_status(state, TaskStatus.API_INTEGRATION, node_name)

            for capability, evaluation_result in state["api_evaluation_results"].items():
                best_api = evaluation_result["best_api"]["candidate"]

                self.logger.info(f"🔗 集成API: {best_api.name}")

                if self.api_integrator:
                    try:
                        # 生成集成代码
                        integration_result = await self.api_integrator.integrate_api(
                            best_api, capability
                        )

                        if integration_result["success"]:
                            # 创建代码工件
                            code_artifact = CodeArtifact(
                                name=f"{capability}_api_wrapper",
                                code_content=integration_result["code"],
                                dependencies=integration_result.get("dependencies", []),
                                status=CodeStatus.GENERATED
                            )

                            state = add_code_artifact(state, capability, code_artifact)

                            # 更新能力状态
                            state = update_capability_status(
                                state, capability, CapabilityStatus.API_INTEGRATED,
                                api_integration_code=integration_result["code"]
                            )

                            # 保存集成结果
                            state["api_integration_results"][capability] = integration_result

                            self.logger.info(f"✅ API集成成功: {best_api.name}")
                        else:
                            error_msg = f"API集成失败: {integration_result.get('error', 'Unknown error')}"
                            self.logger.error(error_msg)
                            state = add_error_message(state, error_msg, node_name)

                    except Exception as e:
                        error_msg = f"API集成异常: {e}"
                        self.logger.error(error_msg)
                        state = add_error_message(state, error_msg, node_name)
                else:
                    self.logger.warning("API集成器不可用")

            return state

        except Exception as e:
            error_msg = f"API集成节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def code_generation_node(self, state: AgentState) -> AgentState:
        """🤖 自主代码生成节点"""
        node_name = "code_generation"
        self.logger.info(f"🤖 开始自主代码生成")

        try:
            state = update_state_status(state, TaskStatus.CODE_GENERATION, node_name)

            missing_capabilities = get_missing_capabilities(state)

            for capability in missing_capabilities:
                self.logger.info(f"🤖 生成代码: {capability}")

                # 更新能力状态
                state = update_capability_status(state, capability, CapabilityStatus.GENERATING_CODE)

                if self.code_generator:
                    try:
                        # 分析能力需求
                        if self.capability_analyzer:
                            analysis = await self.capability_analyzer.analyze_capability(capability)
                        else:
                            analysis = {"implementation_type": "basic", "complexity": "medium"}

                        # 生成代码
                        generation_result = await self.code_generator.generate_capability_code(
                            capability, analysis
                        )

                        if generation_result["success"]:
                            # 创建代码工件
                            code_artifact = CodeArtifact(
                                name=f"{capability}_implementation",
                                code_content=generation_result["code"],
                                dependencies=generation_result.get("dependencies", []),
                                status=CodeStatus.GENERATED
                            )

                            state = add_code_artifact(state, capability, code_artifact)

                            # 更新能力状态
                            state = update_capability_status(state, capability, CapabilityStatus.CODE_GENERATED)

                            # 更新性能指标
                            state["performance_metrics"]["code_generation_count"] += 1

                            self.logger.info(f"✅ 代码生成成功: {capability}")
                        else:
                            error_msg = f"代码生成失败: {generation_result.get('error', 'Unknown error')}"
                            self.logger.error(error_msg)
                            state = add_error_message(state, error_msg, node_name)

                    except Exception as e:
                        error_msg = f"代码生成异常: {e}"
                        self.logger.error(error_msg)
                        state = add_error_message(state, error_msg, node_name)
                else:
                    self.logger.warning("代码生成器不可用")

            return state

        except Exception as e:
            error_msg = f"代码生成节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def code_validation_node(self, state: AgentState) -> AgentState:
        """✅ 代码验证节点"""
        node_name = "code_validation"
        self.logger.info(f"✅ 开始代码验证")

        try:
            state = update_state_status(state, TaskStatus.CODE_VALIDATION, node_name)

            for capability, code_artifacts in state["code_generation_results"].items():
                for artifact in code_artifacts:
                    if artifact.status == CodeStatus.GENERATED:
                        self.logger.info(f"✅ 验证代码: {artifact.name}")

                        if self.code_validator:
                            try:
                                # 验证代码
                                validation_result = await self.code_validator.validate_code(
                                    artifact.code_content, artifact.language
                                )

                                # 更新工件状态
                                artifact.validation_score = validation_result.get("score", 0.0)
                                artifact.test_results = validation_result

                                if validation_result.get("is_valid", False):
                                    artifact.status = CodeStatus.VALIDATED
                                    state = update_capability_status(state, capability, CapabilityStatus.CODE_VALIDATED)
                                    self.logger.info(f"✅ 代码验证通过: {artifact.name}")
                                else:
                                    artifact.status = CodeStatus.FAILED
                                    artifact.error_message = validation_result.get("error", "Validation failed")
                                    self.logger.error(f"❌ 代码验证失败: {artifact.name}")

                                # 保存验证结果
                                if capability not in state["code_validation_results"]:
                                    state["code_validation_results"][capability] = {}
                                state["code_validation_results"][capability][artifact.name] = validation_result

                            except Exception as e:
                                error_msg = f"代码验证异常: {e}"
                                self.logger.error(error_msg)
                                artifact.status = CodeStatus.FAILED
                                artifact.error_message = str(e)
                                state = add_error_message(state, error_msg, node_name)
                        else:
                            self.logger.warning("代码验证器不可用，跳过验证")
                            artifact.status = CodeStatus.VALIDATED  # 假设通过

            return state

        except Exception as e:
            error_msg = f"代码验证节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def dependency_management_node(self, state: AgentState) -> AgentState:
        """📦 依赖管理节点"""
        node_name = "dependency_management"
        self.logger.info(f"📦 开始依赖管理")

        try:
            state = update_state_status(state, TaskStatus.DEPENDENCY_MANAGEMENT, node_name)

            # 收集所有依赖
            all_dependencies = set()

            # 从代码工件收集依赖
            for capability, artifacts in state["code_generation_results"].items():
                for artifact in artifacts:
                    if artifact.status == CodeStatus.VALIDATED:
                        all_dependencies.update(artifact.dependencies)

            # 从API集成收集依赖
            for capability, integration_result in state["api_integration_results"].items():
                dependencies = integration_result.get("dependencies", [])
                all_dependencies.update(dependencies)

            self.logger.info(f"📦 发现依赖: {list(all_dependencies)}")

            if self.dependency_manager and all_dependencies:
                try:
                    # 检查和安装依赖
                    for package in all_dependencies:
                        self.logger.info(f"📦 处理依赖: {package}")

                        # 检查是否已安装
                        is_installed = await self.dependency_manager.check_dependency(package)

                        dependency_info = DependencyInfo(
                            package_name=package,
                            is_installed=is_installed,
                            installation_method="pip"
                        )

                        if not is_installed:
                            # 尝试安装
                            install_result = await self.dependency_manager.install_dependency(package)

                            if install_result["success"]:
                                dependency_info.is_installed = True
                                dependency_info.installation_time = time.time()
                                state["installed_packages"].append(package)
                                state["performance_metrics"]["dependency_install_count"] += 1
                                self.logger.info(f"✅ 依赖安装成功: {package}")
                            else:
                                dependency_info.error_message = install_result.get("error", "Installation failed")
                                self.logger.error(f"❌ 依赖安装失败: {package}")
                        else:
                            self.logger.info(f"✅ 依赖已存在: {package}")

                        # 更新依赖状态
                        state = update_dependency_status(state, package, dependency_info)

                    # 保存依赖管理结果
                    state["dependency_installation_results"]["global"] = {
                        "total_dependencies": len(all_dependencies),
                        "installed_count": len(state["installed_packages"]),
                        "timestamp": time.time()
                    }

                except Exception as e:
                    error_msg = f"依赖管理异常: {e}"
                    self.logger.error(error_msg)
                    state = add_error_message(state, error_msg, node_name)
            else:
                if not all_dependencies:
                    self.logger.info("📦 无需安装依赖")
                else:
                    self.logger.warning("依赖管理器不可用")

            return state

        except Exception as e:
            error_msg = f"依赖管理节点失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def task_execution_node(self, state: AgentState) -> AgentState:
        """🚀 任务执行节点"""
        node_name = "task_execution"
        self.logger.info(f"🚀 开始任务执行")

        try:
            state = update_state_status(state, TaskStatus.TASK_EXECUTION, node_name)

            # 执行原始任务
            if self.enhanced_agent:
                execution_result = await self.enhanced_agent.execute_task(
                    state['task_context'].original_task
                )

                # 保存执行结果
                state["execution_results"]["main_task"] = execution_result
                state["task_outputs"].append(execution_result)
                state["task_context"].execution_result = execution_result

                self.logger.info(f"✅ 任务执行完成")
            else:
                error_msg = "增强智能体不可用，无法执行任务"
                self.logger.error(error_msg)
                state = add_error_message(state, error_msg, node_name)

                # 创建模拟结果
                state["execution_results"]["main_task"] = {
                    "success": False,
                    "error": error_msg,
                    "result": None
                }

            return state

        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)

            # 保存错误结果
            state["execution_results"]["main_task"] = {
                "success": False,
                "error": str(e),
                "result": None
            }
            return state

    async def result_validation_node(self, state: AgentState) -> AgentState:
        """🔬 结果验证节点"""
        node_name = "result_validation"
        self.logger.info(f"🔬 开始结果验证")

        try:
            state = update_state_status(state, TaskStatus.RESULT_VALIDATION, node_name)

            execution_result = state["execution_results"].get("main_task")

            if execution_result:
                # 验证执行结果
                validation_score = 0.0
                validation_details = {}

                if execution_result.get("success", False):
                    validation_score = 0.8  # 基础分数

                    # 检查结果质量
                    result = execution_result.get("result")
                    if result is not None:
                        validation_score += 0.2
                        validation_details["has_result"] = True
                    else:
                        validation_details["has_result"] = False

                    validation_details["success"] = True
                else:
                    validation_details["success"] = False
                    validation_details["error"] = execution_result.get("error", "Unknown error")

                # 保存验证结果
                state["validation_results"]["main_task"] = {
                    "score": validation_score,
                    "details": validation_details,
                    "timestamp": time.time()
                }

                state["quality_scores"]["overall"] = validation_score

                self.logger.info(f"🔬 结果验证完成，评分: {validation_score}")
            else:
                self.logger.warning("🔬 无执行结果可验证")
                state["validation_results"]["main_task"] = {
                    "score": 0.0,
                    "details": {"error": "No execution result"},
                    "timestamp": time.time()
                }

            return state

        except Exception as e:
            error_msg = f"结果验证失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def experience_learning_node(self, state: AgentState) -> AgentState:
        """🧠 经验学习节点"""
        node_name = "experience_learning"
        self.logger.info(f"🧠 开始经验学习")

        try:
            state = update_state_status(state, TaskStatus.EXPERIENCE_LEARNING, node_name)

            # 收集学习数据
            learning_data = {
                "task_info": {
                    "original_task": state["task_context"].original_task,
                    "required_capabilities": state["task_context"].required_capabilities,
                    "missing_capabilities": state["task_context"].missing_capabilities
                },
                "execution_metrics": {
                    "duration": state["performance_metrics"].get("duration"),
                    "api_calls": state["performance_metrics"].get("api_calls_count", 0),
                    "code_generations": state["performance_metrics"].get("code_generation_count", 0),
                    "dependency_installs": state["performance_metrics"].get("dependency_install_count", 0),
                    "errors": state["performance_metrics"].get("error_count", 0),
                    "retries": state["performance_metrics"].get("retry_count", 0)
                },
                "quality_metrics": {
                    "overall_score": state["quality_scores"].get("overall", 0.0),
                    "validation_results": state["validation_results"]
                },
                "capability_acquisition": {
                    "apis_used": len(state["api_integration_results"]),
                    "code_generated": len(state["code_generation_results"]),
                    "dependencies_installed": len(state["installed_packages"])
                }
            }

            # 生成优化建议
            suggestions = []

            # 基于性能指标的建议
            if state["performance_metrics"].get("error_count", 0) > 0:
                suggestions.append("考虑改进错误处理机制")

            if state["performance_metrics"].get("retry_count", 0) > 1:
                suggestions.append("优化重试策略，减少不必要的重试")

            if state["quality_scores"].get("overall", 0.0) < 0.7:
                suggestions.append("提高结果质量验证标准")

            # 基于能力获取的建议
            if len(state["code_generation_results"]) > len(state["api_integration_results"]):
                suggestions.append("优先考虑API集成而非代码生成")

            if not suggestions:
                suggestions.append("当前执行表现良好，继续保持")

            # 保存学习数据
            state["experience_data"] = learning_data
            state["optimization_suggestions"] = suggestions

            self.logger.info(f"🧠 经验学习完成，生成 {len(suggestions)} 条优化建议")

            return state

        except Exception as e:
            error_msg = f"经验学习失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)
            return state

    async def response_generation_node(self, state: AgentState) -> AgentState:
        """💬 响应生成节点"""
        node_name = "response_generation"
        self.logger.info(f"💬 开始响应生成")

        try:
            state = update_state_status(state, TaskStatus.RESPONSE_GENERATION, node_name)

            # 生成最终响应
            execution_result = state["execution_results"].get("main_task")
            quality_score = state["quality_scores"].get("overall", 0.0)

            if execution_result and execution_result.get("success", False):
                # 成功响应
                response = f"✅ 任务执行成功！\n\n"
                response += f"📋 原始任务: {state['task_context'].original_task}\n"
                response += f"⭐ 质量评分: {quality_score:.2f}/1.0\n"

                result = execution_result.get("result")
                if result:
                    response += f"📊 执行结果: {result}\n"

                # 添加能力获取信息
                if state["api_integration_results"]:
                    response += f"🔗 集成API: {len(state['api_integration_results'])} 个\n"

                if state["code_generation_results"]:
                    response += f"🤖 生成代码: {len(state['code_generation_results'])} 个模块\n"

                if state["installed_packages"]:
                    response += f"📦 安装依赖: {len(state['installed_packages'])} 个包\n"

                # 添加性能信息
                duration = state["performance_metrics"].get("duration")
                if duration:
                    response += f"⏱️ 执行时间: {duration:.2f} 秒\n"

                # 添加优化建议
                if state["optimization_suggestions"]:
                    response += f"\n💡 优化建议:\n"
                    for suggestion in state["optimization_suggestions"]:
                        response += f"  • {suggestion}\n"

            else:
                # 失败响应
                response = f"❌ 任务执行失败\n\n"
                response += f"📋 原始任务: {state['task_context'].original_task}\n"

                if execution_result:
                    error = execution_result.get("error", "Unknown error")
                    response += f"🚫 错误信息: {error}\n"

                # 添加错误历史
                if state["error_messages"]:
                    response += f"\n📝 错误历史:\n"
                    for error_entry in state["error_messages"][-3:]:  # 最近3个错误
                        if isinstance(error_entry, dict):
                            response += f"  • {error_entry.get('message', str(error_entry))}\n"
                        else:
                            response += f"  • {error_entry}\n"

            # 保存最终响应
            state["final_response"] = response
            state["response_metadata"] = {
                "generation_time": time.time(),
                "response_length": len(response),
                "success": execution_result.get("success", False) if execution_result else False
            }

            self.logger.info(f"💬 响应生成完成")

            return state

        except Exception as e:
            error_msg = f"响应生成失败: {e}"
            self.logger.error(error_msg)
            state = add_error_message(state, error_msg, node_name)

            # 生成错误响应
            state["final_response"] = f"❌ 系统错误: {error_msg}"
            state["response_metadata"] = {
                "generation_time": time.time(),
                "success": False,
                "error": error_msg
            }

            return state
