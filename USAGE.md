# 自进化智能体使用指南

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行演示

```bash
# 运行完整演示场景
python run_demo.py --mode demo

# 运行交互模式
python run_demo.py --mode interactive

# 运行测试
python run_demo.py --mode test
```

## 核心概念

### 自进化流程

1. **任务接收**: 智能体接收用户任务
2. **能力分析**: 分析完成任务所需的能力
3. **缺失检测**: 识别当前缺失的能力
4. **API请求**: 向用户请求相应的API或工具
5. **代码生成**: 基于API文档自动生成功能模块
6. **热更新**: 动态加载新功能到系统中
7. **任务执行**: 使用新获得的能力完成任务
8. **经验学习**: 记录和优化执行过程

### 能力类型

智能体可以自动识别和获得以下类型的能力：

- **web_search**: 网络搜索
- **web_scraping**: 网页爬虫
- **file_operations**: 文件操作
- **data_analysis**: 数据分析
- **image_processing**: 图像处理
- **email_operations**: 邮件操作
- **database_operations**: 数据库操作
- **api_integration**: API集成
- **natural_language_processing**: 自然语言处理
- **scheduling**: 任务调度

## 使用示例

### 示例1: 搜索并保存新闻

```python
# 智能体会自动请求搜索API和文件操作能力
task = "搜索最新的AI新闻并保存到文件"
result = await agent.execute_task(task)
```

### 示例2: 天气查询

```python
# 智能体会请求天气API能力
task = "查询北京今天的天气"
result = await agent.execute_task(task)
```

### 示例3: 数据分析

```python
# 智能体会请求文件读取和数据分析能力
task = "分析sales.csv文件中的销售数据"
result = await agent.execute_task(task)
```

## API信息格式

当智能体请求新能力时，您需要提供API信息，格式如下：

### REST API示例

```json
{
  "type": "rest",
  "base_url": "https://api.example.com",
  "authentication": {
    "type": "api_key",
    "key": "your-api-key"
  },
  "endpoints": [
    {
      "name": "search",
      "method": "GET",
      "path": "/search",
      "description": "搜索功能",
      "parameters": [
        {
          "name": "query",
          "type": "str",
          "description": "搜索关键词"
        },
        {
          "name": "limit",
          "type": "int",
          "description": "结果数量",
          "default": "10"
        }
      ]
    }
  ]
}
```

### GraphQL API示例

```json
{
  "type": "graphql",
  "base_url": "https://api.example.com/graphql",
  "authentication": {
    "type": "bearer",
    "token": "your-token"
  },
  "schema": {
    "queries": [
      {
        "name": "getUser",
        "description": "获取用户信息",
        "parameters": [
          {"name": "id", "type": "ID", "description": "用户ID"}
        ]
      }
    ]
  }
}
```

### 本地工具示例

```json
{
  "type": "local",
  "description": "本地文件操作工具",
  "endpoints": [
    {
      "name": "process_file",
      "description": "处理文件",
      "parameters": [
        {"name": "filename", "type": "str", "description": "文件名"},
        {"name": "operation", "type": "str", "description": "操作类型"}
      ]
    }
  ]
}
```

## 高级功能

### 1. 热更新

智能体支持运行时热更新能力模块：

```python
# 手动重新加载能力
await agent.hot_reloader.reload_capability("web_search")

# 强制重新加载（忽略防抖动）
await agent.hot_reloader.force_reload_capability("web_search")
```

### 2. 性能监控

查看能力使用情况和性能指标：

```python
# 获取能力性能统计
performance = await agent.experience_recorder.get_capability_performance("web_search")

# 获取学习洞察
insights = await agent.experience_recorder.get_learning_insights()
```

### 3. 依赖管理

智能体自动管理能力之间的依赖关系：

```python
# 获取依赖关系图
dependency_graph = await agent.capability_registry.get_dependency_graph()

# 解析加载顺序
load_order = await agent.capability_registry.resolve_load_order(["cap1", "cap2", "cap3"])
```

## 配置选项

编辑 `config.yaml` 文件来自定义智能体行为：

```yaml
# 启用/禁用热更新
hot_reload:
  enabled: true
  debounce_delay: 1.0

# 任务执行配置
task_execution:
  max_execution_time: 300
  max_retry_count: 3

# 学习系统配置
learning:
  experience_retention_days: 90
  auto_optimization: true
```

## 故障排除

### 常见问题

1. **模块导入错误**
   - 确保所有依赖已安装
   - 检查Python路径设置

2. **数据库连接错误**
   - 确保data目录存在
   - 检查文件权限

3. **API调用失败**
   - 验证API密钥和URL
   - 检查网络连接

4. **能力加载失败**
   - 检查生成的代码语法
   - 查看日志文件获取详细错误信息

### 调试模式

启用详细日志：

```bash
python run_demo.py --mode interactive --verbose
```

查看日志文件：

```bash
tail -f logs/agent.log
```

## 扩展开发

### 添加新的能力模式

```python
from src.core.capability_discoverer import CapabilityPattern

# 创建新的能力模式
new_pattern = CapabilityPattern(
    name="custom_capability",
    keywords=["自定义", "custom"],
    patterns=[r"自定义.*?功能"],
    description="自定义功能能力",
    priority=2
)

# 添加到发现器
agent.capability_discoverer.add_capability_pattern(new_pattern)
```

### 自定义代码模板

在 `src/code_generation/templates/` 目录下添加新的Jinja2模板文件。

### 添加学习算法

继承 `ExperienceRecorder` 类并实现自定义的学习算法。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
