"""
代码验证系统
自动测试生成的代码，确保功能正确性和安全性
"""
import asyncio
import logging
import ast
import sys
import subprocess
import tempfile
import importlib.util
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

from .code_generator import CodeGenerationPlan


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    syntax_check: bool
    security_check: bool
    functionality_check: bool
    performance_check: bool
    test_results: List[Dict[str, Any]]
    errors: List[str]
    warnings: List[str]
    score: float  # 0-100分
    recommendations: List[str]


class CodeValidator:
    """代码验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 安全检查规则
        self.security_patterns = [
            "eval(",
            "exec(",
            "os.system(",
            "subprocess.call(",
            "__import__(",
            "open(",  # 需要检查文件操作
            "input(",  # 可能的安全风险
            "raw_input(",
        ]
        
        # 危险模块
        self.dangerous_modules = [
            "os",
            "subprocess", 
            "sys",
            "importlib",
            "pickle",  # 反序列化风险
            "marshal",
            "shelve"
        ]
        
    async def validate_code(self, plan: CodeGenerationPlan) -> ValidationResult:
        """
        全面验证生成的代码
        
        Args:
            plan: 代码生成计划
            
        Returns:
            验证结果
        """
        self.logger.info(f"开始验证代码: {plan.capability_name}")
        
        result = ValidationResult(
            is_valid=False,
            syntax_check=False,
            security_check=False,
            functionality_check=False,
            performance_check=False,
            test_results=[],
            errors=[],
            warnings=[],
            score=0.0,
            recommendations=[]
        )
        
        try:
            # 1. 语法检查
            result.syntax_check = await self._check_syntax(plan.code_template, result)
            
            # 2. 安全检查
            result.security_check = await self._check_security(plan.code_template, result)
            
            # 3. 功能检查
            result.functionality_check = await self._check_functionality(plan, result)
            
            # 4. 性能检查
            result.performance_check = await self._check_performance(plan, result)
            
            # 5. 运行测试用例
            await self._run_test_cases(plan, result)
            
            # 6. 计算总分
            result.score = self._calculate_score(result)
            
            # 7. 生成建议
            result.recommendations = self._generate_recommendations(result, plan)
            
            # 8. 判断是否通过验证
            result.is_valid = (
                result.syntax_check and 
                result.security_check and 
                result.functionality_check and
                result.score >= 70.0  # 至少70分才算通过
            )
            
            self.logger.info(f"代码验证完成，评分: {result.score:.1f}, 通过: {result.is_valid}")
            
        except Exception as e:
            self.logger.error(f"代码验证异常: {e}")
            result.errors.append(f"验证过程异常: {e}")
            
        return result
        
    async def _check_syntax(self, code: str, result: ValidationResult) -> bool:
        """检查语法正确性"""
        try:
            # 使用AST解析代码
            ast.parse(code)
            self.logger.info("语法检查通过")
            return True
            
        except SyntaxError as e:
            error_msg = f"语法错误: {e.msg} (行 {e.lineno})"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
            return False
            
        except Exception as e:
            error_msg = f"语法检查异常: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
            return False
            
    async def _check_security(self, code: str, result: ValidationResult) -> bool:
        """检查安全性"""
        security_issues = []
        
        # 检查危险函数调用
        for pattern in self.security_patterns:
            if pattern in code:
                if pattern == "open(":
                    # 文件操作需要更细致的检查
                    if self._is_safe_file_operation(code):
                        result.warnings.append(f"检测到文件操作，请确保路径安全")
                    else:
                        security_issues.append(f"不安全的文件操作: {pattern}")
                else:
                    security_issues.append(f"检测到潜在危险函数: {pattern}")
                    
        # 检查导入的模块
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in self.dangerous_modules:
                            result.warnings.append(f"导入了需要谨慎使用的模块: {alias.name}")
                elif isinstance(node, ast.ImportFrom):
                    if node.module in self.dangerous_modules:
                        result.warnings.append(f"从危险模块导入: {node.module}")
                        
        except Exception as e:
            result.warnings.append(f"模块导入检查异常: {e}")
            
        if security_issues:
            result.errors.extend(security_issues)
            self.logger.warning(f"发现安全问题: {security_issues}")
            return False
        else:
            self.logger.info("安全检查通过")
            return True
            
    def _is_safe_file_operation(self, code: str) -> bool:
        """检查文件操作是否安全"""
        # 简单的启发式检查
        dangerous_patterns = [
            "open('/'",
            "open('/etc'",
            "open('/bin'",
            "open('/usr'",
            "open('/sys'",
            "open('/proc'",
            "mode='w'",  # 写模式需要特别注意
            "mode='a'",  # 追加模式
        ]
        
        return not any(pattern in code for pattern in dangerous_patterns)
        
    async def _check_functionality(self, plan: CodeGenerationPlan, 
                                 result: ValidationResult) -> bool:
        """检查功能完整性"""
        functionality_issues = []
        
        # 检查必要的方法是否存在
        if plan.capability_name.lower() not in plan.code_template.lower():
            functionality_issues.append(f"代码中缺少主要方法: {plan.capability_name}")
            
        # 检查错误处理
        if "try:" not in plan.code_template or "except" not in plan.code_template:
            functionality_issues.append("代码缺少错误处理机制")
            
        # 检查日志记录
        if "logging" not in plan.code_template and "logger" not in plan.code_template:
            result.warnings.append("建议添加日志记录功能")
            
        # 检查返回值格式
        if "return" not in plan.code_template:
            functionality_issues.append("方法缺少返回值")
            
        # 检查类型提示
        if "typing" not in plan.code_template and "Dict" not in plan.code_template:
            result.warnings.append("建议添加类型提示")
            
        if functionality_issues:
            result.errors.extend(functionality_issues)
            self.logger.warning(f"功能检查发现问题: {functionality_issues}")
            return False
        else:
            self.logger.info("功能检查通过")
            return True
            
    async def _check_performance(self, plan: CodeGenerationPlan, 
                               result: ValidationResult) -> bool:
        """检查性能相关问题"""
        performance_warnings = []
        
        # 检查是否使用了异步编程
        if "async" not in plan.code_template:
            performance_warnings.append("建议使用异步编程提高性能")
            
        # 检查是否有资源管理
        if "with " not in plan.code_template and "open(" in plan.code_template:
            performance_warnings.append("建议使用上下文管理器管理资源")
            
        # 检查是否有大量循环
        loop_count = plan.code_template.count("for ") + plan.code_template.count("while ")
        if loop_count > 3:
            performance_warnings.append("代码包含较多循环，注意性能优化")
            
        # 检查内存使用
        if "pandas" in plan.code_template and "memory_usage" not in plan.code_template:
            performance_warnings.append("使用pandas时建议监控内存使用")
            
        result.warnings.extend(performance_warnings)
        
        # 性能检查通常不会导致失败，只是警告
        self.logger.info("性能检查完成")
        return True
        
    async def _run_test_cases(self, plan: CodeGenerationPlan, 
                            result: ValidationResult) -> None:
        """运行测试用例"""
        if not plan.test_cases:
            result.warnings.append("没有提供测试用例")
            return
            
        self.logger.info(f"运行 {len(plan.test_cases)} 个测试用例")
        
        for test_case in plan.test_cases:
            test_result = await self._run_single_test(plan, test_case)
            result.test_results.append(test_result)
            
    async def _run_single_test(self, plan: CodeGenerationPlan, 
                             test_case: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试用例"""
        test_result = {
            "name": test_case["name"],
            "description": test_case["description"],
            "passed": False,
            "error": None,
            "execution_time": 0.0
        }
        
        try:
            # 创建临时文件来测试代码
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                # 写入代码
                f.write(plan.code_template)
                f.write("\n\n# 测试代码\n")
                f.write(self._generate_test_code(plan, test_case))
                temp_file = f.name
                
            # 运行测试
            import time
            start_time = time.time()
            
            # 使用subprocess运行测试，避免影响当前进程
            result = subprocess.run(
                [sys.executable, temp_file],
                capture_output=True,
                text=True,
                timeout=30  # 30秒超时
            )
            
            execution_time = time.time() - start_time
            test_result["execution_time"] = execution_time
            
            if result.returncode == 0:
                test_result["passed"] = True
                self.logger.info(f"测试 {test_case['name']} 通过")
            else:
                test_result["error"] = result.stderr
                self.logger.warning(f"测试 {test_case['name']} 失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            test_result["error"] = "测试超时"
            self.logger.warning(f"测试 {test_case['name']} 超时")
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"测试 {test_case['name']} 异常: {e}")
        finally:
            # 清理临时文件
            try:
                Path(temp_file).unlink()
            except:
                pass
                
        return test_result
        
    def _generate_test_code(self, plan: CodeGenerationPlan, 
                          test_case: Dict[str, Any]) -> str:
        """生成测试代码"""
        class_name = plan.capability_name.title().replace('_', '') + 'Capability'
        method_name = plan.capability_name.lower()
        
        test_code = f"""
import asyncio

async def run_test():
    try:
        # 创建实例
        capability = {class_name}()
        
        # 运行测试
        result = await capability.{method_name}(**{test_case['input']})
        
        # 检查结果
        assert result is not None, "结果不能为空"
        
        # 运行断言
        """
        
        for assertion in test_case.get('assertions', []):
            test_code += f"        assert {assertion}, '{assertion} 失败'\n"
            
        test_code += """
        print("测试通过")
        
    except Exception as e:
        print(f"测试失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(run_test())
"""
        
        return test_code
        
    def _calculate_score(self, result: ValidationResult) -> float:
        """计算代码质量评分"""
        score = 0.0
        
        # 语法检查 (30分)
        if result.syntax_check:
            score += 30.0
            
        # 安全检查 (25分)
        if result.security_check:
            score += 25.0
            
        # 功能检查 (25分)
        if result.functionality_check:
            score += 25.0
            
        # 性能检查 (10分)
        if result.performance_check:
            score += 10.0
            
        # 测试通过率 (10分)
        if result.test_results:
            passed_tests = sum(1 for test in result.test_results if test["passed"])
            test_score = (passed_tests / len(result.test_results)) * 10.0
            score += test_score
            
        # 扣分项
        # 每个错误扣5分
        score -= len(result.errors) * 5.0
        
        # 每个警告扣1分
        score -= len(result.warnings) * 1.0
        
        # 确保分数在0-100之间
        return max(0.0, min(100.0, score))
        
    def _generate_recommendations(self, result: ValidationResult, 
                                plan: CodeGenerationPlan) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if not result.syntax_check:
            recommendations.append("修复语法错误")
            
        if not result.security_check:
            recommendations.append("解决安全问题")
            
        if not result.functionality_check:
            recommendations.append("完善功能实现")
            
        if result.warnings:
            recommendations.append("处理警告信息以提高代码质量")
            
        if result.test_results:
            failed_tests = [test for test in result.test_results if not test["passed"]]
            if failed_tests:
                recommendations.append(f"修复 {len(failed_tests)} 个失败的测试用例")
                
        if result.score < 80.0:
            recommendations.append("提高代码质量，目标评分80分以上")
            
        # 根据复杂度给出建议
        if plan.complexity_level == "complex":
            recommendations.append("考虑将复杂功能拆分为多个简单函数")
            
        if len(plan.required_libraries) > 5:
            recommendations.append("考虑减少依赖库的数量")
            
        return recommendations
