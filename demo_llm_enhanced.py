#!/usr/bin/env python3
"""
LLM 增强的自进化智能体演示
展示使用 DeepSeek API 增强的智能体能力
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.core.agent import SelfEvolvingAgent
from src.llm.deepseek_client import DeepSeekClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class LLMEnhancedDemo:
    """LLM 增强演示类"""
    
    def __init__(self):
        self.agent = None
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化智能体"""
        print("🚀 初始化 LLM 增强的自进化智能体...")

        self.agent = SelfEvolvingAgent()

        # 设置用户交互回调
        self.agent.set_user_interaction_callback(self.handle_capability_request)

        print("✅ 智能体初始化完成")
        
    async def handle_capability_request(self, interaction_type: str, data: dict):
        """处理能力请求"""
        if interaction_type == "capability_request":
            capability = data.get("capability")
            print(f"\n🤖 智能体请求: {data.get('message')}")
            
            # 模拟提供API信息
            api_info = await self.get_mock_api_info(capability)
            if api_info:
                print(f"📡 提供 {capability} API信息")
                return api_info
            else:
                print(f"❌ 没有 {capability} 的API信息")
                return None
        
        return None
        
    async def get_mock_api_info(self, capability: str) -> dict:
        """获取模拟API信息"""
        api_templates = {
            "web_search": {
                "type": "rest",
                "base_url": "https://api.duckduckgo.com",
                "endpoints": {
                    "search": {
                        "method": "GET",
                        "path": "/",
                        "parameters": {
                            "q": "搜索关键词",
                            "format": "json"
                        }
                    }
                },
                "description": "DuckDuckGo 搜索API"
            },
            "weather_api": {
                "type": "rest", 
                "base_url": "https://api.openweathermap.org/data/2.5",
                "endpoints": {
                    "current_weather": {
                        "method": "GET",
                        "path": "/weather",
                        "parameters": {
                            "q": "城市名称",
                            "appid": "API密钥",
                            "units": "metric",
                            "lang": "zh_cn"
                        }
                    }
                },
                "authentication": {
                    "type": "api_key",
                    "key_name": "appid"
                },
                "description": "OpenWeatherMap 天气API"
            },
            "news_api": {
                "type": "rest",
                "base_url": "https://newsapi.org/v2", 
                "endpoints": {
                    "search_news": {
                        "method": "GET",
                        "path": "/everything",
                        "parameters": {
                            "q": "搜索关键词",
                            "apiKey": "API密钥",
                            "language": "zh",
                            "sortBy": "publishedAt"
                        }
                    }
                },
                "authentication": {
                    "type": "api_key",
                    "key_name": "apiKey"
                },
                "description": "新闻搜索API"
            },
            "translation": {
                "type": "rest",
                "base_url": "https://api.mymemory.translated.net",
                "endpoints": {
                    "translate": {
                        "method": "GET",
                        "path": "/get",
                        "parameters": {
                            "q": "要翻译的文本",
                            "langpair": "en|zh"
                        }
                    }
                },
                "description": "MyMemory 翻译API"
            }
        }
        
        return api_templates.get(capability)
        
    async def run_enhanced_demo(self):
        """运行增强演示"""
        print("\n" + "=" * 60)
        print("🧠 LLM 增强的自进化智能体演示")
        print("=" * 60)
        
        # 演示任务列表
        demo_tasks = [
            {
                "name": "智能新闻搜索",
                "description": "搜索最新的人工智能技术新闻",
                "expected": "使用LLM精确识别需要web_search能力"
            },
            {
                "name": "天气查询服务", 
                "description": "查询北京今天的天气情况",
                "expected": "使用LLM识别需要weather_api能力"
            },
            {
                "name": "多语言翻译",
                "description": "将这段英文翻译成中文：Hello, how are you?",
                "expected": "使用LLM识别需要translation能力"
            },
            {
                "name": "复合任务处理",
                "description": "搜索AI新闻并翻译成中文摘要",
                "expected": "使用LLM识别需要多个能力的组合"
            }
        ]
        
        results = []
        
        for i, task in enumerate(demo_tasks, 1):
            print(f"\n📋 任务 {i}: {task['name']}")
            print("=" * 50)
            print(f"描述: {task['description']}")
            print(f"预期: {task['expected']}")
            print()
            
            try:
                # 执行任务
                result = await self.agent.execute_task(task['description'])
                
                if result.get('status') == 'success':
                    print(f"✅ 任务 {i} 成功完成!")
                    results.append(True)
                else:
                    print(f"❌ 任务 {i} 执行失败: {result.get('error', '未知错误')}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ 任务 {i} 异常: {e}")
                results.append(False)
            
            # 显示当前可用能力
            available_capabilities = self.agent.capability_registry.list_capabilities()
            print(f"当前可用能力: {available_capabilities}")
            print()
            
        # 显示结果统计
        self.show_demo_results(results, demo_tasks)
        
    def show_demo_results(self, results: list, tasks: list):
        """显示演示结果"""
        print("=" * 60)
        print("📊 LLM 增强演示结果")
        print("=" * 60)
        
        success_count = sum(results)
        total_count = len(results)
        
        for i, (task, success) in enumerate(zip(tasks, results), 1):
            status = "✅ 成功" if success else "❌ 失败"
            print(f"任务 {i} - {task['name']}: {status}")
            
        print("-" * 60)
        print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("\n🎉 所有任务完成！LLM 增强功能运行完美！")
        else:
            print(f"\n⚠️ 有 {total_count - success_count} 个任务需要优化")
            
        print("\n💡 LLM 增强的优势:")
        print("- 🧠 更精确的能力识别")
        print("- ⚡ 更智能的代码生成")
        print("- 🎯 更准确的任务理解")
        print("- 🔄 更好的自适应能力")
        
    async def interactive_mode(self):
        """交互模式"""
        print("\n🎮 进入 LLM 增强交互模式")
        print("输入任务描述，智能体将使用 LLM 进行智能分析和处理")
        print("输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                task = input("\n请输入任务: ").strip()
                
                if task.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                    
                if not task:
                    continue
                    
                print(f"\n🤖 处理任务: {task}")
                
                # 执行任务
                result = await self.agent.execute_task(task)
                
                if result.get('status') == 'success':
                    print("✅ 任务完成!")
                else:
                    print(f"❌ 任务失败: {result.get('error', '未知错误')}")
                    
                # 显示当前能力
                capabilities = self.agent.capability_registry.list_capabilities()
                print(f"📦 当前可用能力: {capabilities}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理异常: {e}")


async def main():
    """主函数"""
    demo = LLMEnhancedDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        # 检查命令行参数
        if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
            await demo.interactive_mode()
        else:
            await demo.run_enhanced_demo()
            
    except Exception as e:
        print(f"❌ 演示异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 LLM 增强的自进化智能体")
    print("使用 DeepSeek API 提供智能分析和代码生成能力")
    print("=" * 60)
    
    asyncio.run(main())
