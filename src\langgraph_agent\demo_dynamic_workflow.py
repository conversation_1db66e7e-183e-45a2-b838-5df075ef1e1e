"""
动态工作流和智能Prompt生成演示
展示Agent自动生成工作流和Prompt的能力
"""
import asyncio
import logging
import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langgraph_agent.agent_graph import SelfEvolvingAgentGraph
from langgraph_agent.dynamic_workflow_generator import WorkflowPattern, WorkflowComplexity
from langgraph_agent.intelligent_prompt_generator import PromptType, PromptStyle
from langgraph_agent.workflow_optimizer import OptimizationStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DynamicWorkflowDemo:
    """动态工作流演示类"""
    
    def __init__(self, deepseek_api_key: str):
        self.agent = SelfEvolvingAgentGraph(deepseek_api_key)
        self.demo_tasks = self._init_demo_tasks()
    
    def _init_demo_tasks(self) -> Dict[str, Dict[str, Any]]:
        """初始化演示任务"""
        return {
            "simple_calculation": {
                "description": "计算两个数字的平方和",
                "expected_pattern": WorkflowPattern.LINEAR,
                "expected_complexity": WorkflowComplexity.SIMPLE,
                "context": {"numbers": [3, 4]}
            },
            "data_analysis": {
                "description": "分析CSV文件中的销售数据，生成报告和可视化图表",
                "expected_pattern": WorkflowPattern.PARALLEL,
                "expected_complexity": WorkflowComplexity.COMPLEX,
                "context": {"file_path": "sales_data.csv", "output_format": "html"}
            },
            "api_integration": {
                "description": "集成天气API获取多个城市的天气信息并发送邮件通知",
                "expected_pattern": WorkflowPattern.CONDITIONAL,
                "expected_complexity": WorkflowComplexity.MEDIUM,
                "context": {"cities": ["北京", "上海", "广州"], "email": "<EMAIL>"}
            },
            "machine_learning": {
                "description": "训练一个文本分类模型，包括数据预处理、模型训练、评估和部署",
                "expected_pattern": WorkflowPattern.ITERATIVE,
                "expected_complexity": WorkflowComplexity.EXPERT,
                "context": {"dataset": "text_classification.csv", "model_type": "transformer"}
            },
            "web_scraping": {
                "description": "爬取电商网站的产品信息，清洗数据，存储到数据库，并生成价格趋势分析",
                "expected_pattern": WorkflowPattern.HYBRID,
                "expected_complexity": WorkflowComplexity.COMPLEX,
                "context": {"target_sites": ["site1.com", "site2.com"], "products": ["laptop", "phone"]}
            }
        }
    
    async def run_full_demo(self):
        """运行完整演示"""
        print("🚀 开始动态工作流和智能Prompt生成演示")
        print("=" * 60)
        
        # 1. 演示动态工作流生成
        await self._demo_dynamic_workflow_generation()
        
        # 2. 演示智能Prompt生成
        await self._demo_intelligent_prompt_generation()
        
        # 3. 演示工作流优化
        await self._demo_workflow_optimization()
        
        # 4. 演示完整任务执行
        await self._demo_complete_task_execution()
        
        # 5. 显示分析报告
        await self._show_analytics_report()
        
        print("\n🎉 演示完成！")
    
    async def _demo_dynamic_workflow_generation(self):
        """演示动态工作流生成"""
        print("\n📋 1. 动态工作流生成演示")
        print("-" * 40)
        
        for task_name, task_info in self.demo_tasks.items():
            print(f"\n🎯 任务: {task_name}")
            print(f"描述: {task_info['description']}")
            
            try:
                # 生成动态工作流
                workflow_result = await self.agent._generate_dynamic_workflow(
                    task_info['description'], 
                    self.agent.workflow_nodes.create_initial_state(task_info['description'], task_name)
                )
                
                if workflow_result["success"]:
                    generated_workflow = workflow_result["generated_workflow"]
                    print(f"✅ 工作流生成成功:")
                    print(f"   - 名称: {generated_workflow.name}")
                    print(f"   - 模式: {generated_workflow.pattern.value}")
                    print(f"   - 复杂度: {generated_workflow.complexity.value}")
                    print(f"   - 节点数量: {len(generated_workflow.nodes)}")
                    print(f"   - 边数量: {len(generated_workflow.edges)}")
                    print(f"   - 预估时间: {generated_workflow.estimated_duration}秒")
                    
                    # 显示节点信息
                    print("   - 节点列表:")
                    for node in generated_workflow.nodes[:3]:  # 只显示前3个节点
                        print(f"     * {node.name} ({node.node_type})")
                    if len(generated_workflow.nodes) > 3:
                        print(f"     * ... 还有 {len(generated_workflow.nodes) - 3} 个节点")
                else:
                    print(f"❌ 工作流生成失败: {workflow_result.get('error', '未知错误')}")
                
            except Exception as e:
                print(f"❌ 生成过程出错: {e}")
            
            print()
    
    async def _demo_intelligent_prompt_generation(self):
        """演示智能Prompt生成"""
        print("\n💬 2. 智能Prompt生成演示")
        print("-" * 40)
        
        prompt_scenarios = [
            {
                "type": PromptType.TASK_ANALYSIS,
                "style": PromptStyle.ANALYTICAL,
                "context": {
                    "task_description": "开发一个实时聊天应用",
                    "complexity": "high",
                    "domain": "web"
                }
            },
            {
                "type": PromptType.CODE_GENERATION,
                "style": PromptStyle.STRUCTURED,
                "context": {
                    "requirement": "实现PDF文档解析功能",
                    "language": "python",
                    "tech_stack": ["PyPDF2", "pandas"],
                    "performance_requirements": "处理大文件"
                }
            },
            {
                "type": PromptType.API_INTEGRATION,
                "style": PromptStyle.DIRECT,
                "context": {
                    "api_info": "OpenWeatherMap API",
                    "integration_goal": "获取天气数据",
                    "tech_environment": "Python Flask"
                }
            }
        ]
        
        for i, scenario in enumerate(prompt_scenarios, 1):
            print(f"\n🎯 场景 {i}: {scenario['type'].value}")
            print(f"风格: {scenario['style'].value}")
            
            try:
                prompt_result = await self.agent.generate_intelligent_prompt(
                    scenario['type'],
                    scenario['context'],
                    scenario['style']
                )
                
                if prompt_result["success"]:
                    generated_prompt = prompt_result["prompt"]
                    print(f"✅ Prompt生成成功:")
                    print(f"   - ID: {generated_prompt.prompt_id}")
                    print(f"   - 评分: {generated_prompt.optimization_score:.2f}")
                    print(f"   - 预估Token: {generated_prompt.estimated_tokens}")
                    print(f"   - 内容预览:")
                    content_preview = generated_prompt.content[:200] + "..." if len(generated_prompt.content) > 200 else generated_prompt.content
                    print(f"     {content_preview}")
                else:
                    print(f"❌ Prompt生成失败: {prompt_result.get('error', '未知错误')}")
                
            except Exception as e:
                print(f"❌ 生成过程出错: {e}")
    
    async def _demo_workflow_optimization(self):
        """演示工作流优化"""
        print("\n🔧 3. 工作流优化演示")
        print("-" * 40)
        
        # 首先生成一个工作流用于优化
        task_description = "处理大量图片文件的批量转换和压缩"
        print(f"🎯 优化任务: {task_description}")
        
        try:
            # 生成初始工作流
            initial_state = self.agent.workflow_nodes.create_initial_state(task_description, "optimization_demo")
            workflow_result = await self.agent._generate_dynamic_workflow(task_description, initial_state)
            
            if workflow_result["success"]:
                workflow_id = workflow_result["workflow_id"]
                print(f"✅ 初始工作流已生成: {workflow_id}")
                
                # 模拟一些执行历史
                mock_history = self._create_mock_execution_history(workflow_id)
                self.agent.execution_history.extend(mock_history)
                print(f"📊 添加了 {len(mock_history)} 条模拟执行记录")
                
                # 执行优化
                optimization_strategies = [
                    OptimizationStrategy.CONSERVATIVE,
                    OptimizationStrategy.BALANCED,
                    OptimizationStrategy.AGGRESSIVE
                ]
                
                for strategy in optimization_strategies:
                    print(f"\n🔍 使用 {strategy.value} 策略优化:")
                    
                    opt_result = await self.agent.optimize_workflow(workflow_id, strategy)
                    
                    if opt_result["success"]:
                        optimization = opt_result["optimization_result"]
                        print(f"   ✅ 优化完成，评分: {optimization.optimization_score:.2f}")
                        print(f"   📈 性能改进:")
                        for metric, improvement in optimization.performance_improvement.items():
                            print(f"     - {metric}: +{improvement:.1%}")
                        print(f"   🛡️ 风险评估: {optimization.risk_assessment['overall_risk']}")
                        print(f"   💡 应用规则: {len(optimization.applied_rules)} 个")
                    else:
                        print(f"   ❌ 优化失败: {opt_result.get('error', '未知错误')}")
            else:
                print(f"❌ 无法生成初始工作流: {workflow_result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 优化演示出错: {e}")
    
    async def _demo_complete_task_execution(self):
        """演示完整任务执行"""
        print("\n🚀 4. 完整任务执行演示")
        print("-" * 40)
        
        # 选择一个简单任务进行完整执行
        task_name = "simple_calculation"
        task_info = self.demo_tasks[task_name]
        
        print(f"🎯 执行任务: {task_info['description']}")
        
        try:
            # 使用动态工作流执行任务
            result = await self.agent.process_task(
                task_info['description'],
                task_name,
                use_dynamic_workflow=True
            )
            
            print(f"✅ 任务执行完成:")
            print(f"   - 成功: {result['success']}")
            print(f"   - 执行时间: {result['execution_time']:.2f}秒")
            print(f"   - 步骤数: {result['step_count']}")
            print(f"   - 使用动态工作流: {result['used_dynamic_workflow']}")
            print(f"   - 生成能力: {len(result['generated_capabilities'])} 个")
            
            if result['final_response']:
                response_preview = result['final_response'][:200] + "..." if len(result['final_response']) > 200 else result['final_response']
                print(f"   - 响应预览: {response_preview}")
            
            if result['error_messages']:
                print(f"   ⚠️ 错误信息: {len(result['error_messages'])} 个")
                
        except Exception as e:
            print(f"❌ 任务执行出错: {e}")
    
    async def _show_analytics_report(self):
        """显示分析报告"""
        print("\n📊 5. 分析报告")
        print("-" * 40)
        
        try:
            analytics = await self.agent.get_workflow_analytics()
            
            print(f"📈 执行统计:")
            print(f"   - 总执行次数: {analytics['total_executions']}")
            print(f"   - 动态工作流数量: {analytics['dynamic_workflows_count']}")
            print(f"   - 成功率: {analytics['success_rate']:.1%}")
            print(f"   - 平均执行时间: {analytics['average_execution_time']:.2f}秒")
            
            if analytics['most_common_errors']:
                print(f"   🚨 常见错误:")
                for error_type, count in analytics['most_common_errors'].items():
                    print(f"     - {error_type}: {count} 次")
            
            print(f"\n🎯 系统能力:")
            print(f"   ✅ 动态工作流生成")
            print(f"   ✅ 智能Prompt优化")
            print(f"   ✅ 自适应工作流优化")
            print(f"   ✅ 性能监控和分析")
            print(f"   ✅ 自主学习和改进")
            
        except Exception as e:
            print(f"❌ 获取分析报告失败: {e}")
    
    def _create_mock_execution_history(self, workflow_id: str) -> list:
        """创建模拟执行历史"""
        import random
        import time
        
        history = []
        base_time = time.time() - 86400  # 24小时前
        
        for i in range(10):
            success = random.choice([True, True, True, False])  # 75%成功率
            duration = random.uniform(30, 120)  # 30-120秒
            
            record = {
                "task_id": f"mock_task_{i}",
                "workflow_id": workflow_id,
                "user_task": "模拟图片处理任务",
                "execution_time": duration,
                "step_count": random.randint(5, 12),
                "success": success,
                "timestamp": base_time + i * 3600,  # 每小时一个
                "execution_steps": [],
                "final_state": {"current_status": "completed" if success else "failed"}
            }
            
            if not success:
                record["errors"] = [{"type": "TimeoutError", "message": "操作超时"}]
            
            history.append(record)
        
        return history


async def main():
    """主函数"""
    # 使用DeepSeek API密钥
    deepseek_api_key = "***********************************"
    
    # 创建演示实例
    demo = DynamicWorkflowDemo(deepseek_api_key)
    
    # 运行演示
    await demo.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())
