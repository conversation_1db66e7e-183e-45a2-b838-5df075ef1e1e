"""
自主代码生成系统
当Agent遇到缺失功能时，自动判断是否可以通过编程实现，并生成相应代码
"""
import asyncio
import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from enum import Enum

# 尝试导入LLM客户端
try:
    from ..llm.deepseek_client import DeepSeekClient
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False

# 导入依赖管理器
from .dependency_manager import DependencyManager


class ImplementationType(Enum):
    """实现类型枚举"""
    SELF_CODED = "self_coded"  # 可以自己编程实现
    API_REQUIRED = "api_required"  # 需要外部API
    HYBRID = "hybrid"  # 混合模式（部分自编程，部分API）
    IMPOSSIBLE = "impossible"  # 无法实现


@dataclass
class CodeGenerationPlan:
    """代码生成计划"""
    capability_name: str
    implementation_type: ImplementationType
    required_libraries: List[str]
    complexity_level: str  # "simple", "moderate", "complex"
    estimated_lines: int
    main_functions: List[str]
    dependencies: List[str]
    code_template: str
    test_cases: List[Dict[str, Any]]
    documentation: str
    reasoning: str  # 为什么选择这种实现方式


class CapabilityAnalyzer:
    """能力分析器 - 判断功能是否可以自主实现"""
    
    def __init__(self, llm_client=None):
        self.logger = logging.getLogger(__name__)
        self.llm_client = llm_client
        
        # 可以自主实现的功能类别
        self.self_implementable_categories = {
            "文档处理": ["pdf", "word", "excel", "text", "markdown"],
            "图像处理": ["resize", "crop", "filter", "format_conversion", "basic_analysis"],
            "数据处理": ["csv", "json", "xml", "data_cleaning", "statistics"],
            "文件操作": ["file_management", "compression", "encryption", "backup"],
            "文本分析": ["nlp", "sentiment", "keyword_extraction", "text_mining"],
            "数学计算": ["statistics", "algebra", "geometry", "financial"],
            "网络工具": ["http_client", "web_scraping", "url_parsing"],
            "系统工具": ["process_management", "system_info", "monitoring"],
            "加密安全": ["hashing", "encryption", "password_generation"],
            "时间处理": ["date_parsing", "timezone", "scheduling"]
        }
        
        # 通常需要API的功能类别
        self.api_required_categories = {
            "实时数据": ["weather", "stock_prices", "news", "social_media"],
            "AI服务": ["translation", "speech_recognition", "image_recognition"],
            "第三方服务": ["payment", "email_service", "sms", "cloud_storage"],
            "地理服务": ["maps", "geocoding", "routing"],
            "社交平台": ["twitter", "facebook", "linkedin"],
            "专业数据": ["financial_data", "scientific_data", "government_data"]
        }
        
    async def analyze_capability_requirement(self, capability_name: str, 
                                           task_description: str) -> ImplementationType:
        """
        分析能力需求，判断实现类型
        
        Args:
            capability_name: 能力名称
            task_description: 任务描述
            
        Returns:
            实现类型
        """
        self.logger.info(f"分析能力需求: {capability_name}")
        
        # 首先使用规则判断
        rule_based_type = self._rule_based_analysis(capability_name, task_description)
        
        # 如果有LLM，使用智能分析
        if self.llm_client:
            try:
                llm_based_type = await self._llm_based_analysis(capability_name, task_description)
                # 综合两种分析结果
                return self._combine_analysis_results(rule_based_type, llm_based_type)
            except Exception as e:
                self.logger.warning(f"LLM分析失败，使用规则分析结果: {e}")
                
        return rule_based_type
        
    def _rule_based_analysis(self, capability_name: str, task_description: str) -> ImplementationType:
        """基于规则的分析"""
        capability_lower = capability_name.lower()
        description_lower = task_description.lower()
        
        # 检查是否属于可自主实现的类别
        for category, keywords in self.self_implementable_categories.items():
            if any(keyword in capability_lower or keyword in description_lower 
                   for keyword in keywords):
                self.logger.info(f"规则判断: {capability_name} 属于可自主实现类别 '{category}'")
                return ImplementationType.SELF_CODED
                
        # 检查是否需要API
        for category, keywords in self.api_required_categories.items():
            if any(keyword in capability_lower or keyword in description_lower 
                   for keyword in keywords):
                self.logger.info(f"规则判断: {capability_name} 需要API支持 '{category}'")
                return ImplementationType.API_REQUIRED
                
        # 默认尝试自主实现
        return ImplementationType.SELF_CODED
        
    async def _llm_based_analysis(self, capability_name: str, 
                                task_description: str) -> ImplementationType:
        """基于LLM的智能分析"""
        prompt = f"""
        请分析以下功能需求，判断最佳实现方式：
        
        功能名称: {capability_name}
        任务描述: {task_description}
        
        请从以下选项中选择最合适的实现方式：
        1. SELF_CODED - 可以通过Python编程自主实现（如PDF处理、图像处理、数据分析等）
        2. API_REQUIRED - 需要外部API支持（如实时天气、翻译服务、社交媒体数据等）
        3. HYBRID - 混合模式（部分功能自编程，部分需要API）
        4. IMPOSSIBLE - 无法实现
        
        考虑因素：
        - 是否需要实时外部数据
        - 是否可以用Python标准库或常见第三方库实现
        - 功能复杂度和可行性
        - 数据来源和处理方式
        
        请只返回选项名称（如：SELF_CODED），并简要说明理由。
        """
        
        messages = [{"role": "user", "content": prompt}]
        response_data = await self.llm_client.chat_completion(messages)
        response = response_data['choices'][0]['message']['content']
        
        # 解析响应
        response_upper = response.upper()
        if "SELF_CODED" in response_upper:
            return ImplementationType.SELF_CODED
        elif "API_REQUIRED" in response_upper:
            return ImplementationType.API_REQUIRED
        elif "HYBRID" in response_upper:
            return ImplementationType.HYBRID
        elif "IMPOSSIBLE" in response_upper:
            return ImplementationType.IMPOSSIBLE
        else:
            # 默认尝试自主实现
            return ImplementationType.SELF_CODED
            
    def _combine_analysis_results(self, rule_result: ImplementationType, 
                                llm_result: ImplementationType) -> ImplementationType:
        """综合分析结果"""
        # 如果两个结果一致，直接返回
        if rule_result == llm_result:
            return rule_result
            
        # 如果有冲突，优先考虑LLM的判断（更智能）
        # 但如果规则明确指出需要API，则优先考虑规则
        if rule_result == ImplementationType.API_REQUIRED:
            return ImplementationType.API_REQUIRED
        else:
            return llm_result


class CodeGenerator:
    """代码生成器"""
    
    def __init__(self, deepseek_api_key: str = None):
        self.logger = logging.getLogger(__name__)

        # 初始化LLM客户端
        if LLM_AVAILABLE and deepseek_api_key:
            self.llm_client = DeepSeekClient(deepseek_api_key)
            self.llm_available = True
        else:
            self.llm_client = None
            self.llm_available = False

        # 初始化依赖管理器
        self.dependency_manager = DependencyManager()
            
        # 代码模板库
        self.code_templates = {
            "pdf_processing": self._get_pdf_template(),
            "image_processing": self._get_image_template(),
            "data_processing": self._get_data_template(),
            "file_operations": self._get_file_template(),
            "text_analysis": self._get_text_template()
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self.llm_client:
            await self.llm_client.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.llm_client:
            await self.llm_client.__aexit__(exc_type, exc_val, exc_tb)
            
    async def generate_code_plan(self, capability_name: str, 
                               task_description: str) -> CodeGenerationPlan:
        """
        生成代码实现计划
        
        Args:
            capability_name: 能力名称
            task_description: 任务描述
            
        Returns:
            代码生成计划
        """
        self.logger.info(f"为能力 '{capability_name}' 生成代码计划")
        
        # 分析需求
        requirements = await self._analyze_requirements(capability_name, task_description)
        
        # 确定依赖库
        libraries = self._determine_libraries(capability_name, requirements)
        
        # 评估复杂度
        complexity = self._estimate_complexity(requirements)
        
        # 生成代码
        code_template = await self._generate_code_implementation(
            capability_name, task_description, requirements, libraries
        )
        
        # 创建测试用例
        test_cases = self._generate_test_cases(capability_name, requirements)
        
        # 生成文档
        documentation = self._generate_documentation(capability_name, requirements)
        
        plan = CodeGenerationPlan(
            capability_name=capability_name,
            implementation_type=ImplementationType.SELF_CODED,
            required_libraries=libraries,
            complexity_level=complexity,
            estimated_lines=len(code_template.split('\n')),
            main_functions=self._extract_function_names(code_template),
            dependencies=libraries,
            code_template=code_template,
            test_cases=test_cases,
            documentation=documentation,
            reasoning=f"基于{capability_name}的功能特点，可以通过Python编程自主实现"
        )

        # 检查和安装依赖
        await self._handle_dependencies(plan)

        return plan

    async def _handle_dependencies(self, plan: CodeGenerationPlan) -> None:
        """处理代码依赖"""
        self.logger.info(f"检查依赖库: {plan.required_libraries}")

        # 检查依赖状态
        dependency_status = self.dependency_manager.check_dependencies(plan.required_libraries)

        if dependency_status['missing_packages']:
            self.logger.info(f"发现 {dependency_status['missing_count']} 个缺失依赖")

            # 自动安装依赖
            install_result = await self.dependency_manager.auto_install_dependencies(
                plan.required_libraries, auto_confirm=True
            )

            if install_result['success']:
                self.logger.info("✅ 所有依赖安装成功")
            else:
                self.logger.warning(f"⚠️  部分依赖安装失败: {install_result['failed_packages']}")

                # 为失败的包建议替代方案
                for failed_pkg in install_result['failed_packages']:
                    alternatives = self.dependency_manager.suggest_alternatives(failed_pkg['pip_name'])
                    if alternatives:
                        self.logger.info(f"建议替代方案 {failed_pkg['pip_name']}: {alternatives}")
        else:
            self.logger.info("✅ 所有依赖都已安装")

    async def _analyze_requirements(self, capability_name: str,
                                  task_description: str) -> Dict[str, Any]:
        """分析功能需求"""
        if self.llm_available:
            return await self._llm_analyze_requirements(capability_name, task_description)
        else:
            return self._basic_analyze_requirements(capability_name, task_description)
            
    async def _llm_analyze_requirements(self, capability_name: str, 
                                      task_description: str) -> Dict[str, Any]:
        """使用LLM分析需求"""
        prompt = f"""
        请详细分析以下功能需求，提取关键信息：
        
        功能名称: {capability_name}
        任务描述: {task_description}
        
        请分析并返回JSON格式的需求信息：
        {{
            "main_purpose": "主要目的",
            "input_types": ["输入类型1", "输入类型2"],
            "output_types": ["输出类型1", "输出类型2"],
            "key_operations": ["关键操作1", "关键操作2"],
            "performance_requirements": "性能要求",
            "error_handling": ["错误处理需求"],
            "special_features": ["特殊功能需求"]
        }}
        """
        
        messages = [{"role": "user", "content": prompt}]
        response_data = await self.llm_client.chat_completion(messages)
        response = response_data['choices'][0]['message']['content']
        
        try:
            # 尝试解析JSON
            if "```json" in response:
                json_content = response.split("```json")[1].split("```")[0].strip()
                return json.loads(json_content)
            elif "{" in response and "}" in response:
                # 提取JSON部分
                start = response.find("{")
                end = response.rfind("}") + 1
                json_content = response[start:end]
                return json.loads(json_content)
        except:
            pass
            
        # 如果解析失败，返回基础分析
        return self._basic_analyze_requirements(capability_name, task_description)
        
    def _basic_analyze_requirements(self, capability_name: str, 
                                  task_description: str) -> Dict[str, Any]:
        """基础需求分析"""
        return {
            "main_purpose": f"实现{capability_name}功能",
            "input_types": ["文件", "文本", "数据"],
            "output_types": ["处理结果", "分析报告"],
            "key_operations": ["数据处理", "结果输出"],
            "performance_requirements": "高效稳定",
            "error_handling": ["输入验证", "异常处理"],
            "special_features": []
        }
        
    def _determine_libraries(self, capability_name: str, 
                           requirements: Dict[str, Any]) -> List[str]:
        """确定所需库"""
        base_libraries = ["logging", "pathlib", "typing"]
        
        capability_lower = capability_name.lower()
        
        # 根据功能类型添加特定库
        if "pdf" in capability_lower:
            base_libraries.extend(["PyPDF2", "pdfplumber", "reportlab"])
        elif "image" in capability_lower:
            base_libraries.extend(["Pillow", "opencv-python"])
        elif "data" in capability_lower or "csv" in capability_lower:
            base_libraries.extend(["pandas", "numpy"])
        elif "web" in capability_lower or "http" in capability_lower:
            base_libraries.extend(["requests", "aiohttp"])
        elif "text" in capability_lower or "nlp" in capability_lower:
            base_libraries.extend(["nltk", "textblob"])
        elif "excel" in capability_lower:
            base_libraries.extend(["openpyxl", "xlsxwriter"])
        elif "json" in capability_lower:
            base_libraries.append("json")
        elif "xml" in capability_lower:
            base_libraries.extend(["xml.etree.ElementTree", "lxml"])
            
        return list(set(base_libraries))  # 去重
        
    def _estimate_complexity(self, requirements: Dict[str, Any]) -> str:
        """评估复杂度"""
        complexity_score = 0
        
        # 基于操作数量
        operations = requirements.get("key_operations", [])
        complexity_score += len(operations)
        
        # 基于输入输出类型
        input_types = requirements.get("input_types", [])
        output_types = requirements.get("output_types", [])
        complexity_score += len(input_types) + len(output_types)
        
        # 基于特殊功能
        special_features = requirements.get("special_features", [])
        complexity_score += len(special_features) * 2
        
        if complexity_score <= 5:
            return "simple"
        elif complexity_score <= 10:
            return "moderate"
        else:
            return "complex"
            
    def _get_pdf_template(self) -> str:
        """PDF处理模板"""
        return '''
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import PyPDF2
import pdfplumber

class PdfProcessingCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def process_pdf(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        PDF文档处理功能
        """
        self.logger.info("开始PDF处理")
        
        file_path = kwargs.get('file_path')
        operation = kwargs.get('operation', 'extract_text')
        
        if not file_path:
            raise ValueError("需要提供file_path参数")
            
        pdf_path = Path(file_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF文件不存在: {file_path}")
            
        try:
            if operation == 'extract_text':
                return await self._extract_text(pdf_path)
            elif operation == 'get_info':
                return await self._get_pdf_info(pdf_path)
            elif operation == 'extract_pages':
                return await self._extract_pages(pdf_path, kwargs)
            else:
                raise ValueError(f"不支持的操作: {operation}")
                
        except Exception as e:
            self.logger.error(f"PDF处理失败: {e}")
            raise
            
    async def _extract_text(self, pdf_path: Path) -> Dict[str, Any]:
        """提取PDF文本"""
        text_content = []
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                text_content.append({
                    'page': page_num + 1,
                    'text': text.strip()
                })
                
        return {
            'status': 'success',
            'total_pages': len(text_content),
            'content': text_content,
            'full_text': '\\n'.join([page['text'] for page in text_content])
        }
        
    async def _get_pdf_info(self, pdf_path: Path) -> Dict[str, Any]:
        """获取PDF信息"""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            info = {
                'status': 'success',
                'file_name': pdf_path.name,
                'file_size': pdf_path.stat().st_size,
                'total_pages': len(pdf_reader.pages),
                'metadata': {}
            }
            
            if pdf_reader.metadata:
                info['metadata'] = {
                    'title': pdf_reader.metadata.get('/Title', ''),
                    'author': pdf_reader.metadata.get('/Author', ''),
                    'subject': pdf_reader.metadata.get('/Subject', ''),
                    'creator': pdf_reader.metadata.get('/Creator', ''),
                    'producer': pdf_reader.metadata.get('/Producer', ''),
                    'creation_date': str(pdf_reader.metadata.get('/CreationDate', '')),
                    'modification_date': str(pdf_reader.metadata.get('/ModDate', ''))
                }
                
        return info
        
    async def _extract_pages(self, pdf_path: Path, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """提取指定页面"""
        start_page = kwargs.get('start_page', 1)
        end_page = kwargs.get('end_page', None)
        
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            
            if end_page is None:
                end_page = total_pages
                
            extracted_pages = []
            
            for page_num in range(start_page - 1, min(end_page, total_pages)):
                page = pdf.pages[page_num]
                
                page_data = {
                    'page_number': page_num + 1,
                    'text': page.extract_text() or '',
                    'tables': [],
                    'images': len(page.images) if hasattr(page, 'images') else 0
                }
                
                # 提取表格
                tables = page.extract_tables()
                if tables:
                    page_data['tables'] = tables
                    
                extracted_pages.append(page_data)
                
        return {
            'status': 'success',
            'total_pages': total_pages,
            'extracted_pages': len(extracted_pages),
            'pages': extracted_pages
        }
'''
        
    def _get_image_template(self) -> str:
        """图像处理模板"""
        return '''
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from PIL import Image, ImageFilter, ImageEnhance
import io

class ImageProcessingCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def process_image(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        图像处理功能
        """
        self.logger.info("开始图像处理")
        
        image_path = kwargs.get('image_path')
        operation = kwargs.get('operation', 'info')
        
        if not image_path:
            raise ValueError("需要提供image_path参数")
            
        img_path = Path(image_path)
        if not img_path.exists():
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
        try:
            with Image.open(img_path) as img:
                if operation == 'info':
                    return await self._get_image_info(img, img_path)
                elif operation == 'resize':
                    return await self._resize_image(img, kwargs)
                elif operation == 'filter':
                    return await self._apply_filter(img, kwargs)
                elif operation == 'enhance':
                    return await self._enhance_image(img, kwargs)
                else:
                    raise ValueError(f"不支持的操作: {operation}")
                    
        except Exception as e:
            self.logger.error(f"图像处理失败: {e}")
            raise
            
    async def _get_image_info(self, img: Image.Image, img_path: Path) -> Dict[str, Any]:
        """获取图像信息"""
        return {
            'status': 'success',
            'file_name': img_path.name,
            'file_size': img_path.stat().st_size,
            'format': img.format,
            'mode': img.mode,
            'size': img.size,
            'width': img.width,
            'height': img.height,
            'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
        }
        
    async def _resize_image(self, img: Image.Image, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """调整图像大小"""
        width = kwargs.get('width')
        height = kwargs.get('height')
        output_path = kwargs.get('output_path')
        
        if not width or not height:
            raise ValueError("需要提供width和height参数")
            
        resized_img = img.resize((width, height), Image.Resampling.LANCZOS)
        
        if output_path:
            resized_img.save(output_path)
            
        return {
            'status': 'success',
            'original_size': img.size,
            'new_size': (width, height),
            'output_path': output_path
        }
'''
        
    def _get_data_template(self) -> str:
        """数据处理模板"""
        return '''
import logging
import pandas as pd
import json
from pathlib import Path
from typing import Dict, Any, Optional

class DataProcessingCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def process_data(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        数据处理功能
        """
        self.logger.info("开始数据处理")
        
        data_path = kwargs.get('data_path')
        operation = kwargs.get('operation', 'analyze')
        
        if not data_path:
            raise ValueError("需要提供data_path参数")
            
        try:
            data = await self._load_data(data_path)
            
            if operation == 'analyze':
                return await self._analyze_data(data)
            elif operation == 'clean':
                return await self._clean_data(data, kwargs)
            elif operation == 'transform':
                return await self._transform_data(data, kwargs)
            else:
                raise ValueError(f"不支持的操作: {operation}")
                
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            raise
            
    async def _load_data(self, data_path: str) -> pd.DataFrame:
        """加载数据"""
        path = Path(data_path)
        
        if path.suffix.lower() == '.csv':
            return pd.read_csv(path)
        elif path.suffix.lower() in ['.xlsx', '.xls']:
            return pd.read_excel(path)
        elif path.suffix.lower() == '.json':
            return pd.read_json(path)
        else:
            raise ValueError(f"不支持的文件格式: {path.suffix}")
            
    async def _analyze_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析数据"""
        return {
            'status': 'success',
            'shape': data.shape,
            'columns': data.columns.tolist(),
            'dtypes': data.dtypes.to_dict(),
            'missing_values': data.isnull().sum().to_dict(),
            'statistics': data.describe().to_dict(),
            'memory_usage': data.memory_usage(deep=True).sum()
        }
'''
        
    def _get_file_template(self) -> str:
        """文件操作模板"""
        return "# 文件操作模板代码"
        
    def _get_text_template(self) -> str:
        """文本分析模板"""
        return "# 文本分析模板代码"

    async def _generate_code_implementation(self, capability_name: str,
                                          task_description: str,
                                          requirements: Dict[str, Any],
                                          libraries: List[str]) -> str:
        """生成代码实现"""
        if self.llm_available:
            return await self._llm_generate_code(capability_name, task_description, requirements, libraries)
        else:
            return self._template_generate_code(capability_name, requirements)

    async def _llm_generate_code(self, capability_name: str,
                               task_description: str,
                               requirements: Dict[str, Any],
                               libraries: List[str]) -> str:
        """使用LLM生成代码"""
        prompt = f"""
        请为以下功能需求生成完整的Python代码实现：

        功能名称: {capability_name}
        任务描述: {task_description}

        需求分析:
        {json.dumps(requirements, indent=2, ensure_ascii=False)}

        可用库: {', '.join(libraries)}

        请生成一个完整的Python类，要求：
        1. 类名格式: {capability_name.title().replace('_', '')}Capability
        2. 主要方法名: {capability_name.lower()}
        3. 使用async/await异步编程
        4. 方法签名: async def {capability_name.lower()}(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]
        5. 包含完整的错误处理和日志记录
        6. 返回标准化的结果格式 {{'status': 'success/error', 'data': ..., 'message': ...}}
        7. 添加详细的文档字符串
        8. 使用类型提示

        特别注意：
        - 代码必须是生产就绪的质量
        - 包含输入验证和错误处理
        - 使用适当的设计模式
        - 代码要清晰、可维护

        只返回Python代码，不要包含其他说明。
        """

        messages = [{"role": "user", "content": prompt}]
        response_data = await self.llm_client.chat_completion(messages)
        response = response_data['choices'][0]['message']['content']

        # 提取代码块
        if "```python" in response:
            code = response.split("```python")[1].split("```")[0].strip()
            return code
        elif "```" in response:
            code = response.split("```")[1].split("```")[0].strip()
            return code
        else:
            return response.strip()

    def _template_generate_code(self, capability_name: str,
                              requirements: Dict[str, Any]) -> str:
        """基于模板生成代码"""
        capability_lower = capability_name.lower()

        # 根据功能类型选择模板
        if "pdf" in capability_lower:
            return self.code_templates["pdf_processing"]
        elif "image" in capability_lower:
            return self.code_templates["image_processing"]
        elif "data" in capability_lower:
            return self.code_templates["data_processing"]
        else:
            # 生成通用模板
            return self._generate_generic_template(capability_name, requirements)

    def _generate_generic_template(self, capability_name: str,
                                 requirements: Dict[str, Any]) -> str:
        """生成通用代码模板"""
        class_name = capability_name.title().replace('_', '') + 'Capability'
        method_name = capability_name.lower()

        template = f'''
import logging
from typing import Dict, Any, Optional

class {class_name}:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    async def {method_name}(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """
        {requirements.get('main_purpose', f'实现{capability_name}功能')}

        Args:
            context: 上下文信息
            **kwargs: 功能参数

        Returns:
            处理结果字典
        """
        self.logger.info("开始执行{capability_name}")

        try:
            # 输入验证
            self._validate_inputs(kwargs)

            # 执行主要逻辑
            result = await self._execute_main_logic(kwargs)

            return {{
                'status': 'success',
                'data': result,
                'message': f'{capability_name}执行成功'
            }}

        except Exception as e:
            self.logger.error(f"{capability_name}执行失败: {{e}}")
            return {{
                'status': 'error',
                'error': str(e),
                'message': f'{capability_name}执行失败'
            }}

    def _validate_inputs(self, kwargs: Dict[str, Any]) -> None:
        """验证输入参数"""
        # 添加具体的输入验证逻辑
        pass

    async def _execute_main_logic(self, kwargs: Dict[str, Any]) -> Any:
        """执行主要业务逻辑"""
        # 实现具体的功能逻辑
        return {{"result": "功能实现完成"}}
'''
        return template.strip()

    def _generate_test_cases(self, capability_name: str,
                           requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成测试用例"""
        test_cases = []

        # 基础功能测试
        basic_test = {
            "name": f"test_{capability_name}_basic",
            "description": f"测试{capability_name}基础功能",
            "input": {"test_param": "test_value"},
            "expected_status": "success",
            "assertions": [
                "result['status'] == 'success'",
                "'data' in result",
                "result['data'] is not None"
            ]
        }
        test_cases.append(basic_test)

        # 错误处理测试
        error_test = {
            "name": f"test_{capability_name}_error_handling",
            "description": f"测试{capability_name}错误处理",
            "input": {},  # 空输入可能触发错误
            "expected_status": "error",
            "assertions": [
                "result['status'] == 'error'",
                "'error' in result",
                "'message' in result"
            ]
        }
        test_cases.append(error_test)

        # 根据需求添加特定测试
        input_types = requirements.get("input_types", [])
        for input_type in input_types:
            specific_test = {
                "name": f"test_{capability_name}_{input_type.lower()}",
                "description": f"测试{capability_name}处理{input_type}",
                "input": {f"{input_type.lower()}_data": f"test_{input_type}"},
                "expected_status": "success",
                "assertions": [
                    "result['status'] == 'success'",
                    f"'{input_type.lower()}_processed' in result.get('data', {{}})"
                ]
            }
            test_cases.append(specific_test)

        return test_cases

    def _generate_documentation(self, capability_name: str,
                              requirements: Dict[str, Any]) -> str:
        """生成文档"""
        doc = f"""
# {capability_name.title().replace('_', ' ')} 功能文档

## 功能描述
{requirements.get('main_purpose', f'实现{capability_name}相关功能')}

## 主要特性
"""

        key_operations = requirements.get("key_operations", [])
        for operation in key_operations:
            doc += f"- {operation}\n"

        doc += f"""
## 输入类型
"""
        input_types = requirements.get("input_types", [])
        for input_type in input_types:
            doc += f"- {input_type}\n"

        doc += f"""
## 输出类型
"""
        output_types = requirements.get("output_types", [])
        for output_type in output_types:
            doc += f"- {output_type}\n"

        doc += f"""
## 使用示例

```python
# 创建能力实例
capability = {capability_name.title().replace('_', '')}Capability()

# 执行功能
result = await capability.{capability_name.lower()}(
    param1="value1",
    param2="value2"
)

# 检查结果
if result['status'] == 'success':
    data = result['data']
    print("处理成功:", data)
else:
    print("处理失败:", result['error'])
```

## 错误处理
- 输入验证失败时返回错误状态
- 处理过程中的异常会被捕获并记录
- 所有错误都包含详细的错误信息

## 性能要求
{requirements.get('performance_requirements', '高效稳定的处理性能')}
"""

        return doc.strip()

    def _extract_function_names(self, code: str) -> List[str]:
        """从代码中提取函数名"""
        import re

        # 匹配函数定义
        function_pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        async_function_pattern = r'async\s+def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('

        functions = []
        functions.extend(re.findall(function_pattern, code))
        functions.extend(re.findall(async_function_pattern, code))

        # 去重并过滤私有方法
        public_functions = [f for f in set(functions) if not f.startswith('_')]

        return public_functions
