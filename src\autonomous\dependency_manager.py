"""
自动依赖管理系统
当Agent生成代码时，自动检测、安装和管理所需的依赖库
"""
import subprocess
import sys
import logging
import importlib
import pkg_resources
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
import asyncio


class DependencyManager:
    """依赖管理器 - 自动处理代码生成过程中的依赖问题"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.installed_packages = set()
        self.failed_packages = set()
        
        # 常用库的映射关系（import名称 -> pip包名称）
        self.package_mapping = {
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'sklearn': 'scikit-learn',
            'yaml': 'PyYAML',
            'requests': 'requests',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'seaborn': 'seaborn',
            'plotly': 'plotly',
            'PyPDF2': 'PyPDF2',
            'pdfplumber': 'pdfplumber',
            'reportlab': 'reportlab',
            'openpyxl': 'openpyxl',
            'xlsxwriter': 'xlsxwriter',
            'beautifulsoup4': 'beautifulsoup4',
            'lxml': 'lxml',
            'selenium': 'selenium',
            'flask': 'Flask',
            'fastapi': 'fastapi',
            'uvicorn': 'uvicorn',
            'sqlalchemy': 'SQLAlchemy',
            'psycopg2': 'psycopg2-binary',
            'pymongo': 'pymongo',
            'redis': 'redis',
            'celery': 'celery',
            'schedule': 'schedule',
            'click': 'click',
            'tqdm': 'tqdm',
            'rich': 'rich',
            'typer': 'typer'
        }
        
        # 获取当前已安装的包
        self._load_installed_packages()
        
    def _load_installed_packages(self):
        """加载当前已安装的包列表"""
        try:
            installed_packages = [pkg.project_name.lower() for pkg in pkg_resources.working_set]
            self.installed_packages = set(installed_packages)
            self.logger.info(f"已加载 {len(self.installed_packages)} 个已安装包")
        except Exception as e:
            self.logger.warning(f"无法加载已安装包列表: {e}")
            self.installed_packages = set()
    
    def check_dependencies(self, required_libraries: List[str]) -> Dict[str, Any]:
        """
        检查所需依赖库的安装状态
        
        Args:
            required_libraries: 所需库列表
            
        Returns:
            依赖检查结果
        """
        self.logger.info(f"检查依赖库: {required_libraries}")
        
        missing_packages = []
        available_packages = []
        unknown_packages = []
        
        for lib in required_libraries:
            # 跳过标准库
            if self._is_standard_library(lib):
                available_packages.append(lib)
                continue
                
            # 获取pip包名
            pip_name = self._get_pip_package_name(lib)
            
            if self._is_package_installed(pip_name):
                available_packages.append(lib)
            elif pip_name:
                missing_packages.append({
                    'import_name': lib,
                    'pip_name': pip_name
                })
            else:
                unknown_packages.append(lib)
                
        return {
            'missing_packages': missing_packages,
            'available_packages': available_packages,
            'unknown_packages': unknown_packages,
            'total_required': len(required_libraries),
            'missing_count': len(missing_packages),
            'unknown_count': len(unknown_packages)
        }
    
    async def auto_install_dependencies(self, required_libraries: List[str], 
                                      auto_confirm: bool = False) -> Dict[str, Any]:
        """
        自动安装缺失的依赖库
        
        Args:
            required_libraries: 所需库列表
            auto_confirm: 是否自动确认安装
            
        Returns:
            安装结果
        """
        self.logger.info("开始自动安装依赖库")
        
        # 检查依赖状态
        dependency_status = self.check_dependencies(required_libraries)
        
        if not dependency_status['missing_packages']:
            self.logger.info("所有依赖库都已安装")
            return {
                'success': True,
                'message': '所有依赖库都已安装',
                'installed_packages': [],
                'failed_packages': [],
                'dependency_status': dependency_status
            }
        
        missing_packages = dependency_status['missing_packages']
        self.logger.info(f"发现 {len(missing_packages)} 个缺失的依赖库")
        
        # 如果不是自动确认，询问用户
        if not auto_confirm:
            print(f"\n📦 发现缺失的依赖库:")
            for pkg in missing_packages:
                print(f"   - {pkg['import_name']} (pip install {pkg['pip_name']})")
            
            response = input("\n是否自动安装这些依赖库? (y/n): ").lower().strip()
            if response not in ['y', 'yes', '是']:
                return {
                    'success': False,
                    'message': '用户取消安装',
                    'installed_packages': [],
                    'failed_packages': missing_packages,
                    'dependency_status': dependency_status
                }
        
        # 执行安装
        installed_packages = []
        failed_packages = []
        
        for pkg_info in missing_packages:
            pip_name = pkg_info['pip_name']
            import_name = pkg_info['import_name']
            
            self.logger.info(f"正在安装: {pip_name}")
            
            try:
                success = await self._install_package(pip_name)
                if success:
                    installed_packages.append(pkg_info)
                    self.installed_packages.add(pip_name.lower())
                    self.logger.info(f"✅ 成功安装: {pip_name}")
                else:
                    failed_packages.append(pkg_info)
                    self.failed_packages.add(pip_name)
                    self.logger.error(f"❌ 安装失败: {pip_name}")
                    
            except Exception as e:
                failed_packages.append(pkg_info)
                self.failed_packages.add(pip_name)
                self.logger.error(f"❌ 安装异常: {pip_name} - {e}")
        
        return {
            'success': len(failed_packages) == 0,
            'message': f'安装完成: {len(installed_packages)} 成功, {len(failed_packages)} 失败',
            'installed_packages': installed_packages,
            'failed_packages': failed_packages,
            'dependency_status': dependency_status
        }
    
    async def _install_package(self, package_name: str) -> bool:
        """
        安装单个包
        
        Args:
            package_name: 包名
            
        Returns:
            是否安装成功
        """
        try:
            # 使用subprocess异步执行pip install
            process = await asyncio.create_subprocess_exec(
                sys.executable, '-m', 'pip', 'install', package_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                self.logger.info(f"pip install {package_name} 执行成功")
                return True
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                self.logger.error(f"pip install {package_name} 失败: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行pip install {package_name} 异常: {e}")
            return False
    
    def _is_standard_library(self, module_name: str) -> bool:
        """检查是否为Python标准库"""
        standard_libs = {
            'os', 'sys', 'json', 'logging', 'pathlib', 'typing', 'asyncio',
            'datetime', 'time', 'random', 'math', 'statistics', 'collections',
            'itertools', 'functools', 'operator', 're', 'string', 'io',
            'tempfile', 'shutil', 'glob', 'fnmatch', 'pickle', 'csv',
            'configparser', 'argparse', 'getopt', 'threading', 'multiprocessing',
            'subprocess', 'socket', 'urllib', 'http', 'email', 'base64',
            'hashlib', 'hmac', 'secrets', 'ssl', 'sqlite3', 'uuid',
            'decimal', 'fractions', 'copy', 'pprint', 'enum', 'dataclasses'
        }
        return module_name.lower() in standard_libs
    
    def _get_pip_package_name(self, import_name: str) -> Optional[str]:
        """获取import名称对应的pip包名"""
        # 首先检查映射表
        if import_name in self.package_mapping:
            return self.package_mapping[import_name]
        
        # 对于大多数情况，import名称就是pip包名
        return import_name.lower()
    
    def _is_package_installed(self, package_name: str) -> bool:
        """检查包是否已安装"""
        package_name_lower = package_name.lower()
        
        # 检查已安装包列表
        if package_name_lower in self.installed_packages:
            return True
            
        # 尝试导入检查
        try:
            importlib.import_module(package_name)
            self.installed_packages.add(package_name_lower)
            return True
        except ImportError:
            pass
            
        # 检查是否在映射表中有对应的import名称
        for import_name, pip_name in self.package_mapping.items():
            if pip_name.lower() == package_name_lower:
                try:
                    importlib.import_module(import_name)
                    self.installed_packages.add(package_name_lower)
                    return True
                except ImportError:
                    pass
        
        return False
    
    def get_installation_summary(self) -> Dict[str, Any]:
        """获取安装摘要信息"""
        return {
            'total_installed': len(self.installed_packages),
            'total_failed': len(self.failed_packages),
            'installed_packages': sorted(list(self.installed_packages)),
            'failed_packages': sorted(list(self.failed_packages))
        }
    
    def suggest_alternatives(self, failed_package: str) -> List[str]:
        """为失败的包建议替代方案"""
        alternatives = {
            'PyPDF2': ['pdfplumber', 'pymupdf', 'pdfminer.six'],
            'opencv-python': ['Pillow', 'scikit-image'],
            'tensorflow': ['pytorch', 'scikit-learn'],
            'pytorch': ['tensorflow', 'scikit-learn'],
            'mysql-connector-python': ['pymysql', 'mysqlclient'],
            'psycopg2': ['psycopg2-binary', 'asyncpg'],
        }
        
        return alternatives.get(failed_package, [])
