"""
简单的智能体测试脚本
"""
import asyncio
import logging
from pathlib import Path

from src.core.agent import SelfEvolvingAgent


async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试自进化智能体基本功能\n")
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建智能体
    agent = SelfEvolvingAgent()
    
    # 初始化
    await agent.capability_registry.initialize()
    await agent.experience_recorder.initialize()
    
    print("✅ 智能体初始化完成")
    
    # 测试能力发现
    print("\n📋 测试能力发现...")
    required_caps = await agent.capability_discoverer.analyze_task("搜索最新的AI新闻并保存到文件")
    print(f"识别到所需能力: {required_caps}")
    
    # 测试能力注册
    print("\n📦 测试能力注册...")
    
    # 注册文件操作能力
    file_ops_code = '''"""
file_operations 能力模块
"""
import logging
from typing import Dict, Any
from pathlib import Path

class FileOperationsCapability:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def execute(self, params: Dict[str, Any] = {}, context: Dict[str, Any] = None) -> Dict[str, Any]:
        return {"status": "success", "message": "文件操作完成"}
'''
    
    success = await agent.capability_registry.register_capability(
        "file_operations",
        file_ops_code,
        {"type": "local", "description": "文件操作能力"}
    )
    
    if success:
        print("✅ 文件操作能力注册成功")
    else:
        print("❌ 文件操作能力注册失败")
        
    # 检查可用能力
    capabilities = agent.get_available_capabilities()
    print(f"当前可用能力: {capabilities}")
    
    # 测试任务执行
    print("\n🚀 测试任务执行...")
    
    # 设置简单的用户交互回调
    async def simple_callback(interaction_type, data):
        if interaction_type == "capability_request":
            capability = data.get("capability")
            print(f"模拟提供 {capability} API信息")
            return {
                "type": "mock",
                "description": f"模拟的 {capability} API"
            }
        return None
        
    agent.set_user_interaction_callback(simple_callback)
    
    # 执行简单任务
    result = await agent.execute_task("保存一些文本到文件")
    
    if result["status"] == "success":
        print("✅ 任务执行成功")
        print(f"结果: {result}")
    else:
        print("❌ 任务执行失败")
        print(f"错误: {result}")
        
    # 查看任务历史
    history = agent.get_task_history()
    print(f"\n📊 任务历史: {len(history)} 个任务")
    for task in history:
        status_emoji = "✅" if task["status"] == "completed" else "❌"
        print(f"  {status_emoji} {task['description']}")
        
    print("\n🎉 基本功能测试完成!")


async def test_capability_discovery():
    """测试能力发现功能"""
    print("\n🔍 测试能力发现功能")
    print("=" * 50)
    
    from src.core.capability_discoverer import CapabilityDiscoverer
    
    discoverer = CapabilityDiscoverer()
    
    test_tasks = [
        "搜索最新的AI新闻",
        "分析这个CSV文件的数据",
        "发送邮件给客户",
        "处理这张图片并调整大小",
        "查询数据库中的用户信息",
        "翻译这段英文文本",
        "定时执行数据备份任务"
    ]
    
    for task in test_tasks:
        capabilities = await discoverer.analyze_task(task)
        print(f"任务: {task}")
        print(f"所需能力: {capabilities}")
        print("-" * 30)


async def test_code_generation():
    """测试代码生成功能"""
    print("\n⚙️ 测试代码生成功能")
    print("=" * 50)
    
    from src.code_generation.code_generator import CodeGenerator
    
    generator = CodeGenerator()
    
    # 测试API信息
    api_info = {
        "type": "rest",
        "base_url": "https://api.example.com",
        "authentication": {
            "type": "api_key",
            "key": "test-key"
        },
        "endpoints": [
            {
                "name": "search",
                "method": "GET",
                "path": "/search",
                "description": "搜索功能",
                "parameters": [
                    {"name": "query", "type": "str", "description": "搜索关键词"},
                    {"name": "limit", "type": "int", "description": "结果数量", "default": "10"}
                ]
            }
        ]
    }
    
    try:
        code = await generator.generate_capability_module("test_search", api_info)
        print("✅ 代码生成成功")
        print("生成的代码片段:")
        print("-" * 30)
        print(code[:500] + "..." if len(code) > 500 else code)
        
    except Exception as e:
        print(f"❌ 代码生成失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 自进化智能体测试套件")
    print("=" * 60)
    
    try:
        # 基本功能测试
        await test_basic_functionality()
        
        # 能力发现测试
        await test_capability_discovery()
        
        # 代码生成测试
        await test_code_generation()
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
