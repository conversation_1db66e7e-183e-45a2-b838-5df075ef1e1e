# 自进化智能体配置文件

# 数据库配置
database:
  capabilities_db: "data/capabilities.db"
  experience_db: "data/experience.db"

# 日志配置
logging:
  level: "INFO"
  file: "logs/agent.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 能力管理配置
capability_management:
  capabilities_dir: "src/capabilities"
  auto_reload: true
  reload_debounce_delay: 1.0

# 代码生成配置
code_generation:
  templates_dir: "src/code_generation/templates"
  output_dir: "src/capabilities"
  backup_generated_code: true

# 任务执行配置
task_execution:
  max_execution_time: 300  # 5分钟
  max_retry_count: 3
  parallel_execution: false

# 学习系统配置
learning:
  experience_retention_days: 90
  pattern_confidence_threshold: 0.7
  auto_optimization: true

# 热更新配置
hot_reload:
  enabled: true
  watch_directories:
    - "src/capabilities"
  file_patterns:
    - "*.py"
  debounce_delay: 1.0

# API配置模板
api_templates:
  rest:
    default_timeout: 30
    default_headers:
      "User-Agent": "SelfEvolvingAgent/1.0"
      "Content-Type": "application/json"
  
  graphql:
    default_timeout: 30
    
  websocket:
    default_timeout: 60
    
# 安全配置
security:
  allowed_domains:
    - "api.openweathermap.org"
    - "api.duckduckgo.com"
    - "httpbin.org"
  
  max_file_size: 10485760  # 10MB
  allowed_file_types:
    - ".txt"
    - ".json"
    - ".csv"
    - ".md"
    
# 性能配置
performance:
  max_concurrent_tasks: 5
  capability_cache_size: 100
  experience_batch_size: 50

# 用户界面配置
ui:
  interactive_mode: true
  auto_confirm_api_requests: false
  show_debug_info: false
  
# 示例API配置
example_apis:
  weather:
    type: "rest"
    base_url: "https://api.openweathermap.org/data/2.5"
    authentication:
      type: "api_key"
      key: "your-api-key-here"
    endpoints:
      - name: "current_weather"
        method: "GET"
        path: "/weather"
        parameters:
          - name: "q"
            type: "str"
            description: "城市名称"
          - name: "appid"
            type: "str"
            description: "API密钥"
            
  search:
    type: "rest"
    base_url: "https://api.duckduckgo.com"
    endpoints:
      - name: "instant_answer"
        method: "GET"
        path: "/"
        parameters:
          - name: "q"
            type: "str"
            description: "搜索查询"
          - name: "format"
            type: "str"
            description: "返回格式"
            default: "json"
