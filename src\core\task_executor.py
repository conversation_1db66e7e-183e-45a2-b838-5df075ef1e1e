"""
任务执行器 - 协调各种能力来执行任务
"""
import asyncio
import logging
from typing import Dict, List, Any, Optional
import importlib
import sys
from pathlib import Path


class TaskExecutor:
    """
    任务执行器
    
    负责协调各种能力模块来执行具体任务
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.loaded_capabilities = {}
        
    async def execute(self, task) -> Any:
        """
        执行任务
        
        Args:
            task: 任务对象
            
        Returns:
            执行结果
        """
        self.logger.info(f"开始执行任务: {task.description}")
        
        try:
            # 1. 加载所需的能力模块
            await self._load_required_capabilities(task.required_capabilities)
            
            # 2. 制定执行计划
            execution_plan = await self._create_execution_plan(task)
            
            # 3. 按计划执行
            result = await self._execute_plan(execution_plan, task)
            
            self.logger.info(f"任务执行完成: {task.id}")
            return result
            
        except Exception as e:
            self.logger.error(f"任务执行失败: {e}")
            raise
            
    async def _load_required_capabilities(self, capabilities: List[str]):
        """加载所需的能力模块"""
        for capability in capabilities:
            if capability not in self.loaded_capabilities:
                try:
                    # 动态导入能力模块
                    module_path = f"src.capabilities.{capability}"
                    module = importlib.import_module(module_path)
                    
                    # 获取能力类
                    capability_class_name = self._get_capability_class_name(capability)
                    capability_class = getattr(module, capability_class_name)
                    
                    # 实例化能力
                    capability_instance = capability_class()
                    self.loaded_capabilities[capability] = capability_instance
                    
                    self.logger.info(f"成功加载能力: {capability}")
                    
                except Exception as e:
                    self.logger.error(f"加载能力失败 {capability}: {e}")
                    raise
                    
    def _get_capability_class_name(self, capability: str) -> str:
        """根据能力名称生成类名"""
        # 将下划线分隔的名称转换为驼峰命名
        words = capability.split('_')
        class_name = ''.join(word.capitalize() for word in words) + 'Capability'
        return class_name
        
    async def _create_execution_plan(self, task) -> List[Dict[str, Any]]:
        """
        创建执行计划
        
        根据任务描述和可用能力，制定执行步骤
        """
        plan = []
        
        # 简化的计划生成逻辑
        # 实际应用中可以使用更复杂的规划算法
        
        task_desc = task.description.lower()
        
        # 如果需要搜索
        if "web_search" in task.required_capabilities:
            plan.append({
                "step": "search",
                "capability": "web_search",
                "action": "search_web",
                "params": self._extract_search_params(task.description)
            })
            
        # 如果需要数据分析
        if "data_analysis" in task.required_capabilities:
            plan.append({
                "step": "analyze",
                "capability": "data_analysis",
                "action": "analyze_data",
                "params": {}
            })
            
        # 如果需要文件操作
        if "file_operations" in task.required_capabilities:
            plan.append({
                "step": "save",
                "capability": "file_operations",
                "action": "save_result",
                "params": {}
            })
            
        # 如果没有特定计划，创建通用计划
        if not plan:
            plan.append({
                "step": "execute",
                "capability": "general",
                "action": "execute_task",
                "params": {"description": task.description}
            })
            
        self.logger.info(f"创建执行计划: {len(plan)} 个步骤")
        return plan
        
    def _extract_search_params(self, description: str) -> Dict[str, Any]:
        """从任务描述中提取搜索参数"""
        # 简单的参数提取逻辑
        params = {"query": description}
        
        # 提取关键词
        if "搜索" in description:
            # 提取"搜索"后面的内容作为查询词
            import re
            match = re.search(r'搜索(.+?)(?:的|信息|资料|$)', description)
            if match:
                params["query"] = match.group(1).strip()
                
        return params
        
    async def _execute_plan(self, plan: List[Dict[str, Any]], task) -> Any:
        """执行计划"""
        results = []
        context = {}
        
        for step in plan:
            self.logger.info(f"执行步骤: {step['step']}")
            
            try:
                # 获取能力实例
                capability_name = step["capability"]
                if capability_name == "general":
                    # 通用执行逻辑
                    result = await self._execute_general_task(step, context)
                else:
                    capability = self.loaded_capabilities.get(capability_name)
                    if not capability:
                        raise ValueError(f"能力未加载: {capability_name}")
                    
                    # 调用能力方法
                    action = step["action"]
                    params = step["params"]
                    
                    if hasattr(capability, action):
                        method = getattr(capability, action)
                        if asyncio.iscoroutinefunction(method):
                            result = await method(**params, context=context)
                        else:
                            result = method(**params, context=context)
                    else:
                        raise ValueError(f"能力 {capability_name} 没有方法 {action}")
                
                results.append(result)
                context[step["step"]] = result
                
                self.logger.info(f"步骤 {step['step']} 执行完成")
                
            except Exception as e:
                self.logger.error(f"步骤 {step['step']} 执行失败: {e}")
                raise
                
        # 返回最终结果
        if len(results) == 1:
            return results[0]
        else:
            return {
                "steps": results,
                "final_result": results[-1] if results else None
            }
            
    async def _execute_general_task(self, step: Dict[str, Any], context: Dict[str, Any]) -> str:
        """执行通用任务"""
        description = step["params"].get("description", "")
        
        # 这里可以集成LLM来处理通用任务
        # 目前返回简单的响应
        return f"已处理任务: {description}"
        
    def get_loaded_capabilities(self) -> List[str]:
        """获取已加载的能力列表"""
        return list(self.loaded_capabilities.keys())
        
    async def reload_capability(self, capability_name: str):
        """重新加载能力模块"""
        if capability_name in self.loaded_capabilities:
            del self.loaded_capabilities[capability_name]
            
        # 重新导入模块
        module_path = f"src.capabilities.{capability_name}"
        if module_path in sys.modules:
            importlib.reload(sys.modules[module_path])
            
        await self._load_required_capabilities([capability_name])
