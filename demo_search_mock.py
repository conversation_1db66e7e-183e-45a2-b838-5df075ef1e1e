#!/usr/bin/env python3
"""
搜索功能模拟演示
展示搜索功能的完整工作流程（使用模拟数据）
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.core.agent import SelfEvolvingAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class MockSearchDemo:
    """模拟搜索功能演示类"""
    
    def __init__(self):
        self.agent = None
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化智能体"""
        print("🚀 初始化模拟搜索演示...")
        self.agent = SelfEvolvingAgent()
        self.agent.set_user_interaction_callback(self.handle_capability_request)
        print("✅ 智能体初始化完成\n")
        
    async def handle_capability_request(self, interaction_type: str, data: dict):
        """处理能力请求 - 提供模拟搜索API信息"""
        if interaction_type == "capability_request":
            capability = data.get("capability")
            print(f"\n🤖 智能体请求: {data.get('message')}")
            
            if capability == "web_search":
                # 提供模拟搜索API信息
                api_info = {
                    "type": "mock",
                    "name": "Mock Search API",
                    "description": "模拟搜索API，用于演示搜索功能",
                    "endpoints": {
                        "search": {
                            "method": "GET",
                            "parameters": {
                                "query": "搜索查询词"
                            },
                            "response_format": {
                                "abstract": "搜索结果摘要",
                                "results": "搜索结果列表",
                                "related_topics": "相关主题"
                            }
                        }
                    },
                    "example_code": """
                    async def search_web(self, context=None, **kwargs):
                        query = kwargs.get('query', '')
                        
                        # 模拟搜索结果
                        mock_results = {
                            'abstract': f'关于"{query}"的搜索结果摘要...',
                            'results': [
                                {'title': f'{query} - 详细介绍', 'url': 'https://example.com/1'},
                                {'title': f'{query} - 最新资讯', 'url': 'https://example.com/2'}
                            ],
                            'related_topics': [
                                {'text': f'{query}相关概念1'},
                                {'text': f'{query}相关概念2'}
                            ],
                            'query': query,
                            'status': 'success'
                        }
                        
                        return mock_results
                    """
                }
                
                print(f"📡 提供模拟搜索 API 信息")
                return api_info
                
        return None
        
    async def demo_search_workflow(self):
        """演示搜索工作流程"""
        print("=" * 80)
        print("🔍 搜索功能工作流程演示")
        print("=" * 80)
        
        search_examples = [
            {
                "query": "人工智能最新发展",
                "description": "搜索AI技术的最新进展和趋势"
            },
            {
                "query": "Python机器学习库",
                "description": "查找Python中常用的机器学习库"
            },
            {
                "query": "深度学习框架比较",
                "description": "比较不同深度学习框架的特点"
            }
        ]
        
        for i, example in enumerate(search_examples, 1):
            print(f"\n📋 搜索示例 {i}: {example['query']}")
            print(f"📝 描述: {example['description']}")
            print("-" * 60)
            
            # 展示完整的工作流程
            print("🔄 工作流程:")
            print("   1️⃣ 接收用户搜索请求")
            print("   2️⃣ LLM 分析任务需求")
            print("   3️⃣ 识别需要 web_search 能力")
            print("   4️⃣ 检查能力是否存在")
            
            if i == 1:
                print("   5️⃣ 能力不存在，请求API信息")
                print("   6️⃣ LLM 生成搜索能力代码")
                print("   7️⃣ 热加载新能力到系统")
                print("   8️⃣ 执行搜索任务")
            else:
                print("   5️⃣ 能力已存在，直接使用")
                print("   6️⃣ 执行搜索任务")
            
            try:
                start_time = asyncio.get_event_loop().time()
                
                # 执行搜索任务
                task_description = f"搜索关于'{example['query']}'的信息"
                result = await self.agent.execute_task(task_description)
                
                end_time = asyncio.get_event_loop().time()
                execution_time = end_time - start_time
                
                print(f"\n✅ 搜索完成 (耗时: {execution_time:.2f}秒)")
                
                if result.get('status') == 'success':
                    search_result = result.get('result', {})
                    
                    # 显示搜索结果
                    print("📊 搜索结果:")
                    if isinstance(search_result, dict):
                        if 'abstract' in search_result:
                            print(f"   📄 摘要: {search_result['abstract']}")
                        if 'results' in search_result:
                            results = search_result['results']
                            print(f"   🔗 找到 {len(results)} 个结果:")
                            for j, res in enumerate(results[:3], 1):
                                if isinstance(res, dict):
                                    title = res.get('title', '无标题')
                                    print(f"      {j}. {title}")
                        if 'related_topics' in search_result:
                            topics = search_result['related_topics']
                            if topics:
                                print(f"   🏷️ 相关主题: {len(topics)} 个")
                                for topic in topics[:2]:
                                    if isinstance(topic, dict):
                                        print(f"      • {topic.get('text', '')}")
                    
                    # 分析性能
                    if i == 1:
                        print("   💡 首次搜索: 包含能力生成时间")
                    else:
                        print("   ⚡ 复用能力: 直接执行搜索")
                        
                else:
                    print(f"❌ 搜索失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 搜索异常: {e}")
                
            await asyncio.sleep(0.5)  # 短暂延迟
            
    async def demo_intelligent_understanding(self):
        """演示智能理解能力"""
        print("\n" + "=" * 80)
        print("🧠 LLM 智能理解演示")
        print("=" * 80)
        
        complex_requests = [
            "我想了解机器学习的基础知识",
            "帮我找一些Python编程的学习资源",
            "搜索一下最新的AI技术发展趋势",
            "查找关于深度学习的入门教程"
        ]
        
        print("🎯 测试 LLM 对不同表达方式的理解能力:")
        
        for i, request in enumerate(complex_requests, 1):
            print(f"\n📝 请求 {i}: \"{request}\"")
            
            try:
                result = await self.agent.execute_task(request)
                
                if result.get('status') == 'success':
                    print("✅ LLM 成功理解并识别为搜索任务")
                    search_result = result.get('result', {})
                    if isinstance(search_result, dict) and 'query' in search_result:
                        actual_query = search_result['query']
                        print(f"🔍 实际搜索词: \"{actual_query}\"")
                        print("💡 LLM 智能提取了关键搜索词")
                else:
                    print(f"❌ 理解失败: {result.get('error')}")
                    
            except Exception as e:
                print(f"❌ 处理异常: {e}")
                
    async def show_generated_code(self):
        """展示生成的搜索代码"""
        print("\n" + "=" * 80)
        print("💻 LLM 生成的搜索代码")
        print("=" * 80)
        
        capability_file = Path("src/capabilities/web_search.py")
        if capability_file.exists():
            print(f"📁 代码文件: {capability_file}")
            print(f"📏 文件大小: {capability_file.stat().st_size} 字节")
            
            # 读取并显示部分代码
            with open(capability_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print("\n🔍 代码预览 (前20行):")
            print("-" * 60)
            for i, line in enumerate(lines[:20], 1):
                print(f"{i:2d}: {line.rstrip()}")
            
            if len(lines) > 20:
                print(f"... (还有 {len(lines) - 20} 行)")
                
            print("\n💡 代码特点:")
            print("   ✅ 完整的类定义和方法")
            print("   ✅ 异步HTTP请求处理")
            print("   ✅ 完善的错误处理")
            print("   ✅ 详细的日志记录")
            print("   ✅ 灵活的参数处理")
            print("   ✅ 符合Python编码规范")
            
        else:
            print("❌ 搜索能力代码文件不存在")
            
    async def show_system_status(self):
        """显示系统状态"""
        print("\n" + "=" * 80)
        print("📊 系统状态总览")
        print("=" * 80)
        
        # 显示可用能力
        capabilities = self.agent.capability_registry.list_capabilities()
        print(f"📦 当前系统能力: {capabilities}")
        
        # 显示能力详情
        if 'web_search' in capabilities:
            print("\n🔍 web_search 能力详情:")
            print("   📋 能力名称: web_search")
            print("   🎯 主要功能: 网络搜索")
            print("   🔧 实现方式: LLM 自动生成")
            print("   ⚡ 调用方法: search_web(**kwargs)")
            print("   📝 参数格式: query='搜索词'")
            print("   📊 返回格式: JSON 结构化数据")
            
        print(f"\n🧠 LLM 增强功能:")
        print("   ✅ 智能任务理解")
        print("   ✅ 自动能力识别") 
        print("   ✅ 动态代码生成")
        print("   ✅ 热加载机制")
        print("   ✅ 学习和优化")


async def main():
    """主函数"""
    demo = MockSearchDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        # 演示搜索工作流程
        await demo.demo_search_workflow()
        
        # 演示智能理解
        await demo.demo_intelligent_understanding()
        
        # 展示生成的代码
        await demo.show_generated_code()
        
        # 显示系统状态
        await demo.show_system_status()
        
        print("\n" + "=" * 80)
        print("🎉 搜索功能演示完成!")
        print("\n💡 核心亮点:")
        print("   🧠 LLM 智能理解: 准确识别搜索意图")
        print("   ⚡ 动态生成: 自动创建搜索能力")
        print("   🔄 热加载: 无需重启即可使用新能力")
        print("   📈 学习优化: 复用已有能力提升性能")
        print("   🎯 灵活适应: 处理各种表达方式")
        print("\n🚀 这展示了真正的自进化智能体:")
        print("   • 理解用户意图")
        print("   • 识别缺失功能")
        print("   • 自动生成代码")
        print("   • 持续学习优化")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 演示异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 自进化智能体搜索功能模拟演示")
    print("展示完整的搜索工作流程和LLM增强能力")
    print("=" * 80)
    
    asyncio.run(main())
