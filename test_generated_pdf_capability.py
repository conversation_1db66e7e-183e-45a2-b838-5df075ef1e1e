"""
测试生成的PDF处理能力
验证Agent自动生成的代码是否真的可以工作
"""
import asyncio
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)

async def test_generated_pdf_capability():
    """测试生成的PDF处理能力"""
    print("🧪 测试生成的PDF处理能力")
    print("=" * 50)
    
    try:
        # 导入生成的模块
        import importlib.util
        
        code_file = Path("generated_capabilities/pdf_document_analysis_with_deps.py")
        if not code_file.exists():
            print("❌ 生成的代码文件不存在")
            return False
            
        spec = importlib.util.spec_from_file_location("pdf_capability", code_file)
        pdf_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(pdf_module)
        
        print("✅ 成功导入生成的模块")
        
        # 创建PDF处理器实例
        if hasattr(pdf_module, 'PdfProcessingCapability'):
            pdf_processor = pdf_module.PdfProcessingCapability()
            print("✅ 成功创建PDF处理器实例")
            
            # 检查可用方法
            methods = [method for method in dir(pdf_processor) 
                      if not method.startswith('_') and callable(getattr(pdf_processor, method))]
            print(f"📋 可用方法: {', '.join(methods)}")
            
            # 测试基本功能（不需要真实PDF文件）
            print("\n🔍 测试基本功能...")
            
            # 测试错误处理
            try:
                result = await pdf_processor.process_pdf(file_path="nonexistent.pdf")
            except FileNotFoundError as e:
                print("✅ 错误处理正常：文件不存在时正确抛出异常")
            except Exception as e:
                print(f"⚠️  意外错误: {e}")
            
            try:
                result = await pdf_processor.process_pdf()
            except ValueError as e:
                print("✅ 参数验证正常：缺少必需参数时正确抛出异常")
            except Exception as e:
                print(f"⚠️  意外错误: {e}")
            
            print("\n🎉 生成的PDF处理代码基本功能测试通过！")
            print("💡 Agent成功生成了可工作的PDF处理代码")
            
            return True
            
        else:
            print("❌ 未找到预期的PdfProcessingCapability类")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 这可能表示依赖库安装有问题")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_dependency_verification():
    """验证依赖库是否正确安装"""
    print("\n📦 验证依赖库安装状态")
    print("=" * 50)
    
    required_libs = ['PyPDF2', 'pdfplumber', 'reportlab']
    
    for lib in required_libs:
        try:
            __import__(lib)
            print(f"✅ {lib}: 已安装")
        except ImportError:
            print(f"❌ {lib}: 未安装")
            return False
    
    print("🎉 所有依赖库都已正确安装！")
    return True


async def main():
    """主测试函数"""
    print("🚀 开始测试Agent生成的PDF处理能力")
    
    # 测试1: 验证依赖安装
    deps_ok = await test_dependency_verification()
    
    # 测试2: 测试生成的代码
    if deps_ok:
        code_ok = await test_generated_pdf_capability()
    else:
        print("⚠️  跳过代码测试，因为依赖库未正确安装")
        code_ok = False
    
    print(f"\n🎯 测试总结:")
    print(f"   - 依赖安装: {'✅' if deps_ok else '❌'}")
    print(f"   - 代码功能: {'✅' if code_ok else '❌'}")
    
    if deps_ok and code_ok:
        print("\n🎉 恭喜！Agent的自动依赖管理和代码生成功能完全正常！")
        print("💡 Agent现在可以：")
        print("   1. 自动检测缺失的依赖库")
        print("   2. 自动安装所需的依赖")
        print("   3. 生成可工作的代码")
        print("   4. 处理依赖相关的错误")
    else:
        print("\n⚠️  还有一些问题需要解决")


if __name__ == "__main__":
    asyncio.run(main())
