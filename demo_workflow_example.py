#!/usr/bin/env python3
"""
自进化智能体工作流程完整演示
展示从任务理解到能力生成的完整过程
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.core.agent import SelfEvolvingAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class WorkflowDemo:
    """工作流程演示类"""
    
    def __init__(self):
        self.agent = None
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化智能体"""
        print("🚀 初始化自进化智能体...")
        self.agent = SelfEvolvingAgent()
        self.agent.set_user_interaction_callback(self.handle_capability_request)
        print("✅ 智能体初始化完成\n")
        
    async def handle_capability_request(self, interaction_type: str, data: dict):
        """处理能力请求 - 模拟用户提供API信息"""
        if interaction_type == "capability_request":
            capability = data.get("capability")
            print(f"\n🤖 智能体请求: {data.get('message')}")
            
            # 提供简化的API信息用于演示
            api_info = {
                "type": "rest",
                "base_url": "https://api.example.com",
                "endpoints": {
                    "main_action": {
                        "method": "GET",
                        "path": "/action",
                        "parameters": {
                            "query": "搜索关键词"
                        }
                    }
                },
                "description": f"{capability} API演示"
            }
            
            print(f"📡 提供 {capability} API信息")
            return api_info
        
        return None
        
    def print_step(self, step_num: int, title: str, description: str):
        """打印步骤信息"""
        print(f"\n{'='*60}")
        print(f"📋 步骤 {step_num}: {title}")
        print(f"{'='*60}")
        print(f"📝 {description}")
        print()
        
    async def demonstrate_workflow(self):
        """演示完整工作流程"""
        print("🎯 自进化智能体工作流程完整演示")
        print("=" * 80)
        
        # 演示任务
        task_description = "搜索最新的人工智能技术新闻"
        
        self.print_step(1, "任务接收", 
                       f"用户输入任务: '{task_description}'")
        
        self.print_step(2, "LLM 智能分析", 
                       "使用 DeepSeek API 分析任务，识别所需能力")
        
        # 开始执行任务
        print("🔄 开始执行任务...")
        result = await self.agent.execute_task(task_description)
        
        self.print_step(3, "能力检查", 
                       "检查系统中是否已有所需能力")
        
        self.print_step(4, "用户交互", 
                       "如果缺少能力，向用户请求API文档")
        
        self.print_step(5, "LLM 代码生成", 
                       "使用 DeepSeek API 根据API文档生成Python代码")
        
        self.print_step(6, "能力注册", 
                       "验证代码语法，动态加载到系统中")
        
        self.print_step(7, "任务执行", 
                       "使用新生成的能力执行原始任务")
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 执行结果")
        print("="*60)
        
        if result.get('status') == 'success':
            print("✅ 任务执行成功!")
            print(f"📄 结果: {result.get('result', '无具体结果')}")
        else:
            print("❌ 任务执行失败")
            print(f"🔍 原因: {result.get('error', '未知错误')}")
            
        # 显示系统状态
        capabilities = self.agent.capability_registry.list_capabilities()
        print(f"\n📦 当前系统能力: {capabilities}")
        
        return result
        
    async def demonstrate_learning(self):
        """演示学习和优化过程"""
        print("\n" + "="*80)
        print("🧠 学习和优化演示")
        print("="*80)
        
        # 执行相似任务
        similar_tasks = [
            "搜索机器学习相关资讯",
            "查找深度学习最新论文",
            "搜索AI技术发展趋势"
        ]
        
        print("🔄 执行相似任务，观察系统学习效果...")
        
        for i, task in enumerate(similar_tasks, 1):
            print(f"\n📋 相似任务 {i}: {task}")
            
            start_time = asyncio.get_event_loop().time()
            result = await self.agent.execute_task(task)
            end_time = asyncio.get_event_loop().time()
            
            execution_time = end_time - start_time
            
            if result.get('status') == 'success':
                print(f"✅ 任务完成 (耗时: {execution_time:.2f}秒)")
                print("💡 系统复用了已有的 web_search 能力，无需重新生成")
            else:
                print(f"❌ 任务失败: {result.get('error')}")
                
        print("\n🎯 学习效果:")
        print("- 首次任务: 需要生成新能力 (较慢)")
        print("- 相似任务: 直接复用能力 (快速)")
        print("- 系统记忆: 保存任务经验，持续优化")
        
    async def show_system_architecture(self):
        """展示系统架构"""
        print("\n" + "="*80)
        print("🏗️ 系统架构概览")
        print("="*80)
        
        architecture = """
        ┌─────────────────────────────────────────────────────────────┐
        │                    用户任务输入                              │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │              🧠 LLM 增强能力发现器                          │
        │         (DeepSeek API 智能分析任务需求)                     │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │                能力注册表检查                                │
        │            (检查是否已有所需能力)                           │
        └─────────────────────┬───────────────────────────────────────┘
                              │
                    ┌─────────▼─────────┐
                    │   能力是否存在?    │
                    └─────────┬─────────┘
                              │
                    ┌─────────▼─────────┐
                    │       否          │
                    └─────────┬─────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │                  用户交互                                   │
        │              (请求API文档)                                  │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │              🤖 LLM 智能代码生成器                          │
        │        (DeepSeek API 生成完整Python类)                     │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │                代码验证与注册                                │
        │            (语法检查 + 热加载)                              │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │                  任务执行器                                 │
        │              (调用新生成的能力)                             │
        └─────────────────────┬───────────────────────────────────────┘
                              │
        ┌─────────────────────▼───────────────────────────────────────┐
        │                  经验记录器                                 │
        │            (保存执行经验，持续学习)                         │
        └─────────────────────────────────────────────────────────────┘
        """
        
        print(architecture)
        
        print("\n🔧 核心组件说明:")
        print("- 🧠 LLM 能力发现器: 使用大语言模型理解任务需求")
        print("- 🤖 LLM 代码生成器: 基于API文档自动生成Python代码")
        print("- 📦 能力注册表: 管理所有可用能力，支持热加载")
        print("- ⚡ 任务执行器: 智能调度和执行任务")
        print("- 🧠 经验记录器: 学习和优化系统性能")
        print("- 🔄 决策引擎: 智能决策和策略优化")


async def main():
    """主函数"""
    demo = WorkflowDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        # 演示完整工作流程
        await demo.demonstrate_workflow()
        
        # 演示学习过程
        await demo.demonstrate_learning()
        
        # 展示系统架构
        await demo.show_system_architecture()
        
        print("\n" + "="*80)
        print("🎉 工作流程演示完成!")
        print("💡 这展示了一个真正的自进化智能体系统:")
        print("   - 智能理解任务需求")
        print("   - 自动生成缺失能力") 
        print("   - 持续学习和优化")
        print("   - 适应新的任务类型")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 演示异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 自进化智能体工作流程演示")
    print("展示从任务理解到能力生成的完整过程")
    print("=" * 80)
    
    asyncio.run(main())
