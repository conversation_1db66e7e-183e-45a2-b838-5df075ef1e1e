"""
自动依赖管理演示
演示Agent如何自动检测、安装和管理代码生成过程中的依赖库
"""
import asyncio
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 导入自主代码生成组件
from src.autonomous.code_generator import CodeGenerator, CapabilityAnalyzer, ImplementationType
from src.autonomous.code_validator import CodeValidator
from src.autonomous.dependency_manager import DependencyManager


async def demo_auto_dependency_management():
    """演示自动依赖管理功能"""
    print("=" * 80)
    print("🤖 自动依赖管理演示：Agent自主处理缺失依赖")
    print("=" * 80)
    
    try:
        # 1. 创建依赖管理器
        print("\n📦 步骤1: 初始化依赖管理器...")
        dependency_manager = DependencyManager()
        print("✅ 依赖管理器初始化完成")
        
        # 2. 演示依赖检查
        print("\n🔍 步骤2: 检查PDF处理相关依赖...")
        required_libraries = ['PyPDF2', 'pdfplumber', 'reportlab', 'pathlib', 'logging', 'typing']
        
        dependency_status = dependency_manager.check_dependencies(required_libraries)
        
        print(f"📊 依赖检查结果:")
        print(f"   - 总需求库: {dependency_status['total_required']}")
        print(f"   - 已安装: {len(dependency_status['available_packages'])}")
        print(f"   - 缺失: {dependency_status['missing_count']}")
        print(f"   - 未知: {dependency_status['unknown_count']}")
        
        if dependency_status['available_packages']:
            print(f"   - 已安装库: {', '.join(dependency_status['available_packages'])}")
            
        if dependency_status['missing_packages']:
            print(f"   - 缺失库:")
            for pkg in dependency_status['missing_packages']:
                print(f"     • {pkg['import_name']} -> pip install {pkg['pip_name']}")
                
        if dependency_status['unknown_packages']:
            print(f"   - 未知库: {', '.join(dependency_status['unknown_packages'])}")
        
        # 3. 演示自动安装
        if dependency_status['missing_packages']:
            print(f"\n💾 步骤3: 自动安装缺失依赖...")
            
            install_result = await dependency_manager.auto_install_dependencies(
                required_libraries, auto_confirm=True
            )
            
            print(f"📊 安装结果:")
            print(f"   - 安装状态: {'✅ 成功' if install_result['success'] else '❌ 部分失败'}")
            print(f"   - 消息: {install_result['message']}")
            
            if install_result['installed_packages']:
                print(f"   - 成功安装:")
                for pkg in install_result['installed_packages']:
                    print(f"     ✅ {pkg['import_name']} ({pkg['pip_name']})")
                    
            if install_result['failed_packages']:
                print(f"   - 安装失败:")
                for pkg in install_result['failed_packages']:
                    print(f"     ❌ {pkg['import_name']} ({pkg['pip_name']})")
                    
                    # 建议替代方案
                    alternatives = dependency_manager.suggest_alternatives(pkg['pip_name'])
                    if alternatives:
                        print(f"       💡 建议替代: {', '.join(alternatives)}")
        else:
            print(f"\n✅ 步骤3: 所有依赖都已安装，无需额外操作")
        
        # 4. 演示完整的代码生成流程（带依赖管理）
        print(f"\n🚀 步骤4: 演示完整的代码生成流程...")
        
        generator = CodeGenerator(deepseek_api_key=None)  # 使用模板模式
        
        capability_name = "pdf_document_analysis"
        task_description = "分析PDF文档，提取文本内容、识别文档结构、提取表格数据和元数据信息"
        
        print(f"   - 能力名称: {capability_name}")
        print(f"   - 任务描述: {task_description}")
        
        # 生成代码计划（包含自动依赖管理）
        async with generator:
            plan = await generator.generate_code_plan(capability_name, task_description)
        
        print(f"📝 代码生成完成:")
        print(f"   - 实现类型: {plan.implementation_type.value}")
        print(f"   - 复杂度: {plan.complexity_level}")
        print(f"   - 预估行数: {plan.estimated_lines}")
        print(f"   - 所需库: {', '.join(plan.required_libraries)}")
        
        # 5. 验证生成的代码
        print(f"\n🔍 步骤5: 验证生成的代码...")
        validator = CodeValidator()
        validation_result = await validator.validate_code(plan)
        
        print(f"📊 验证结果:")
        print(f"   - 总体评分: {validation_result.score:.1f}/100")
        print(f"   - 验证通过: {'✅' if validation_result.is_valid else '❌'}")
        
        # 6. 测试代码执行
        print(f"\n🧪 步骤6: 测试代码执行...")
        
        # 保存代码到临时文件
        output_dir = Path("generated_capabilities")
        output_dir.mkdir(exist_ok=True)
        
        code_file = output_dir / f"{capability_name}_with_deps.py"
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(f'"""\n{plan.documentation}\n"""\n\n')
            f.write(plan.code_template)
        
        print(f"✅ 代码已保存到: {code_file}")
        
        # 尝试导入和执行
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("pdf_capability_with_deps", code_file)
            pdf_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(pdf_module)
            
            print("✅ 代码导入成功")
            
            # 检查类是否存在
            if hasattr(pdf_module, 'PdfProcessingCapability'):
                pdf_analyzer = pdf_module.PdfProcessingCapability()
                print("✅ 成功创建PDF分析器实例")
                
                # 显示可用方法
                methods = [method for method in dir(pdf_analyzer) 
                          if not method.startswith('_') and callable(getattr(pdf_analyzer, method))]
                print(f"📋 可用方法: {', '.join(methods)}")
                
                print("🎉 代码执行测试成功！依赖管理工作正常！")
                
            else:
                print("⚠️  未找到预期的类")
                
        except ImportError as e:
            if "No module named" in str(e):
                missing_module = str(e).split("'")[1]
                print(f"❌ 仍然缺少模块: {missing_module}")
                print("💡 这可能需要重启Python环境或检查安装状态")
            else:
                print(f"❌ 导入错误: {e}")
        except Exception as e:
            print(f"❌ 执行错误: {e}")
        
        # 7. 显示依赖管理摘要
        print(f"\n📈 步骤7: 依赖管理摘要...")
        summary = dependency_manager.get_installation_summary()
        
        print(f"📊 依赖管理统计:")
        print(f"   - 总安装包数: {summary['total_installed']}")
        print(f"   - 总失败包数: {summary['total_failed']}")
        
        if summary['failed_packages']:
            print(f"   - 失败的包: {', '.join(summary['failed_packages'])}")
        
        print("\n" + "=" * 80)
        print("🎉 自动依赖管理演示完成！")
        print("💡 Agent现在可以自动处理代码生成过程中的依赖问题")
        print("=" * 80)
        
        return {
            "success": True,
            "dependency_status": dependency_status,
            "install_result": install_result if dependency_status['missing_packages'] else None,
            "validation_score": validation_result.score,
            "code_file": str(code_file)
        }
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }


async def demo_dependency_scenarios():
    """演示不同的依赖场景"""
    print("\n" + "=" * 80)
    print("🔄 多场景依赖管理演示")
    print("=" * 80)
    
    scenarios = [
        {
            "name": "图像处理",
            "libraries": ["PIL", "opencv-python", "numpy", "matplotlib"]
        },
        {
            "name": "数据分析",
            "libraries": ["pandas", "numpy", "matplotlib", "seaborn", "plotly"]
        },
        {
            "name": "Web开发",
            "libraries": ["flask", "requests", "beautifulsoup4", "lxml"]
        }
    ]
    
    dependency_manager = DependencyManager()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['name']}")
        
        status = dependency_manager.check_dependencies(scenario['libraries'])
        
        print(f"   - 需求库: {len(scenario['libraries'])}")
        print(f"   - 已安装: {len(status['available_packages'])}")
        print(f"   - 缺失: {status['missing_count']}")
        
        if status['missing_packages']:
            print(f"   - 缺失库: {', '.join([pkg['import_name'] for pkg in status['missing_packages']])}")


async def main():
    """主演示函数"""
    print("🚀 启动自动依赖管理演示系统")
    
    # 演示1: 自动依赖管理
    main_result = await demo_auto_dependency_management()
    
    # 演示2: 多场景依赖检查
    await demo_dependency_scenarios()
    
    print(f"\n🎯 演示总结:")
    print(f"   - 主要演示: {'✅' if main_result['success'] else '❌'}")
    
    if main_result['success']:
        print(f"   - 生成的代码文件: {main_result['code_file']}")
        print(f"   - 验证评分: {main_result['validation_score']:.1f}")


if __name__ == "__main__":
    asyncio.run(main())
