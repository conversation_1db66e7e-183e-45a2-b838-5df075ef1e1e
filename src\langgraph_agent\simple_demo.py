"""
简化的动态工作流演示
展示核心的动态工作流和智能Prompt生成功能
"""
import asyncio
import logging
import json
import time
from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class WorkflowPattern(Enum):
    """工作流模式枚举"""
    LINEAR = "linear"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    ITERATIVE = "iterative"
    HYBRID = "hybrid"


class WorkflowComplexity(Enum):
    """工作流复杂度枚举"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    EXPERT = "expert"


@dataclass
class WorkflowNode:
    """工作流节点"""
    name: str
    node_type: str
    description: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    estimated_duration: float


@dataclass
class WorkflowEdge:
    """工作流边"""
    from_node: str
    to_node: str
    condition: str = None
    condition_mapping: Dict[str, str] = None


@dataclass
class GeneratedWorkflow:
    """生成的工作流"""
    workflow_id: str
    name: str
    description: str
    pattern: WorkflowPattern
    complexity: WorkflowComplexity
    nodes: List[WorkflowNode]
    edges: List[WorkflowEdge]
    estimated_duration: float
    optimization_hints: List[str]


class SimpleDynamicWorkflowGenerator:
    """简化的动态工作流生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def generate_workflow(self, task_description: str, context: Dict[str, Any] = None) -> GeneratedWorkflow:
        """生成工作流"""
        self.logger.info(f"🎯 开始生成工作流: {task_description}")
        
        # 分析任务特征
        task_analysis = self._analyze_task(task_description)
        
        # 选择工作流模式
        pattern = self._select_pattern(task_analysis)
        
        # 确定复杂度
        complexity = self._determine_complexity(task_analysis)
        
        # 生成节点
        nodes = self._generate_nodes(task_analysis, pattern, complexity)
        
        # 生成边
        edges = self._generate_edges(nodes, pattern)
        
        # 估算时间
        estimated_duration = sum(node.estimated_duration for node in nodes)
        
        # 创建工作流
        workflow = GeneratedWorkflow(
            workflow_id=f"workflow_{int(time.time())}",
            name=f"动态工作流_{pattern.value}",
            description=f"为任务'{task_description}'生成的{complexity.value}级{pattern.value}工作流",
            pattern=pattern,
            complexity=complexity,
            nodes=nodes,
            edges=edges,
            estimated_duration=estimated_duration,
            optimization_hints=self._generate_optimization_hints(pattern, complexity)
        )
        
        self.logger.info(f"✅ 工作流生成完成: {workflow.name}")
        return workflow
    
    def _analyze_task(self, task_description: str) -> Dict[str, Any]:
        """分析任务特征"""
        analysis = {
            "keywords": task_description.lower().split(),
            "has_data_processing": any(word in task_description.lower() for word in ["数据", "分析", "处理", "文件"]),
            "has_api_calls": any(word in task_description.lower() for word in ["api", "接口", "服务", "网络"]),
            "has_parallel_tasks": any(word in task_description.lower() for word in ["多个", "批量", "并行", "同时"]),
            "has_conditions": any(word in task_description.lower() for word in ["如果", "条件", "判断", "选择"]),
            "has_iterations": any(word in task_description.lower() for word in ["循环", "重复", "迭代", "训练"]),
            "complexity_indicators": len([word for word in ["复杂", "高级", "专业", "算法", "模型"] if word in task_description])
        }
        return analysis
    
    def _select_pattern(self, analysis: Dict[str, Any]) -> WorkflowPattern:
        """选择工作流模式"""
        if analysis["has_parallel_tasks"]:
            return WorkflowPattern.PARALLEL
        elif analysis["has_conditions"]:
            return WorkflowPattern.CONDITIONAL
        elif analysis["has_iterations"]:
            return WorkflowPattern.ITERATIVE
        elif analysis["has_data_processing"] and analysis["has_api_calls"]:
            return WorkflowPattern.HYBRID
        else:
            return WorkflowPattern.LINEAR
    
    def _determine_complexity(self, analysis: Dict[str, Any]) -> WorkflowComplexity:
        """确定复杂度"""
        complexity_score = 0
        
        if analysis["has_data_processing"]:
            complexity_score += 1
        if analysis["has_api_calls"]:
            complexity_score += 1
        if analysis["has_parallel_tasks"]:
            complexity_score += 1
        if analysis["has_conditions"]:
            complexity_score += 1
        if analysis["has_iterations"]:
            complexity_score += 2
        
        complexity_score += analysis["complexity_indicators"]
        
        if complexity_score >= 5:
            return WorkflowComplexity.EXPERT
        elif complexity_score >= 3:
            return WorkflowComplexity.COMPLEX
        elif complexity_score >= 1:
            return WorkflowComplexity.MEDIUM
        else:
            return WorkflowComplexity.SIMPLE
    
    def _generate_nodes(self, analysis: Dict[str, Any], pattern: WorkflowPattern, complexity: WorkflowComplexity) -> List[WorkflowNode]:
        """生成节点"""
        nodes = []
        
        # 基础节点
        nodes.append(WorkflowNode(
            name="task_reception",
            node_type="reception",
            description="接收和解析任务",
            parameters={"timeout": 30},
            dependencies=[],
            estimated_duration=5.0
        ))
        
        nodes.append(WorkflowNode(
            name="task_analysis",
            node_type="analysis",
            description="分析任务需求",
            parameters={"depth": "detailed" if complexity in [WorkflowComplexity.COMPLEX, WorkflowComplexity.EXPERT] else "basic"},
            dependencies=["task_reception"],
            estimated_duration=10.0 if complexity == WorkflowComplexity.EXPERT else 5.0
        ))
        
        # 根据分析结果添加特定节点
        if analysis["has_data_processing"]:
            nodes.append(WorkflowNode(
                name="data_processing",
                node_type="processing",
                description="数据处理和分析",
                parameters={"batch_size": 1000, "parallel": analysis["has_parallel_tasks"]},
                dependencies=["task_analysis"],
                estimated_duration=30.0
            ))
        
        if analysis["has_api_calls"]:
            nodes.append(WorkflowNode(
                name="api_integration",
                node_type="integration",
                description="API集成和调用",
                parameters={"retry_count": 3, "timeout": 60},
                dependencies=["task_analysis"],
                estimated_duration=20.0
            ))
        
        if pattern == WorkflowPattern.PARALLEL:
            for i in range(2):
                nodes.append(WorkflowNode(
                    name=f"parallel_task_{i+1}",
                    node_type="execution",
                    description=f"并行任务 {i+1}",
                    parameters={"parallel_group": f"group_{i+1}"},
                    dependencies=["task_analysis"],
                    estimated_duration=15.0
                ))
        
        # 结果处理节点
        nodes.append(WorkflowNode(
            name="result_processing",
            node_type="processing",
            description="处理和整合结果",
            parameters={"format": "json"},
            dependencies=[node.name for node in nodes if node.node_type in ["processing", "integration", "execution"]],
            estimated_duration=10.0
        ))
        
        nodes.append(WorkflowNode(
            name="response_generation",
            node_type="response",
            description="生成最终响应",
            parameters={"format": "detailed"},
            dependencies=["result_processing"],
            estimated_duration=5.0
        ))
        
        return nodes
    
    def _generate_edges(self, nodes: List[WorkflowNode], pattern: WorkflowPattern) -> List[WorkflowEdge]:
        """生成边"""
        edges = []
        
        # 基于依赖关系生成边
        for node in nodes:
            for dependency in node.dependencies:
                edges.append(WorkflowEdge(
                    from_node=dependency,
                    to_node=node.name
                ))
        
        # 根据模式添加特殊边
        if pattern == WorkflowPattern.CONDITIONAL:
            # 添加条件边
            edges.append(WorkflowEdge(
                from_node="task_analysis",
                to_node="result_processing",
                condition="simple_task",
                condition_mapping={"simple": "result_processing", "complex": "data_processing"}
            ))
        
        return edges
    
    def _generate_optimization_hints(self, pattern: WorkflowPattern, complexity: WorkflowComplexity) -> List[str]:
        """生成优化提示"""
        hints = []
        
        if pattern == WorkflowPattern.PARALLEL:
            hints.append("考虑使用线程池或异步处理提高并行效率")
        
        if complexity == WorkflowComplexity.EXPERT:
            hints.append("建议添加详细的监控和日志记录")
            hints.append("考虑实现断点续传和错误恢复机制")
        
        if pattern == WorkflowPattern.ITERATIVE:
            hints.append("优化迭代终止条件以避免无限循环")
        
        hints.append("建议添加性能监控和资源使用统计")
        
        return hints


class SimpleDemo:
    """简化演示类"""
    
    def __init__(self):
        self.workflow_generator = SimpleDynamicWorkflowGenerator()
        self.demo_tasks = [
            "计算两个数字的平方和",
            "分析CSV文件中的销售数据并生成报告",
            "集成天气API获取多个城市的天气信息",
            "训练一个文本分类模型并进行评估",
            "爬取网站数据并进行清洗和分析"
        ]
    
    async def run_demo(self):
        """运行演示"""
        print("🚀 动态工作流生成演示")
        print("=" * 50)
        
        for i, task in enumerate(self.demo_tasks, 1):
            print(f"\n📋 任务 {i}: {task}")
            print("-" * 30)
            
            try:
                # 生成工作流
                workflow = await self.workflow_generator.generate_workflow(task)
                
                # 显示结果
                print(f"✅ 工作流生成成功:")
                print(f"   🏷️  名称: {workflow.name}")
                print(f"   🔄 模式: {workflow.pattern.value}")
                print(f"   📊 复杂度: {workflow.complexity.value}")
                print(f"   🔗 节点数: {len(workflow.nodes)}")
                print(f"   ➡️  边数: {len(workflow.edges)}")
                print(f"   ⏱️  预估时间: {workflow.estimated_duration:.1f}秒")
                
                # 显示节点信息
                print(f"   📦 节点列表:")
                for node in workflow.nodes:
                    print(f"      • {node.name} ({node.node_type}) - {node.estimated_duration}s")
                
                # 显示优化提示
                if workflow.optimization_hints:
                    print(f"   💡 优化提示:")
                    for hint in workflow.optimization_hints:
                        print(f"      • {hint}")
                
                # 模拟执行
                await self._simulate_execution(workflow)
                
            except Exception as e:
                print(f"❌ 生成失败: {e}")
        
        print(f"\n🎉 演示完成！")
        print(f"✨ 展示了动态工作流生成的核心能力:")
        print(f"   • 自动任务分析和模式识别")
        print(f"   • 智能节点生成和依赖管理")
        print(f"   • 复杂度评估和优化建议")
        print(f"   • 多种工作流模式支持")
    
    async def _simulate_execution(self, workflow: GeneratedWorkflow):
        """模拟工作流执行"""
        print(f"   🚀 模拟执行:")
        
        executed_nodes = set()
        total_time = 0
        
        # 简单的拓扑排序执行
        while len(executed_nodes) < len(workflow.nodes):
            for node in workflow.nodes:
                if node.name not in executed_nodes:
                    # 检查依赖是否都已执行
                    if all(dep in executed_nodes for dep in node.dependencies):
                        print(f"      ⚡ 执行 {node.name}...")
                        await asyncio.sleep(0.1)  # 模拟执行时间
                        executed_nodes.add(node.name)
                        total_time += node.estimated_duration
                        break
        
        print(f"      ✅ 执行完成，总耗时: {total_time:.1f}秒")


async def main():
    """主函数"""
    demo = SimpleDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
