#!/usr/bin/env python3
"""
搜索功能完整使用示例
展示如何使用自进化智能体进行真实的网络搜索
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from src.core.agent import SelfEvolvingAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class SearchDemo:
    """搜索功能演示类"""
    
    def __init__(self):
        self.agent = None
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化智能体"""
        print("🚀 初始化搜索功能演示...")
        self.agent = SelfEvolvingAgent()
        self.agent.set_user_interaction_callback(self.handle_capability_request)
        print("✅ 智能体初始化完成\n")
        
    async def handle_capability_request(self, interaction_type: str, data: dict):
        """处理能力请求 - 提供真实的搜索API信息"""
        if interaction_type == "capability_request":
            capability = data.get("capability")
            print(f"\n🤖 智能体请求: {data.get('message')}")
            
            if capability == "web_search":
                # 提供DuckDuckGo搜索API信息
                api_info = {
                    "type": "rest",
                    "name": "DuckDuckGo Instant Answer API",
                    "base_url": "https://api.duckduckgo.com",
                    "description": "DuckDuckGo即时答案API，提供搜索结果和摘要信息",
                    "endpoints": {
                        "search": {
                            "method": "GET",
                            "path": "/",
                            "parameters": {
                                "q": "搜索查询词",
                                "format": "json",
                                "no_html": "1",
                                "skip_disambig": "1"
                            },
                            "response_format": {
                                "Abstract": "摘要信息",
                                "AbstractText": "摘要文本",
                                "AbstractURL": "摘要来源URL",
                                "RelatedTopics": "相关主题列表",
                                "Results": "搜索结果列表"
                            }
                        }
                    },
                    "example_usage": """
                    # 搜索示例
                    import aiohttp
                    
                    async with aiohttp.ClientSession() as session:
                        params = {
                            'q': 'artificial intelligence',
                            'format': 'json',
                            'no_html': '1',
                            'skip_disambig': '1'
                        }
                        async with session.get('https://api.duckduckgo.com/', params=params) as response:
                            data = await response.json()
                            return data
                    """,
                    "notes": [
                        "免费API，无需API密钥",
                        "支持多语言搜索",
                        "返回结构化的搜索结果",
                        "包含摘要、相关主题等信息"
                    ]
                }
                
                print(f"📡 提供 DuckDuckGo 搜索 API 信息")
                return api_info
                
        return None
        
    async def demo_basic_search(self):
        """演示基础搜索功能"""
        print("=" * 80)
        print("📋 演示 1: 基础搜索功能")
        print("=" * 80)
        
        search_queries = [
            "人工智能最新发展",
            "Python编程教程",
            "机器学习算法",
            "深度学习框架比较"
        ]
        
        for i, query in enumerate(search_queries, 1):
            print(f"\n🔍 搜索 {i}: {query}")
            print("-" * 50)
            
            try:
                result = await self.agent.execute_task(f"搜索关于'{query}'的信息")
                
                if result.get('status') == 'success':
                    print("✅ 搜索成功!")
                    search_result = result.get('result', {})
                    
                    # 显示搜索结果摘要
                    if isinstance(search_result, dict):
                        abstract = search_result.get('abstract', '')
                        if abstract:
                            print(f"📄 摘要: {abstract[:200]}...")
                        
                        # 显示相关主题
                        related = search_result.get('related_topics', [])
                        if related and len(related) > 0:
                            print(f"🔗 相关主题: {len(related)} 个")
                            for j, topic in enumerate(related[:3], 1):
                                if isinstance(topic, dict):
                                    topic_text = topic.get('Text', '')[:100]
                                    print(f"   {j}. {topic_text}...")
                    else:
                        print(f"📄 结果: {str(search_result)[:200]}...")
                        
                else:
                    print(f"❌ 搜索失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 搜索异常: {e}")
                
            await asyncio.sleep(1)  # 避免请求过于频繁
            
    async def demo_intelligent_search(self):
        """演示智能搜索功能"""
        print("\n" + "=" * 80)
        print("📋 演示 2: 智能搜索分析")
        print("=" * 80)
        
        complex_queries = [
            "比较TensorFlow和PyTorch的优缺点",
            "解释什么是Transformer架构",
            "推荐学习数据科学的资源",
            "分析当前AI技术发展趋势"
        ]
        
        for i, query in enumerate(complex_queries, 1):
            print(f"\n🧠 智能查询 {i}: {query}")
            print("-" * 60)
            
            try:
                # 使用更复杂的任务描述，测试LLM的理解能力
                task_description = f"请帮我搜索并分析：{query}。我需要详细的信息和见解。"
                result = await self.agent.execute_task(task_description)
                
                if result.get('status') == 'success':
                    print("✅ 智能搜索完成!")
                    
                    # 分析搜索结果
                    search_result = result.get('result', {})
                    print(f"📊 搜索结果类型: {type(search_result).__name__}")
                    
                    if isinstance(search_result, dict):
                        # 统计信息
                        keys = list(search_result.keys())
                        print(f"📋 结果包含字段: {', '.join(keys[:5])}...")
                        
                        # 显示关键信息
                        if 'query' in search_result:
                            print(f"🔍 实际查询: {search_result['query']}")
                        if 'abstract' in search_result:
                            abstract = search_result['abstract']
                            if abstract:
                                print(f"💡 核心信息: {abstract[:150]}...")
                                
                else:
                    print(f"❌ 智能搜索失败: {result.get('error')}")
                    
            except Exception as e:
                print(f"❌ 智能搜索异常: {e}")
                
            await asyncio.sleep(1)
            
    async def demo_search_learning(self):
        """演示搜索学习和优化"""
        print("\n" + "=" * 80)
        print("📋 演示 3: 搜索学习和优化")
        print("=" * 80)
        
        # 执行一系列相关搜索，观察系统学习效果
        learning_queries = [
            "机器学习基础概念",
            "监督学习算法",
            "无监督学习方法", 
            "强化学习原理",
            "深度学习网络结构"
        ]
        
        print("🔄 执行一系列相关搜索，观察学习效果...")
        execution_times = []
        
        for i, query in enumerate(learning_queries, 1):
            print(f"\n📚 学习查询 {i}: {query}")
            
            start_time = asyncio.get_event_loop().time()
            
            try:
                result = await self.agent.execute_task(f"搜索{query}的详细信息")
                
                end_time = asyncio.get_event_loop().time()
                execution_time = end_time - start_time
                execution_times.append(execution_time)
                
                if result.get('status') == 'success':
                    print(f"✅ 查询完成 (耗时: {execution_time:.2f}秒)")
                    
                    if i == 1:
                        print("💡 首次搜索: 生成web_search能力")
                    else:
                        print("⚡ 复用能力: 直接执行搜索")
                        
                else:
                    print(f"❌ 查询失败: {result.get('error')}")
                    
            except Exception as e:
                print(f"❌ 查询异常: {e}")
                execution_times.append(0)
                
        # 分析学习效果
        print(f"\n📈 学习效果分析:")
        if len(execution_times) > 1:
            first_time = execution_times[0]
            avg_later_time = sum(execution_times[1:]) / len(execution_times[1:]) if len(execution_times) > 1 else 0
            
            print(f"   首次搜索耗时: {first_time:.2f}秒 (包含能力生成)")
            print(f"   后续平均耗时: {avg_later_time:.2f}秒 (直接复用)")
            
            if first_time > 0 and avg_later_time > 0:
                speedup = first_time / avg_later_time
                print(f"   性能提升: {speedup:.1f}x 倍")
                
    async def show_search_capabilities(self):
        """展示搜索能力详情"""
        print("\n" + "=" * 80)
        print("🔧 搜索能力详情")
        print("=" * 80)
        
        capabilities = self.agent.capability_registry.list_capabilities()
        print(f"📦 当前系统能力: {capabilities}")
        
        if 'web_search' in capabilities:
            print("\n🔍 web_search 能力详情:")
            
            # 获取能力实例
            capability = self.agent.capability_registry.get_capability('web_search')
            if capability:
                print(f"   类名: {capability.__class__.__name__}")
                print(f"   模块: {capability.__class__.__module__}")
                
                # 列出可用方法
                methods = [method for method in dir(capability) 
                          if not method.startswith('_') and callable(getattr(capability, method))]
                print(f"   可用方法: {', '.join(methods)}")
                
                # 显示生成的代码文件
                capability_file = Path("src/capabilities/web_search.py")
                if capability_file.exists():
                    print(f"   代码文件: {capability_file}")
                    print(f"   文件大小: {capability_file.stat().st_size} 字节")
                    
        print("\n💡 搜索功能特点:")
        print("   - 🌐 使用DuckDuckGo API，免费无限制")
        print("   - 🧠 LLM智能理解搜索意图")
        print("   - ⚡ 异步处理，高性能")
        print("   - 📊 结构化结果，易于处理")
        print("   - 🔄 自动学习，持续优化")


async def main():
    """主函数"""
    demo = SearchDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        # 演示基础搜索
        await demo.demo_basic_search()
        
        # 演示智能搜索
        await demo.demo_intelligent_search()
        
        # 演示学习效果
        await demo.demo_search_learning()
        
        # 展示能力详情
        await demo.show_search_capabilities()
        
        print("\n" + "=" * 80)
        print("🎉 搜索功能演示完成!")
        print("💡 主要展示内容:")
        print("   ✅ 真实网络搜索功能")
        print("   ✅ LLM智能任务理解")
        print("   ✅ 动态能力生成")
        print("   ✅ 学习和性能优化")
        print("   ✅ 结构化结果处理")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 演示异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔍 自进化智能体搜索功能演示")
    print("展示真实的网络搜索能力和智能学习过程")
    print("=" * 80)
    
    asyncio.run(main())
