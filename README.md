# 自进化智能体 (Self-Evolving Agent)

一个能够在运行时自我扩展功能的智能体系统。当智能体发现自己缺少完成任务所需的能力时，它会主动请求用户提供相应的API，然后自动生成并集成新的功能模块。

## 核心特性

- **能力发现**: 智能体能够识别自身能力的缺失
- **动态扩展**: 基于API文档自动生成新功能
- **热更新**: 运行时动态加载新能力，无需重启
- **能力管理**: 完整的能力注册、版本控制和依赖管理
- **学习优化**: 基于历史数据优化能力使用策略

## 架构设计

```
自进化智能体
├── 核心引擎 (Core Engine)
│   ├── 任务执行器 (Task Executor)
│   ├── 能力发现器 (Capability Discoverer)
│   └── 决策引擎 (Decision Engine)
├── 能力管理系统 (Capability Management)
│   ├── 能力注册表 (Capability Registry)
│   ├── 版本管理器 (Version Manager)
│   └── 依赖解析器 (Dependency Resolver)
├── 代码生成器 (Code Generator)
│   ├── API解析器 (API Parser)
│   ├── 模板引擎 (Template Engine)
│   └── 代码集成器 (Code Integrator)
└── 学习系统 (Learning System)
    ├── 经验记录器 (Experience Recorder)
    ├── 模式识别器 (Pattern Recognizer)
    └── 策略优化器 (Strategy Optimizer)
```

## 工作流程

1. **任务接收**: 智能体接收用户任务
2. **能力评估**: 分析完成任务所需的能力
3. **缺失检测**: 识别当前缺失的能力
4. **API请求**: 向用户请求相应的API或工具
5. **代码生成**: 基于API文档生成功能模块
6. **能力集成**: 将新功能集成到系统中
7. **任务执行**: 使用新获得的能力完成任务
8. **经验学习**: 记录和优化执行过程

## 快速开始

### 方法1: 自动安装（推荐）

```bash
# 运行自动安装脚本
python install.py
```

### 方法2: 手动安装

```bash
# 安装核心依赖
pip install jinja2>=3.1.0 requests>=2.31.0 aiosqlite>=0.19.0 pyyaml>=6.0.0 watchdog>=3.0.0

# 可选：安装UI增强
pip install rich>=13.0.0 typer>=0.9.0

# 创建必要目录
mkdir -p data logs src/capabilities
```

### 运行演示

```bash
# 运行完整演示
python run_demo.py --mode demo

# 运行交互模式
python run_demo.py --mode interactive

# 运行测试
python run_demo.py --mode test
```

## 示例场景

智能体接收到"搜索最新的AI新闻"的任务，发现自己没有网络搜索能力，于是：

1. 告知用户缺少搜索功能
2. 用户提供搜索API文档
3. 智能体自动生成搜索模块
4. 集成新功能并完成任务
5. 下次遇到类似任务时直接使用已有能力

## 技术栈

- **Python 3.8+**: 主要开发语言
- **FastAPI**: API服务框架
- **SQLite**: 能力和经验数据存储
- **Jinja2**: 代码模板引擎
- **importlib**: 动态模块加载
- **AST**: 代码解析和生成

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！
